﻿using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata.Internal;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Serilog.Core;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Zendar.Data.Contexts;
using Zendar.Data.Enums.Integracao;
using Zendar.Data.Models.Aplicacao;
using Zendar.QueueService.Messages.BackgroundTasks;
using Zendar.Worker.Models;
using Zendar.Worker.Models.Emails;
using Zendar.Worker.Services.EmailService;
using ZendarPackage.NotaFiscal.Enums;

namespace Zendar.Worker.UseCases.LimparBancoDados
{
    public class LimparBancoDadosUseCase : ILimparBancoDadosUseCase
    {
        private readonly ILogger<LimparBancoDadosUseCase> _logger;
        private readonly AplicacaoContexto _contexto;
        private readonly IConfiguration _configuration;

        private readonly IEmailService _emailService;

        public LimparBancoDadosUseCase(
            ILogger<LimparBancoDadosUseCase> logger,
            AplicacaoContexto contexto,
            IConfiguration configuration,
            IEmailService emailService)
        {
            _logger = logger;
            _contexto = contexto;
            _emailService = emailService;
            _configuration = configuration;
        }

        public void Dispose()
        {
            _contexto?.Dispose();
        }

        public async Task ExecuteAsync(
            BackgroundTasksMessage message)
        {
            try
            {
                Guid correlationId = message.GetCorrelationId();

                _contexto.Database.OpenConnection();

                using var transaction = _contexto.Database.BeginTransaction();

                try
                {
                    var integracoesPorLoja = await ListarIntegracoesPorLoja();

                    await CleanDataBase(correlationId);

                    await AtivarUsuarios(
                        correlationId,
                        JsonConvert.DeserializeObject<IEnumerable<Guid>>(message.Dados));

                    transaction.Commit();

                    if (integracoesPorLoja?.Any() == true)
                    {
                        foreach (var integracaoLoja in integracoesPorLoja)
                        {
                            await ResetIntegracao(integracaoLoja.Key, integracaoLoja.Value, message);
                        }
                    }
                }
                catch
                {
                    transaction.Rollback();

                    throw;
                }

                _contexto.Database.CloseConnection();

                await SendSuccessEmailAsync(
                        sistema: message.Sistema,
                        dominio: message.Dominio,
                        email: message.Email);

                _logger.LogInformation(
                        "Correlation ID: {correlationId} | Limpeza de banco de dados concluída com sucesso.",
                        correlationId);
            }
            catch
            {
                await SendErrorEmailAsync(
                    sistema: message.Sistema,
                    dominio: message.Dominio,
                    email: message.Email);

                throw;
            }
        }

        #region [Fiscal]
        private async Task AlterarNumeracaoInicialDocumentoFiscal(
            Guid correlationId)
        {
            var lojas = await _contexto.Loja
                .Select(l => new
                {
                    Id = l.Id,
                    NumeroUltimaNFeEmitida = l.DocumentosFiscais
                    .Where(d => d.ModeloFiscal == ModeloFiscal.NFe &&
                                d.Status == StatusFiscal.AUTORIZADA &&
                                d.Serie == l.LojaFiscal.NFeNumeroSerie)
                    .Max(d => d.Numero),
                    NumeroUltimaNFCeEmitida = l.DocumentosFiscais
                    .Where(d => d.ModeloFiscal == ModeloFiscal.NFCe &&
                                d.Status == StatusFiscal.AUTORIZADA &&
                                d.Serie == l.LojaFiscal.NFCeNumeroSerie)
                    .Max(d => d.Numero)
                })
                .ToListAsync();

            if (lojas == null || !lojas.Any()) return;

            string sqlTemplate =
                    @"UPDATE
						[LojaFiscal]
					SET
						[NFeNumeracaoInicial] = {0},
						[NFCeNumeracaoInicial] = {1}
					WHERE
						[LojaId] = '{2}';";

            foreach (var loja in lojas)
            {
                string sql = string.Format(
                    sqlTemplate,
                    loja.NumeroUltimaNFeEmitida + 1,
                    loja.NumeroUltimaNFCeEmitida + 1,
                    loja.Id);

                await ExecuteSqlAsync(
                    _contexto.Database,
                    _logger,
                    sql,
                    correlationId);
            }
        }

        private async Task DesvincularDocumentoFiscalOperacao(
            Guid correlationId)
        {
            string sql =
                    @"UPDATE
						[DocumentoFiscal]
					SET
						[OperacaoId] = null";

            await ExecuteSqlAsync(
                _contexto.Database,
                _logger,
                sql,
                correlationId);
        }

		private async Task DesvincularEntradaMercadoriaItemDocumentoFiscalItem(
			Guid correlationId)
		{
			string sql =
					@"UPDATE
	                    [EntradaMercadoriaItem]
                    SET
	                    [DocumentoFiscalItemId] = null";

			await ExecuteSqlAsync(
				_contexto.Database,
				_logger,
				sql,
				correlationId);
		}

		private async Task ExcluirDocumentoFiscalSincronizado(
            Guid correlationId)
        {
            string excluirItensSql = @"
                DELETE
                    DFI
                FROM
                    DocumentoFiscalItem DFI
                INNER JOIN DocumentoFiscal DF ON DFI.DocumentoFiscalId = DF.Id
                WHERE NOT EXISTS (
                    SELECT 1
                    FROM DocumentoFiscalXml dXML
                    WHERE dXML.DocumentoFiscalId = DF.Id
                      AND dXML.ArquivoSincronizado = 0
                );";

            string excluirDocumentoSql = @"
                DELETE
                    DF
                FROM
                    DocumentoFiscal DF
                WHERE NOT EXISTS (
                    SELECT 1
                    FROM DocumentoFiscalXml dXML
                    WHERE dXML.DocumentoFiscalId = DF.Id
                      AND dXML.ArquivoSincronizado = 0
                );";

            await ExecuteSqlAsync(_contexto.Database, _logger, excluirItensSql, correlationId);
            await ExecuteSqlAsync(_contexto.Database, _logger, excluirDocumentoSql, correlationId);
        }
        #endregion

        #region [Operacao]
        private async Task ExcluirPedidoIntegracao(
            Guid correlationId)
        {
            string sql = @"
				DELETE FROM [IntegracaoPendenciaPedido];
				DELETE FROM [IntegracaoPedido];
			";

            await ExecuteSqlAsync(
                _contexto.Database,
                _logger,
                sql,
                correlationId);
        }

        private async Task ExcluirVoucher(
            Guid correlationId)
        {
            string sql = @"
				DELETE FROM [ValeMovimentacao];
				DELETE FROM [Vale];
			";

            await ExecuteSqlAsync(
                _contexto.Database,
                _logger,
                sql,
                correlationId);
        }

        private async Task ExcluirOperacao(
            Guid correlationId)
        {
            string sql = @"
				DELETE FROM [MovimentacaoFinanceiraBaixa];
				DELETE FROM [MovimentacaoFinanceira];
				DELETE FROM [OperacaoItemRemovido];
				DELETE FROM [OperacaoItem];
				DELETE FROM [Fatura];
				DELETE FROM [Vale];
				DELETE FROM [OperacaoTroca];
				DELETE FROM [OperacaoTransferencia];
				DELETE FROM [OperacaoFinanceira];
				DELETE FROM [TransacaoStone];
				DELETE FROM [Operacao];
			";

            await ExecuteSqlAsync(
                _contexto.Database,
                _logger,
                sql,
                correlationId);
        }

        private async Task RecalcularSequenciaOperacao(
            Guid correlationId)
        {
            string sql = @"
				EXEC [dbo].[PROC_RECALCULAR_SEQUENCIA_OPERACAO];
			";

            await ExecuteSqlAsync(
                _contexto.Database,
                _logger,
                sql,
                correlationId);
        }

        private async Task LimparContaFinanceira(
            Guid correlationId)
        {
            string sql = @"
				DELETE FROM [CaixaConferencia];
				DELETE FROM [CaixaMovimentacao];
				DELETE FROM [ContaFinanceiraSaldo];
				UPDATE
					[ContaFinanceira]
				SET
					[Saldo] = 0;
			";

            await ExecuteSqlAsync(
                _contexto.Database,
                _logger,
                sql,
                correlationId);
        }
        #endregion

        #region [Estoque]
        private async Task ExcluirConferenciaEstoque(
            Guid correlationId)
        {
            string sql = @"
				DELETE FROM [ConferenciaEstoqueImportacao];
				DELETE FROM [ConferenciaEstoqueFiltros];
				DELETE FROM [ConferenciaEstoqueItens];
				DELETE FROM [ConferenciaEstoque];
			";

            await ExecuteSqlAsync(
                _contexto.Database,
                _logger,
                sql,
                correlationId);
        }

        private async Task ExcluirEntradaMercadoria(
            Guid correlationId)
        {
            string sql = @"
				DELETE FROM [EntradaMercadoriaItem];
				DELETE FROM [EntradaMercadoria];
			";

            await ExecuteSqlAsync(
                _contexto.Database,
                _logger,
                sql,
                correlationId);
        }
        #endregion

        #region [Dashboard]
        private async Task LimparMetaComissao(
            Guid correlationId)
        {
            string sql = @"
				DELETE FROM [LojaFaixaMeta];
				DELETE FROM [LojaMetaComissao];
				DELETE FROM [LojaMetaComissaoPorDia];
				DELETE FROM [VendedorMeta];
				DELETE FROM [VendedorMetaComissaoPorMes];
			";

            await ExecuteSqlAsync(
                _contexto.Database,
                _logger,
                sql,
                correlationId);
        }

        private async Task LimparModuloDashboard(
            Guid correlationId)
        {
            string sql = @"
				DELETE FROM [ItensMaisVendidos];
				DELETE FROM [ItensMaisVendidosDetalhe];
				DELETE FROM [ProdutoEmEstoqueDashboard];
				DELETE FROM [ResumoContasPagaRecebida];
				DELETE FROM [ResumoContasPagarReceber];
				DELETE FROM [ResumoContasPagarReceberPorPlanoConta];
				DELETE FROM [ResumoContasPagarReceberPorData];
				DELETE FROM [TotalizadorVendasPorMes];
				DELETE FROM [VendasPorMesDetalhe];
				DELETE FROM [TotalizadorVendasPorDia];
				DELETE FROM [VendasPorDiaDetalhe];
			";

            await ExecuteSqlAsync(
                _contexto.Database,
                _logger,
                sql,
                correlationId);
        }

        private async Task LimparDashboardInicial(
            Guid correlationId)
        {
            string sql = @"
				DELETE FROM [DashboardInicialDetalhesPorDia];
				DELETE FROM [DashboardInicialDetalhesPorMes];
				DELETE FROM [DashboardInicialTotalizadoresMesAtual];
				DELETE FROM [DashboardInicialTotalizadoresPorMes];

				DECLARE
					@Loja_Id UNIQUEIDENTIFIER;
				BEGIN

					DECLARE lojas CURSOR FOR
					SELECT
						[Id]
					FROM
						[Loja];

					OPEN lojas;

					FETCH NEXT FROM lojas INTO @Loja_Id;

					WHILE @@FETCH_STATUS = 0
					BEGIN
						EXEC PREENCHER_DASHBOARD_NOVA_LOJA @Loja_Id;

						FETCH NEXT FROM lojas INTO @Loja_Id;
					END;

					CLOSE lojas;
					DEALLOCATE lojas;

				END;
			";

            await ExecuteSqlAsync(
                _contexto.Database,
                _logger,
                sql,
                correlationId);
        }
        #endregion

        #region [Cadastros]
        private async Task ExcluirCampoPersonalizado(
            Guid correlationId)
        {
            string sql = @"
				DELETE FROM [CampoPersonalizadoProduto];
				DELETE FROM [CampoPersonalizadoClienteFornecedor];
				DELETE FROM [CampoPersonalizado];
			";

            await ExecuteSqlAsync(
                _contexto.Database,
                _logger,
                sql,
                correlationId);
        }

        private async Task ExcluirProduto(
            Guid correlationId)
        {
            string sql = @"
				DELETE FROM [TabelaPrecoProdutoCorTamanho];
				DELETE FROM [TagProduto];
				DELETE FROM [Tag];
				DELETE FROM [ProdutoCorTamanhoKit];
                DELETE FROM [ProdutoFornecedor];
				DELETE
					P
				FROM
					[Produto] P
				WHERE
					NOT EXISTS (
						SELECT
							DI.[Id]
						FROM
							[DocumentoFiscalItem] DI
						INNER JOIN
							[ProdutoCorTamanho] PCT ON DI.[ProdutoCorTamanhoId] = PCT.[Id]
						INNER JOIN
							[ProdutoCor] PC ON PCT.[ProdutoCorId] = PC.[Id]
						WHERE
							PC.[ProdutoId] = P.[Id]
					);
			";

            await ExecuteSqlAsync(
                _contexto.Database,
                _logger,
                sql,
                correlationId);

            var resetIdentitySql = @"
                IF NOT EXISTS (SELECT 1 FROM [Produto])
                BEGIN
	                DBCC CHECKIDENT([Produto], RESEED, 0)
                END

                IF NOT EXISTS (SELECT 1 FROM [ProdutoCorTamanho])
                BEGIN
	                DBCC CHECKIDENT([ProdutoCorTamanho], RESEED, 0)
                END
            ";

            await ExecuteSqlAsync(
               _contexto.Database,
               _logger,
               resetIdentitySql,
               correlationId);
        }

        private async Task ExcluirCor(
            Guid correlationId)
        {
            string sql = @"
				DELETE
					C
				FROM
					[Cor] C
				WHERE
					C.[PadraoSistema]  = 0
				AND
					NOT EXISTS (
						SELECT
							PC.[Id]
						FROM
							[ProdutoCor] PC 
						WHERE
							C.[Id] = PC.[CorId]
					);
			";

            await ExecuteSqlAsync(
                _contexto.Database,
                _logger,
                sql,
                correlationId);
        }

        private async Task ExcluirTamanho(
            Guid correlationId)
        {
            string sql = @"
				DELETE
					T
				FROM
					[Tamanho] T
				WHERE
					T.[PadraoSistema]  = 0
				AND
					NOT EXISTS (
						SELECT
							PCT.[Id]
						FROM
							[ProdutoCorTamanho] PCT
						WHERE
							T.[Id] = PCT.[TamanhoId]
					);
			";

            await ExecuteSqlAsync(
                _contexto.Database,
                _logger,
                sql,
                correlationId);
        }

        private async Task ExcluirMarca(
            Guid correlationId)
        {
            string sql = @"
				DELETE
					M
				FROM
					[Marca] M
				WHERE
                    M.[PadraoSistema] = 0
					NOT EXISTS (
						SELECT
							P.[Id]
						FROM
							[Produto] P
						WHERE
							M.[Id] = P.[MarcaId]
					);
			";

            await ExecuteSqlAsync(
                _contexto.Database,
                _logger,
                sql,
                correlationId);
        }

        private async Task ExcluirCategoriaProduto(
            Guid correlationId)
        {
            string sqlPreCategoria = @"
                DELETE FROM [CategoriaProdutoComplemento];
                DELETE FROM [CategoriaProdutoObservacao];
                DELETE FROM [ProdutoObservacao];
            ";

			await ExecuteSqlAsync(
				_contexto.Database,
				_logger,
				sqlPreCategoria,
				correlationId);

			const int MAX_CATEGORY_LEVEL = 4;

            string sql = @"
                DELETE * FROM [CategoriaProdutoComplemento];
				DELETE 
					CP
				FROM
					[CategoriaProduto] CP
				WHERE
                    CP.[PadraoSistema] = 0 AND
					NOT EXISTS (
						SELECT
							P.[Id]
						FROM
							[Produto] P
						WHERE
							P.[CategoriaProdutoId] = CP.[Id]
					) AND
					NOT EXISTS (
						SELECT
							CPF.[Id]
						FROM
							[CategoriaProduto] CPF
						WHERE
							CPF.[CategoriaProdutoPaiId] = CP.[Id]
					);
			";

            for (var i = 0; i < MAX_CATEGORY_LEVEL; i++)
            {
                await ExecuteSqlAsync(
                    _contexto.Database,
                    _logger,
                    sql,
                    correlationId);
            }
        }

		private async Task ExcluirImpressorasEDepartamentos(
			Guid correlationId)
		{
			string sql = @"
                DELETE FROM [DepartamentoImpressora];
                DELETE FROM [DepartamentoContaBloqueada];
                DELETE FROM [DepartamentoProdutoFavorito];
                DELETE FROM [Departamento] WHERE [PadraoSistema] = 0;
                DELETE FROM [GerenciadorImpressaoImpressora];
                DELETE FROM [Impressora];
                DELETE FROM [GerenciadorImpressao];
            ";

			await ExecuteSqlAsync(
				_contexto.Database,
				_logger,
				sql,
				correlationId);
		}

		private async Task ExcluirClienteFornecedor(
            Guid correlationId)
        {
            string sql = @"
				DELETE 
					C
				FROM
					ClienteFornecedor C
				WHERE
					C.[PadraoSistema] = 0
					AND NOT EXISTS (
						SELECT
							DP.[Id]
						FROM
							[DocumentoFiscalParticipantes] DP
						WHERE 
							DP.[CodigoParticipante] = C.[Id]
					);
			";

            await ExecuteSqlAsync(
                _contexto.Database,
                _logger,
                sql,
                correlationId);
        }

        private async Task ExcluirTransportador(
            Guid correlationId)
        {
            string sql = @"
				DELETE 
					T
				FROM
					[Transportadora] T
				WHERE
					NOT EXISTS (
						SELECT
							DP.[Id]
						FROM
							[DocumentoFiscalParticipantes] DP
						WHERE 
							DP.[CodigoParticipante] = T.[Id]
					);
			";

            await ExecuteSqlAsync(
                _contexto.Database,
                _logger,
                sql,
                correlationId);
        }
        #endregion

        #region [Integracao]
        private async Task ExcluirDispositivo(
            Guid correlationId)
        {
            string sql = @"
                ALTER TABLE Dispositivo
                DROP CONSTRAINT FK_Dispositivo_ContaFinanceira_ContaFinanceiraId;

                ALTER TABLE Dispositivo
                WITH CHECK ADD CONSTRAINT FK_Dispositivo_ContaFinanceira_ContaFinanceiraId
                FOREIGN KEY (ContaFinanceiraId) REFERENCES ContaFinanceira(Id) ON DELETE CASCADE;

                DELETE 
	                C
                FROM
	                [ContaFinanceira] C
                WHERE
	                C.[TipoContaFinanceira] = 1
	                AND EXISTS (
                    SELECT
                        D.[Id]
                    FROM
                        [Dispositivo] D
	                WHERE
		                D.[ContaFinanceiraId] = C.[Id]
		                AND NOT EXISTS (
			                SELECT
				                O.[Id]
			                FROM
				                [Operacao] O
			                WHERE 
				                O.[DispositivoId] = D.[Id]
		                )
	                );

                ALTER TABLE Dispositivo
                DROP CONSTRAINT FK_Dispositivo_ContaFinanceira_ContaFinanceiraId;

                ALTER TABLE Dispositivo
                WITH CHECK ADD CONSTRAINT FK_Dispositivo_ContaFinanceira_ContaFinanceiraId
                FOREIGN KEY (ContaFinanceiraId) REFERENCES ContaFinanceira(Id) ON DELETE NO ACTION;
			";

            await ExecuteSqlAsync(
                _contexto.Database,
                _logger,
                sql,
                correlationId);
        }

        private async Task ExcluirIntegracao(
            Guid correlationId)
        {
            string sql = @"
				DELETE FROM [IntegracaoOrigemPedido];
				DELETE FROM [IntegracaoSituacaoPedido];
				DELETE FROM [IntegracaoLog];
                DELETE FROM [IntegracaoLogPlataforma];
				DELETE FROM [Integracao];
				DELETE FROM [ImportacaoCadastroIntegracao];
			";

            await ExecuteSqlAsync(
                _contexto.Database,
                _logger,
                sql,
                correlationId);
        }
        #endregion

        #region [Importacao]
        private async Task ExcluirImportacao(
            Guid correlationId)
        {
            string sql = @"
                DELETE FROM [ImportacaoCombinacaoCampos];
                DELETE FROM [Importacao];
            ";

            await ExecuteSqlAsync(
                _contexto.Database,
                _logger,
                sql,
                correlationId);
        }
        #endregion

        #region [Usuario]
        private async Task AtivarUsuarios(
            Guid correlationId,
            IEnumerable<Guid> usuarioIds)
        {
            var sql = new StringBuilder(@"
                UPDATE [AspNetUsers]
                SET [Ativo] = 1
                WHERE [Administrador] = 1");

            var parametros = new List<SqlParameter>();

            var idsArray = usuarioIds as Guid[] ?? usuarioIds.ToArray();
            int count = idsArray.Length;

            if (count > 0)
            {
                sql.Append(" OR [Id] IN (");

                for (int i = 0; i < count; ++i)
                {
                    if (i > 0)
                        sql.Append(", ");

                    sql.Append($"@p{i}");
                    parametros.Add(new($"@p{i}", idsArray[i]));
                }

                sql.Append(')');
            }

            sql.Append(';');

            await ExecuteSqlAsync(
                _contexto.Database,
                _logger,
                sql.ToString(),
                correlationId,
                parametros.ToArray());
        }
        #endregion

        private static async Task ExecuteSqlAsync(
            DatabaseFacade database,
            ILogger logger,
            string sql,
            Guid correlationId,
            SqlParameter[] parametros = null)
        {
            Guid sqlQueryId = Guid.NewGuid();

            logger.LogDebug(
                "Correlation ID: {correlationId} | SqlCommandId: {sqlQueryId} | Description: Executando comando SQL. | SqlCommand: {sql}",
                correlationId,
                sqlQueryId,
                sql);

            try
            {
                int affectedRows = await database.ExecuteSqlRawAsync(sql, parametros);

                if (affectedRows > 0)
                {
                    logger.LogDebug(
                        "Correlation ID: {correlationId} | SqlCommandId: {sqlQueryId} | Description: Comando SQL executado com êxito.",
                        correlationId,
                        sqlQueryId);
                }
                else
                {
                    logger.LogDebug(
                        "Correlation ID: {correlationId} | SqlCommandId: {sqlQueryId} | Description: Comando SQL executado sem falhas, mas nenhum registro foi afetado.",
                        correlationId,
                        sqlQueryId);
                }
            }
            catch
            {
                logger.LogDebug(
                "Correlation ID: {correlationId} | SqlCommandId: {sqlQueryId} | Description: Ocorreu um erro durante a execução do comando SQL.",
                correlationId,
                sqlQueryId);

                throw;
            }
        }

        private async Task CleanDataBase(
            Guid correlationId)
        {
            await AlterarNumeracaoInicialDocumentoFiscal(correlationId);

            await DesvincularDocumentoFiscalOperacao(correlationId);

            await DesvincularEntradaMercadoriaItemDocumentoFiscalItem(correlationId);

            await ExcluirDocumentoFiscalSincronizado(correlationId);

            await ExcluirConferenciaEstoque(correlationId);

            await ExcluirPedidoIntegracao(correlationId);

            await ExcluirVoucher(correlationId);

            await ExcluirOperacao(correlationId);

            await RecalcularSequenciaOperacao(correlationId);

            await ExcluirEntradaMercadoria(correlationId);

            await LimparContaFinanceira(correlationId);

            await LimparMetaComissao(correlationId);

            await LimparModuloDashboard(correlationId);

            await LimparDashboardInicial(correlationId);

            await ExcluirCampoPersonalizado(correlationId);

            await ExcluirProduto(correlationId);

            await ExcluirCor(correlationId);

            await ExcluirTamanho(correlationId);

            await ExcluirMarca(correlationId);

            await ExcluirCategoriaProduto(correlationId);

            await ExcluirImpressorasEDepartamentos(correlationId);

            await ExcluirClienteFornecedor(correlationId);

            await ExcluirTransportador(correlationId);

            await ExcluirDispositivo(correlationId);

            await ExcluirIntegracao(correlationId);

            await ExcluirImportacao(correlationId);
        }

        #region [Integracoes]
        private Expression<Func<Integracao, bool>> IntegracoesComSync =>
            x => x.IdentificacaoIntegracao != IdentificacaoIntegracao.TODAS &&
                x.IdentificacaoIntegracao != IdentificacaoIntegracao.CAIXA_MOVEL &&
                x.IdentificacaoIntegracao != IdentificacaoIntegracao.STI3PAY;

        private async Task<List<KeyValuePair<IdentificacaoIntegracao, Guid>>> ListarIntegracoesPorLoja()
            => await _contexto.Integracao
            .Where(IntegracoesComSync)
            .Select(x => new KeyValuePair<IdentificacaoIntegracao, Guid>(x.IdentificacaoIntegracao, x.LojaId))
            .ToListAsync();

        private async Task ResetIntegracao(
            IdentificacaoIntegracao integracao,
            Guid lojaId,
            BackgroundTasksMessage message)
        {
            try
            {
                using var content = new StringContent(
                string.Empty,
                System.Text.Encoding.UTF8,
                "application/json");

                switch (integracao)
                {
                    case IdentificacaoIntegracao.TRAY:
                        var trayUrl = _configuration["ZendarSyncApi:TrayUrl"];
                        await new HttpClient().PostAsync($"{trayUrl}/v1/store/quit/{lojaId}", content);
                        break;
                    case IdentificacaoIntegracao.CAIXA_PDV_AUTONOMO:
                        // Ainda não existe endpoint
                        break;
                    case IdentificacaoIntegracao.FRENTE_CAIXA:
                        // Ainda não existe endpoint
                        break;
                    default:
                        break;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex,
                        "Correlation ID: {correlationId} | Erro no reset da integração {Integracao} da loja {LojaId} do usuario {usuario}, do dominio {dominio}, com codigo da conta-empresa {codigoContaEmpresa}." +
                        " Ação: {acao} | LojaId: {lojaId} | UsuarioId: {usuarioId} | Dados: {dados}.",
                        message.GetCorrelationId(),
                        nameof(integracao),
                        lojaId,
                        $"{message.Nome} - {message.Email}",
                        message.Dominio,
                        message.CodigoContaEmpresa,
                        message.TaskType.ToString(),
                        message.LojaId,
                        message.UsuarioId,
                        message.Dados);
            }
        }
        #endregion

        #region [E-mail]
        private async Task SendSuccessEmailAsync(
            string sistema,
            string dominio,
            string email)
        {
            var emailLimpeza = new LimpezaBancoDadosEmail(
                sistema,
                dominio);

            await SendEmailAsync(emailLimpeza, email);
        }

        private async Task SendErrorEmailAsync(
            string sistema,
            string dominio,
            string email)
        {
            var emailLimpeza = new LimpezaBancoDadosErroEmail(
                sistema,
                dominio);

            await SendEmailAsync(emailLimpeza, email);
        }

        private async Task SendEmailAsync(
            IEmail email,
            string to)
        {
            var emailEnvio = new EmailEnvio()
            {
                Assunto = "Limpeza do banco de dados",
                Emails = new() { to },
                Sistema = email.Sistema
            };

            emailEnvio.Corpo = email.ToString();

            await _emailService.SendAsync(emailEnvio);
        }
        #endregion
    }
}
