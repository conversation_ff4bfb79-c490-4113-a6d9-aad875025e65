﻿using FluentValidation;
using System;
using System.Linq;
using Zendar.Business.ViewModels.V2.Promocao;
using Zendar.Data.Resources.Validacoes;

namespace Zendar.Business.Validations.V2.ViewModels
{
	public class PromocaoViewModelValidation : AbstractValidator<PromocaoViewModel>
	{
		public PromocaoViewModelValidation()
		{
			RuleFor(p => p.Nome)
				.NotNull()
					.WithMessage(ResourceValidacao.CampoObrigatorio)
				.NotEmpty()
					.WithMessage(ResourceValidacao.CampoObrigatorio)
				.MaximumLength(50)
					.WithMessage(ResourceValidacao.TamanhoMaximo);

			RuleFor(p => p.PeriodoVigencia)
				.NotNull()
					.WithMessage(ResourceValidacao.CampoObrigatorio)
				.Must(v => v.PeriodoFim > v.PeriodoInicio)
					.WithMessage("A data de fim deve ser maior que a de inicio.")
				.Must(v => v.PeriodoInicio > DateTime.MinValue)
					.WithMessage("O data de inicio está inválida.")
				.Must(v => v.PeriodoFim < DateTime.MaxValue)
					.WithMessage("O data de fim está inválida.");

			RuleFor(p => p.HorarioVigencia)
				.NotNull()
					.WithMessage(ResourceValidacao.CampoObrigatorio)
				.Must(v => v.PeriodoInicio.Date == v.PeriodoFim.Date)
					.WithMessage("As datas do horário de vigência devem ser iguais.")
				.Must(v => v.PeriodoInicio.TimeOfDay != v.PeriodoFim.TimeOfDay)
					.WithMessage("A duração deve ser maior que 0 minutos.");

			RuleFor(p => p.DiasDaSemana)
				.NotEmpty()
					.WithMessage("É necessário selecionar ao menos um dia da semana.")
				.Must(p => p.All(d => Enum.IsDefined(d)))
					.WithMessage("A informação dos dias da semana informados está incorreta.");

			RuleFor(p => p.LojasId)
				.NotEmpty()
					.WithMessage("É necessário escolher ao menos uma loja.")
				.Must(lojaId => lojaId.All(id => id != Guid.Empty && id != default))
					.WithMessage("A informação das lojas selecionadas está incorreta.");
		}
	}
}
