﻿using Multiempresa.Shared.Helpers.Convertores;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Zendar.Business.Emails;
using Zendar.Business.Helpers;
using Zendar.Business.Helpers.ImpressaoRelatorioPdf;
using Zendar.Business.Interfaces;
using Zendar.Business.Interfaces.Services;
using Zendar.Business.Interfaces.Services.OperacaoServices;
using Zendar.Business.Services.Financeiro.ContaFinanceiraServices;
using Zendar.Business.Services.Financeiro.MovimentacaoFinanceiraBaixaServices.MovimentacaoFinanceiraBaixaCadastroService;
using Zendar.Business.Services.Financeiro.MovimentacaoFinanceiraBaixaServices.MovimentacaoFinanceiraBaixaCancelarService;
using Zendar.Business.Services.Financeiro.MovimentacaoFinanceiraBaixaServices.MovimentacaoFinanceiraBaixaConciliacaoService;
using Zendar.Business.Services.Financeiro.MovimentacaoFinanceiraBaixaServices.MovimentacaoFinanceiraBaixaObterService;
using Zendar.Business.Services.Financeiro.MovimentacaoFinanceiraServices.MovimentacaoFinanceiraObterServices;
using Zendar.Business.Services.LojaServices.LojaMultaJurosService.LojaMultaJurosObterService;
using Zendar.Business.ViewModels.Relatorios;
using Zendar.Data.Enums;
using Zendar.Data.Helpers;
using Zendar.Data.Interfaces.Aplicacao;
using Zendar.Data.Models.Aplicacao;
using Zendar.Data.Resources.Mensagens;
using Zendar.Data.ViewModels;
using Zendar.Data.ViewModels.Loja;

namespace Zendar.Business.Services.Financeiro.ContasReceberServices
{
    public class ContasReceberService : BaseService, IContasReceberService
    {
        private readonly IAspNetUserInfo _aspNetUserInfo;
        private readonly IStorageService _storageService;
        private readonly IEnvioEmailService _envioEmailService;
        private readonly ILojaRepository _lojaRepository;

        private readonly IMovimentacaoFinanceiraObterService _movimentacaoFinanceiraObterService;
        private readonly IMovimentacaoFinanceiraBaixaObterService _movimentacaoFinanceiraBaixaObterService;
        private readonly IMovimentacaoFinanceiraBaixaCadastroService _movimentacaoFinanceiraBaixaCadastroService;
        private readonly IMovimentacaoFinanceiraBaixaCancelarService _movimentacaoFinanceiraBaixaCancelarService;
        private readonly IMovimentacaoFinanceiraBaixaConciliacaoService _movimentacaoFinanceiraBaixaConciliacaoService;
        private readonly IOperacaoService _operacaoService;
        private readonly ILojaMultaJurosObterService _lojaMultaJurosObterService;
        private readonly IMultaJurosCalculoService _multaJurosCalculoService;
        private readonly IContaFinanceiraService _contaFinanceiraService;
        private readonly IFormaPagamentoRecebimentoParcelaService _formaPagamentoRecebimentoParcelaService;
        private readonly ILogErroService _logErroService;
        private readonly IPadronizacaoService _padronizacaoService;
        public ContasReceberService(INotificador notificador,
                          IContaFinanceiraService contaFinanceiraService,
                          IMovimentacaoFinanceiraBaixaCadastroService movimentacaoFinanceiraBaixaCadastroService,
                          IAspNetUserInfo aspNetUserInfo,
                          IStorageService storageService,
                          ILojaRepository lojaRepository,
                          IEnvioEmailService envioEmailService,
                          IMovimentacaoFinanceiraBaixaCancelarService movimentacaoFinanceiraBaixaCancelarService,
                          IMovimentacaoFinanceiraBaixaObterService movimentacaoFinanceiraBaixaObterService,
                          IOperacaoService operacaoService,
                          ILojaMultaJurosObterService lojaMultaJurosObterService,
                          IMultaJurosCalculoService multaJurosCalculoService,
                          IMovimentacaoFinanceiraObterService movimentacaoFinanceiraObterService,
                          IFormaPagamentoRecebimentoParcelaService formaPagamentoRecebimentoParcelaService,
                          ILogErroService logErroService,
                          IPadronizacaoService padronizacaoService,
                          IMovimentacaoFinanceiraBaixaConciliacaoService movimentacaoFinanceiraBaixaConciliacaoService) : base(notificador)
        {
            _contaFinanceiraService = contaFinanceiraService;
            _aspNetUserInfo = aspNetUserInfo;
            _storageService = storageService;
            _lojaRepository = lojaRepository;
            _envioEmailService = envioEmailService;
            _movimentacaoFinanceiraBaixaCadastroService = movimentacaoFinanceiraBaixaCadastroService;
            _movimentacaoFinanceiraBaixaCancelarService = movimentacaoFinanceiraBaixaCancelarService;
            _movimentacaoFinanceiraBaixaObterService = movimentacaoFinanceiraBaixaObterService;
            _operacaoService = operacaoService;
            _movimentacaoFinanceiraObterService = movimentacaoFinanceiraObterService;
            _lojaMultaJurosObterService = lojaMultaJurosObterService;
            _multaJurosCalculoService = multaJurosCalculoService;
            _formaPagamentoRecebimentoParcelaService = formaPagamentoRecebimentoParcelaService;
            _logErroService = logErroService;
            _padronizacaoService = padronizacaoService;
            _movimentacaoFinanceiraBaixaConciliacaoService = movimentacaoFinanceiraBaixaConciliacaoService;
        }

        public async Task<TotalizadoresContasReceberViewModel> ObterTotalizadoresListaPaginada(ContasReceberFiltroViewModel contasReceberFiltroViewModel)
        {
            var contas = await _movimentacaoFinanceiraObterService.ObterContasReceberParaTotalizadoresDaListaPaginada(contasReceberFiltroViewModel);

            contas = await CalcularValorDasContas(contas, contasReceberFiltroViewModel.Situacao);

            return new TotalizadoresContasReceberViewModel()
            {
                QuantidadeContas = contas.Count,
                ValorTotalDescontos = contas.Sum(c => c.Desconto),
                ValorTotalMultasJuros = contas.Sum(c => c.Multa + c.Juros),
                ValorTotalReceber = contas.Sum(c => c.Valor + c.Multa + c.Juros),
            };
        }

        public async Task<GridPaginadaRetornoContasReceber<ContasReceberPaginadoViewModel>> ListarPaginado(GridPaginadaConsulta gridPaginada, ContasReceberFiltroViewModel contasReceberFiltroViewModel, bool? trazerTodos = false)
        {
            var retorno = _movimentacaoFinanceiraObterService.ListarPaginadoContasReceber(gridPaginada, contasReceberFiltroViewModel);

            retorno.Registros = await CalcularValorDasContas(retorno.Registros, contasReceberFiltroViewModel.Situacao);

            if (trazerTodos.HasValue && trazerTodos.Equals(false))
                retorno.Registros = retorno.Registros.Skip(gridPaginada.Skip).Take(gridPaginada.TamanhoPagina).ToList();

            return retorno;
        }

        public GridPaginadaRetornoContasReceber<ContasReceberMovimentacaoBaixaConciliarViewModel> ObterParaConciliarPaginado(GridPaginadaConsulta gridPaginada, ContasReceberBaixaConciliarFiltroViewModel contasReceberFiltroViewModel, bool? isRelatorio = false)
        {
            return _movimentacaoFinanceiraBaixaConciliacaoService.ObterParaConciliarPaginado(gridPaginada, contasReceberFiltroViewModel, _aspNetUserInfo.LojaId.Value, isRelatorio);
        }

        public async Task AtualizarConciliacao(IEnumerable<ConciliacaoAtualizarViewModel> conciliacaoAtualizarViewModel)
        {
            await _movimentacaoFinanceiraBaixaConciliacaoService.AtualizarConciliacao(conciliacaoAtualizarViewModel);
        }

        private async Task<List<ContasReceberPaginadoViewModel>> CalcularValorDasContas(List<ContasReceberPaginadoViewModel> contas, SituacaoBaixa situacao)
        {
            var lojaMultaJuros = await _lojaMultaJurosObterService.ObterConfiguracoesMultaJurosPorLoja(_aspNetUserInfo.LojaId.Value);

            foreach (var item in contas)
            {
                var valorPago = item.MovimentacaoFinanceiraBaixa.Sum(x => x.Valor + x.Desconto - x.Multa - x.Juros);

                item.Valor = item.ValorOriginal;

                if (item.Valor is not 0 && valorPago is not 0 && item.Valor > valorPago)
                {
                    item.PagamentoParcial = true;
                }

                if (valorPago == item.Valor)
                {
                    item.Multa = item.MovimentacaoFinanceiraBaixa.Sum(x => x.Multa);
                    item.Juros = item.MovimentacaoFinanceiraBaixa.Sum(x => x.Juros);
                    item.Desconto = item.MovimentacaoFinanceiraBaixa.Sum(x => x.Desconto);
                }
                else if (valorPago != item.Valor)
                {
                    if (situacao == SituacaoBaixa.CONTA_EM_ABERTO)
                        item.Valor = item.Valor > valorPago ? item.Valor - valorPago : item.Valor;
                    else if (situacao == SituacaoBaixa.CONTA_BAIXADA)
                        item.Valor = valorPago;
                    else if (situacao == SituacaoBaixa.TODAS)
                    {
                        item.Valor = item.ValorOriginal;
                    }

                    var multaJuros = _multaJurosCalculoService.CalcularMultaJuros(item.DataVencimento, item.Valor, item.Valor, lojaMultaJuros);
                    item.Multa = multaJuros.ValorMulta;
                    item.Juros = multaJuros.ValorJuros;
                }
            }

            return contas;
        }

        public async Task<ContasReceberDetalhesViewModel> ObterDetalhes(Guid movimentacaoFinanceiraId)
        {
            return await _movimentacaoFinanceiraObterService.ObterDetalhesContaReceber(movimentacaoFinanceiraId);
        }

        public async Task BaixarContas(List<ContasReceberBaixarContaViewModel> contasBaixar)
        {
            var lojaMultaJuros = await _lojaMultaJurosObterService.ObterConfiguracoesMultaJurosPorLoja(_aspNetUserInfo.LojaId.Value);

            bool multaJurosConfigurada = lojaMultaJuros.ValorJuros > 0 || lojaMultaJuros.ValorMulta > 0;

            foreach (var conta in contasBaixar)
            {
                var contaPagar = await _movimentacaoFinanceiraObterService.ObterParaLancarMovimentacaoBaixa(conta.MovimentacaoFinanceiraId);

                if (contaPagar == null)
                {
                    NotificarAviso(ResourceMensagem.ContasPagarService_NaoEncontrado);
                    return;
                }

                await CadastrarBaixa(conta, contaPagar, lojaMultaJuros, multaJurosConfigurada);
            }
        }

        private async Task CadastrarBaixa(ContasReceberBaixarContaViewModel contasReceberBaixar, MovimentacaoFinanceira contaReceber,
                                                   LojaMultaJurosViewModel lojaMultaJuros, bool multaJurosConfigurada)
        {
            if (contasReceberBaixar.ContaFinanceiraId.HasValue && !await _contaFinanceiraService.ValidarParaBaixarConta(contasReceberBaixar.ContaFinanceiraId.Value, _aspNetUserInfo.LojaId.Value, Guid.Parse(_aspNetUserInfo.Id)))
            {
                NotificarAviso(ResourceMensagem.MovimentacaoFinanceiraBaixaService_CadastrarBaixaCaixaFechado);
                return;
            }

            var valorTotalAReceber = contaReceber.Valor + contasReceberBaixar.Juros + contasReceberBaixar.Multa - contasReceberBaixar.Desconto;

            if (contasReceberBaixar.BaixaParcial)
            {
                if (!contasReceberBaixar.ZerarMultaJuros && multaJurosConfigurada)
                {
                    (decimal valorMulta, decimal valorJuros) = CalcularMultaJurosProporcional(lojaMultaJuros, contasReceberBaixar.Multa,
                                                                                                         contasReceberBaixar.Juros, contasReceberBaixar.Valor,
                                                                                                         contaReceber.Valor, contaReceber.DataVencimento);

                    contasReceberBaixar.Multa = valorMulta;
                    contasReceberBaixar.Juros = valorJuros;
                }

                valorTotalAReceber = contasReceberBaixar.Valor;
            }

            if (contasReceberBaixar.QtdParcela == 0)
                contasReceberBaixar.QtdParcela = 1;

            var valorParcela = Math.Round(valorTotalAReceber / contasReceberBaixar.QtdParcela, 2);
            var valorParcelaMulta = Math.Round(contasReceberBaixar.Multa / contasReceberBaixar.QtdParcela, 2);
            var valorParcelaJuros = Math.Round(contasReceberBaixar.Juros / contasReceberBaixar.QtdParcela, 2);
            var valorParcelaDesconto = Math.Round(contasReceberBaixar.Desconto / contasReceberBaixar.QtdParcela, 2);

            var intervaloDias = _formaPagamentoRecebimentoParcelaService.ObterIntervaloDiasDaParcela(contasReceberBaixar.FormaRecebimentoParcelaId);
            DateTime dataCompensacao = new DateTime(contasReceberBaixar.DataPagamento.Year, contasReceberBaixar.DataPagamento.Month, contasReceberBaixar.DataPagamento.Day).AddHours(-_aspNetUserInfo.TimezoneOffset.Value);

            for (var i = 0; i < contasReceberBaixar.QtdParcela; i++)
            {
                dataCompensacao = dataCompensacao.AddDays(intervaloDias);

                var movimentacaoFinanceiraBaixa = new MovimentacaoFinanceiraBaixaCadastrarViewModel
                {
                    DataPagamento = contasReceberBaixar.DataPagamento,
                    FormaPagamentoRecebimentoId = contasReceberBaixar.FormaRecebimentoId,
                    FormaPagamentoRecebimentoParcelaId = contasReceberBaixar.FormaRecebimentoParcelaId,
                    Desconto = valorParcelaDesconto + (i == 0 ? Rateio.DiferencaRateio(contasReceberBaixar.Desconto, valorParcelaDesconto * contasReceberBaixar.QtdParcela) : 0),
                    MovimentacaoFinanceiraId = contasReceberBaixar.MovimentacaoFinanceiraId,
                    Multa = valorParcelaMulta + (i == 0 ? Rateio.DiferencaRateio(contasReceberBaixar.Multa, valorParcelaMulta * contasReceberBaixar.QtdParcela) : 0),
                    Juros = valorParcelaJuros + (i == 0 ? Rateio.DiferencaRateio(contasReceberBaixar.Juros, valorParcelaJuros * contasReceberBaixar.QtdParcela) : 0),
                    Valor = valorParcela + (i == 0 ? Rateio.DiferencaRateio(valorTotalAReceber, valorParcela * contasReceberBaixar.QtdParcela) : 0),
                    DataCompensacao = dataCompensacao,
                    ContaFinanceiraId = contasReceberBaixar.ContaFinanceiraId,
                    ValeId = contasReceberBaixar.ValeId,
                };

                await _movimentacaoFinanceiraBaixaCadastroService.CadastrarBaixaPorMovimentacaoFinanceira(movimentacaoFinanceiraBaixa);
            }
        }

        private (decimal valorMulta, decimal valorJuros) CalcularMultaJurosProporcional(LojaMultaJurosViewModel lojaMultaJuros, decimal multa, decimal juros,
                                                                                              decimal valorBaixa, decimal valorTotal, DateTime dataVencimento)
        {
            decimal proporcaoMulta = multa / valorTotal;
            decimal proporcaoJuros = juros / valorTotal;
            decimal multiplicador = 1 + proporcaoMulta + proporcaoJuros;

            valorBaixa = Math.Round(valorBaixa / multiplicador, 2);

            var multaJuros = _multaJurosCalculoService.CalcularMultaJuros(dataVencimento, valorBaixa, valorTotal, lojaMultaJuros);

            return (multaJuros.ValorMulta, multaJuros.ValorJuros);
        }

        public async Task<List<Guid>> BaixarContasCliente(ContasReceberBaixarContaClienteViewModel contasReceberBaixar)
        {
            //Busco as contas em aberto desse cliente
            var contas = await ObterContasAberto(new ContasReceberFiltroViewModel { ClienteFornecedorId = contasReceberBaixar.ClienteFornecedorId });

            //Faco um select nas contas preenchendo a viewModel para baixar contas
            var contasPagar = new List<ContasReceberBaixarContaViewModel>();

            contas.MovimentacaoFinanceira
            .ToList().ForEach(x =>
            {
                if (contasReceberBaixar.ZerarMultaJuros)
                {
                    x.Juros = 0;
                    x.Multa = 0;

                    // valor original desconsidera multa e juros
                    x.ValorTotal = x.ValorOriginal;
                }

                //Caso o valor informado for maior que o total da conta, pego o valor total, caso não, pego o valor informado
                var valorPagar = contasReceberBaixar.ValorInformado > x.ValorTotal ? x.ValorTotal : contasReceberBaixar.ValorInformado;

                //Crio a VM para baixar a conta
                var retorno = new ContasReceberBaixarContaViewModel
                {
                    Juros = x.Juros,
                    Multa = x.Multa,
                    Valor = valorPagar,
                    DataPagamento = DateTime.UtcNow,
                    MovimentacaoFinanceiraId = x.MovimentacaoFinanceiraId,
                    FormaRecebimentoId = contasReceberBaixar.FormaRecebimentoId,
                    FormaRecebimentoParcelaId = contasReceberBaixar.FormaRecebimentoParcelaId,
                    ContaFinanceiraId = contasReceberBaixar.ContaFinanceiraId,
                    QtdParcela = contasReceberBaixar.QtdParcelas,
                    ZerarMultaJuros = contasReceberBaixar.ZerarMultaJuros,
                    BaixaParcial = valorPagar != x.ValorTotal
                };

                contasPagar.Add(retorno);

                //Subtraio no valor informado o valor a pagar
                contasReceberBaixar.ValorInformado -= valorPagar;
            });

            //Filtro somente as contas que tem valor a pagar
            contasPagar = contasPagar.Where(x => x.Valor > 0).ToList();

            await BaixarContas(contasPagar);

            return contasPagar.Select(x => x.MovimentacaoFinanceiraId).Distinct().ToList();
        }

        public async Task ExcluirBaixasConta(MovimentacaoFinanceiraBaixaCancelarBaixaViewModel MovimentacaoFinanceiraBaixaCancelarBaixaViewModel)
        {
            // Validar se a operacao possui alguma devolucao
            var possuiDevolucao = await _movimentacaoFinanceiraObterService.VerificarSeOperacaoPossuiDevolucaoPorMovimentacaoFinanceira(MovimentacaoFinanceiraBaixaCancelarBaixaViewModel.MovimentacoesFinanceiras);

            if (possuiDevolucao)
            {
                NotificarAviso(ResourceMensagem.ContaReceberService_CancelarBaixaOperacaoComDevoucao);
                return;
            }

            foreach (var movimentacao in MovimentacaoFinanceiraBaixaCancelarBaixaViewModel.MovimentacoesFinanceiras)
            {
                // Excluir as baixas
                await _movimentacaoFinanceiraBaixaCancelarService.CancelarBaixaPorMovimentacaoFinanceira(movimentacao);
            }
        }

        public async Task ExcluirContas(ContasPagarReceberExcluirMovimentacaoViewModel contasReceberExcluirMovimentacaoViewModel)
        {
            foreach (var operacao in contasReceberExcluirMovimentacaoViewModel.Operacoes.Distinct())
            {
                //Deleto as movimentacoes e operação a partir do seu id
                await _operacaoService.ExcluirOperacaoDeLancamentoFinanceiro(operacao);
            }
        }

        public async Task<ContasReceberClienteFornecedorViewModel> ObterContasAberto(ContasReceberFiltroViewModel contasFiltro)
        {
            // Grid de paginação da consulta
            var gridPaginada = new GridPaginadaConsulta
            {
                PaginaAtual = 0,
                TamanhoPagina = int.MaxValue,
                CampoOrdenacao = "DataVencimento",
                DirecaoOrdenacao = "asc"
            };
            contasFiltro.Situacao = SituacaoBaixa.CONTA_EM_ABERTO;

            var contas = await ListarPaginado(gridPaginada, contasFiltro);

            if (contas != null && !contas.Registros.Any()) return new ContasReceberClienteFornecedorViewModel();

            var clienteFornecedorId = contasFiltro.ClienteFornecedorId.HasValue ?
                                      contasFiltro.ClienteFornecedorId.Value :
                                      contas.Registros.First().ClienteFornecedorId.Value;

            var contasReceber = await _movimentacaoFinanceiraBaixaObterService.ObterUltimaContaPaga(clienteFornecedorId, _aspNetUserInfo.LojaId.Value);
            if (contasReceber == null)
                contasReceber = new ContasReceberClienteFornecedorViewModel();

            contasReceber.MovimentacaoFinanceira = contas.Registros.Select(c => new ContasReceberClienteFornecedorMovimentacaoViewModel
            {
                MovimentacaoFinanceiraId = c.MovimentacaoFinanceiraId,
                DataVencimento = c.DataVencimento,
                NumeroOperacao = c.NumeroOperacao,
                OperacaoId = c.OperacaoId,
                Juros = c.Juros,
                Multa = c.Multa,
                Parcela = c.Parcela,
                ValorOriginal = c.Valor,
                ClienteFornecedorNome = !string.IsNullOrEmpty(c.ClienteFornecedorNome) ? c.ClienteFornecedorNome : c.ClienteFornecedorRazaoSocial,
                ClienteFornecedorId = (Guid)c.ClienteFornecedorId,
                ValorTotal = c.Valor + c.Multa + c.Juros
            }).ToList();

            return contasReceber;
        }

        public async Task<ReciboImpressaoViewModel> ObterParaImprimir(List<Guid> idContas)
        {
            var reciboVm = new ReciboImpressaoViewModel
            {
                Movimentacoes = new List<ContasReceberDetalhesViewModel>()
            };

            foreach (var idConta in idContas)
            {
                var conta = await ObterDetalhes(idConta);

                if (conta == null)
                {
                    NotificarAviso(ResourceMensagem.ContasPagarService_NaoEncontrado);
                    return null;
                }

                reciboVm.Movimentacoes.Add(conta);
            }

            var lojaCliente = await _movimentacaoFinanceiraObterService.ObterInformacoesDoCabecalhoDoRecibo(idContas.First());

            reciboVm.Emissao = DateTime.UtcNow;
            reciboVm.Cliente = reciboVm.Movimentacoes.FirstOrDefault()?.ClienteFornecedorRazaoSocial;
            reciboVm.TipoSistema = _aspNetUserInfo.Sistema;
            reciboVm.CasasDecimais = await _padronizacaoService.ObterCasasDecimais();
            reciboVm.Loja = new LojaImpressaoViewModel
            {
                Fantasia = lojaCliente.Operacao.Loja.Fantasia,
                RazaoSocial = lojaCliente.Operacao.Loja.RazaoSocial,
                CpfCnpj = lojaCliente.Operacao.Loja.CpfCnpj,
                Logradouro = lojaCliente.Operacao.Loja.Logradouro,
                Numero = lojaCliente.Operacao.Loja.Numero,
                Bairro = lojaCliente.Operacao.Loja.Bairro,
                Cep = lojaCliente.Operacao.Loja.Cep,
                Telefone = lojaCliente.Operacao.Loja.Telefone,
                CidadeUf = lojaCliente.Operacao.Loja.Cidade.CidadeUf,
                LogoQuadrado = lojaCliente.Operacao.Loja.LojaImpressaoRelatorio.LogoQuadrado,
            };

            return reciboVm;
        }

        public async Task<byte[]> ImpressaoReciboA4(List<Guid> idContas)
        {
            // Recupero a view model
            var reciboVm = await ObterParaImprimir(idContas);

            // Retorno o byte[] para a controller
            return new ImpressaoRecibo(reciboVm).ToArray();
        }

        public async Task EnviarEmail(List<Guid> idContas, List<string> emails)
        {
            // Recupero a view model com os dados
            var reciboVm = await ObterParaImprimir(idContas);

            StringBuilder formas = new();

            foreach (var movimentacao in reciboVm.Movimentacoes)
            {
                foreach (var baixa in movimentacao.MovimentacaoFinanceiraBaixa)
                {
                    if (!formas.ToString().Contains(baixa.FormaPagamento))
                    {
                        if (formas.Length > 0)
                            formas.Append(", ");

                        formas.Append(baixa.FormaPagamento);
                    }
                }
            }

            _envioEmailService.EnviarEmailGenericoComAnexo(new ImpressaoRecibo(reciboVm).ToArray(),
                                          "Recibo",
                                          EmailComprovanteRecibo.MontarEmail(reciboVm.Movimentacoes.Sum(x => x.ValorRecebido), reciboVm.Emissao, formas.ToString(), reciboVm.Loja.Fantasia, _aspNetUserInfo.Sistema.ObterDescricao()),
                                          "Comprovante de recebimento",
                                          emails.ToList(),
                                          reciboVm.Loja.Fantasia);
        }

        public async Task<byte[]> RelatorioListagem(ContasReceberFiltroViewModel filtros)
        {
            // Tratamento dos valores de datas não nulos
            filtros.DataVencimentoInicio = filtros.DataVencimentoInicio != new DateTime() ? filtros.DataVencimentoInicio : DateTime.MinValue;
            filtros.DataVencimentoFim = filtros.DataVencimentoFim != new DateTime() ? filtros.DataVencimentoFim : DateTime.MaxValue;

            // Grid de paginação da consulta
            var gridPaginada = new GridPaginadaConsulta
            {
                PaginaAtual = 0,
                TamanhoPagina = int.MaxValue,
                CampoOrdenacao = "DataVencimento",
                DirecaoOrdenacao = "asc"
            };

            // Realizo a consulta de todas as contas a receber
            var gridVm = await ListarPaginado(gridPaginada, filtros, true);

            var totalizadores = await ObterTotalizadoresListaPaginada(filtros);

            if (gridVm.Registros.Count == 0)
            {
                NotificarAviso(ResourceMensagem.NenhumRegistroEncontrado);
                return null;
            }

            // Recupero o nome fantasia da loja do usuário logado
            var loja = await _lojaRepository.FirstOrDefaultAsNoTracking(l => l.Id == _aspNetUserInfo.LojaId.Value, l => new Loja
            {
                Fantasia = l.Fantasia
            });

            // Preencho a view model do relatório
            var listagemVm = new ContasReceberRelatorioListagemViewModel
            {
                TipoSistema = _aspNetUserInfo.Sistema,
                LojaFantasia = loja.Fantasia,
                Emissao = DateTime.UtcNow,
                Filtros = filtros,
                Totalizadores = totalizadores,
                ContasReceber = gridVm
            };

            ConverterDate.AplicarUTC(_aspNetUserInfo.TimezoneOffset.Value, listagemVm);

            return new ImpressaoRelatorioListagemContasReceber(listagemVm).ToArray();
        }

        public async Task<byte[]> RelatorioListagemConciliacao(ContasReceberBaixaConciliarFiltroViewModel filtros)
        {
            // Tratamento dos valores de datas não nulos
            filtros.DataVencimentoInicio = filtros.DataVencimentoInicio != new DateTime() ? filtros.DataVencimentoInicio : DateTime.MinValue;
            filtros.DataVencimentoFim = filtros.DataVencimentoFim != new DateTime() ? filtros.DataVencimentoFim : DateTime.MaxValue;

            // Realizo a consulta de todas as contas a receber
            var listagemFiltrada = ObterParaConciliarPaginado(new GridPaginadaConsulta
            {
                PaginaAtual = 0,
                TamanhoPagina = int.MaxValue,
                CampoOrdenacao = "DataCompensacao",
                DirecaoOrdenacao = "asc"
            }, filtros, true);

            if (!listagemFiltrada.Registros.Any())
            {
                NotificarAviso(ResourceMensagem.NenhumRegistroEncontrado);
                return null;
            }

            var loja = await _lojaRepository.FirstOrDefaultAsNoTracking(l => l.Id == _aspNetUserInfo.LojaId.Value, l => new Loja { Fantasia = l.Fantasia });

            // Preencho a view model do relatório
            var listagemVm = new ConciliacaoRelatorioListagemViewModel
            {
                TipoSistema = _aspNetUserInfo.Sistema,
                LojaFantasia = loja.Fantasia,
                Emissao = DateTime.UtcNow,
                Filtros = filtros,
                ContasReceber = listagemFiltrada
            };

            ConverterDate.AplicarUTC(_aspNetUserInfo.TimezoneOffset.Value, listagemVm);

            return new ImpressaoRelatorioListagemConciliacao(listagemVm).ToArray();
        }

        public async Task EnviarEmailRelatorioListagem(ContasReceberFiltroViewModel contasReceberFiltroViewModel, List<string> emails)
        {
            var relatorio = await RelatorioListagem(contasReceberFiltroViewModel);
            if (relatorio == null) return;

            await _envioEmailService.EnviarEmailRelatorioListagem(relatorio, "ListaContasAReceber", "contas a receber", "Lista de contas a receber", emails);
        }

        public async Task EnviarEmailRelatorioListagemConciliacao(ContasReceberBaixaConciliarFiltroViewModel contasReceberFiltroViewModel, List<string> emails)
        {
            var relatorio = await RelatorioListagemConciliacao(contasReceberFiltroViewModel);
            if (relatorio == null) return;

            await _envioEmailService.EnviarEmailRelatorioListagem(relatorio, "ListaContasConciliacao", "conciliação de contas", "Lista de Conciliação de Contas", emails);
        }

        public void Dispose()
        {
            _lojaRepository?.Dispose();
            _logErroService?.Dispose();
        }
    }
}
