import {
  Text,
  But<PERSON>,
  I<PERSON>,
  <PERSON>lex,
  <PERSON><PERSON><PERSON><PERSON>nt,
  Drawer<PERSON>eader,
  Drawer<PERSON><PERSON>,
  Divider,
  DrawerFooter,
  Box,
  Tooltip,
} from '@chakra-ui/react';
import { PropsWithChildren } from 'react';
import { FormProvider, UseFormReturn } from 'react-hook-form';
import { FiXCircle } from 'react-icons/fi';
import { LuPanelRightClose } from 'react-icons/lu';

import DrawerPadraoChakra from 'components/PDV/Drawer/DrawerPadraoChakra';
import { SimpleGridForm } from 'components/update/Form/SimpleGridForm';

import { LupaIcon } from 'icons';

type DrawerConsultaProdutosProps = PropsWithChildren & {
  isOpen: boolean;
  isLoading: boolean;
  isLoadingPesquisa: boolean;
  isLargerThan900: boolean;
  isLargerThan1200: boolean;
  formMethods: UseFormReturn<any>;
  onClose: () => void;
  handleLimparPesquisa: () => void;
  handleSubmit: () => Promise<void>;
};

export const DrawerConsultaProdutos = ({
  isOpen,
  isLoading,
  isLoadingPesquisa,
  isLargerThan900,
  isLargerThan1200,
  formMethods,
  onClose,
  handleLimparPesquisa,
  handleSubmit,
  children,
}: DrawerConsultaProdutosProps) => {
  return (
    <DrawerPadraoChakra
      isOverlay={false}
      size={isLargerThan900 ? (isLargerThan1200 ? 'md' : 'sm') : 'full'}
      onClose={onClose}
      isOpen={isOpen && !isLoading}
      closeOnOverlayClick={false}
      blockScrollOnMount={false}
      placement="right"
      variant="clickThrough"
    >
      <DrawerContent bg="gray.50" pr="5px" overflow="scroll" height="full">
        <DrawerHeader pb={0}>
          <Flex align="center">
            <Text color="primary.50" fontSize="md" w="full">
              Filtros
            </Text>
            <Tooltip label="Fechar filtros" placement="left">
              <Box>
                <Icon
                  as={LuPanelRightClose}
                  fontSize="18px"
                  color="black"
                  transition="all 300ms"
                  onClick={onClose}
                  role="button"
                  onKeyDown={(event) => {
                    if (event.key === 'Enter') {
                      onClose();
                    }
                  }}
                  tabIndex={0}
                  cursor="pointer"
                />
              </Box>
            </Tooltip>
          </Flex>

          <Text color="gray.700" fontSize="xs">
            Utilize os filtros de busca para uma pesquisa específica. Todos os
            campos são acumulativos.
          </Text>
          <Divider size="1px" mt="20px" bgColor="gray.600" />
        </DrawerHeader>
        <DrawerBody py="16px" height="fit-content">
          <FormProvider {...formMethods}>
            <SimpleGridForm gap="24px">{children}</SimpleGridForm>
          </FormProvider>
        </DrawerBody>
        <DrawerFooter
          bg="gray.50"
          padding="0px"
          zIndex="9999"
          width="calc(100% - 48px)"
          height="fit-content"
          marginX="auto"
          borderTop="1px solid #572ABC"
        >
          <Flex
            justifyContent={['start', 'start', 'center']}
            direction={['column-reverse', 'column-reverse', 'row']}
            w="full"
            pt="16px"
            pb="12px"
          >
            <Button
              w={['full', 'full', '150px']}
              variant="outlineDefault"
              colorScheme="gray"
              fontSize={['16px', '16px', '14px']}
              height={['40px', '40px', '32px']}
              isDisabled={isLoading || isLoadingPesquisa}
              onClick={handleLimparPesquisa}
              leftIcon={<Icon as={FiXCircle} />}
              borderRadius="full"
              mt={['10px', '10px', '0']}
            >
              Limpar filtros
            </Button>
            <Button
              ml={['0', '0', '24px']}
              w={['full', 'full', '190px']}
              height={['40px', '40px', '32px']}
              variant="solid"
              colorScheme="blue"
              isDisabled={isLoading || isLoadingPesquisa}
              onClick={handleSubmit}
              onKeyDown={(event) => {
                if (event.key === 'Enter') {
                  handleSubmit();
                }
              }}
              leftIcon={<Icon as={LupaIcon} />}
              borderRadius="full"
            >
              Pesquisar
            </Button>
          </Flex>
        </DrawerFooter>
      </DrawerContent>
    </DrawerPadraoChakra>
  );
};
