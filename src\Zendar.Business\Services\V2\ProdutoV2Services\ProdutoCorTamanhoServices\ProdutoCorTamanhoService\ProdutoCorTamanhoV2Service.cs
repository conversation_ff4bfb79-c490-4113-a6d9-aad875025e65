﻿using Microsoft.EntityFrameworkCore;
using Multiempresa.Shared.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Zendar.Business.Application.Commands.ProdutoCommands.ProdutoCorTamanhoEstoqueCommands;
using Zendar.Business.AutoMappers.ProdutoMapper;
using Zendar.Business.Interfaces;
using Zendar.Business.Interfaces.Services;
using Zendar.Business.Services.PesquisaProdutoServices.Popular;
using Zendar.Business.Services.V1.IntegracaoServices.SincronizacaoServices.Trigger;
using Zendar.Business.Services.V2.FichaTecnicaV2Services.RemoverProdutoFichaTecnicaV2Service;
using Zendar.Business.Services.V2.ProdutoV2Services.ProdutoVariacaoService;
using Zendar.Business.Services.V2.ProdutoV2Services.ProdutoCorTamanhoEstoqueService;
using Zendar.Business.ViewModels.V2.ProdutoViewModels;
using Zendar.Data.Enums;
using Zendar.Data.Enums.Integracao;
using Zendar.Data.Helpers;
using Zendar.Data.Interfaces.Aplicacao;
using Zendar.Data.Mediator;
using Zendar.Data.Models.Aplicacao;
using Zendar.Data.ViewModels;

namespace Zendar.Business.Services.V2.ProdutoV2Services.ProdutoCorTamanhoServices.ProdutoCorTamanhoService
{
    public class ProdutoCorTamanhoV2Service : BaseService, IProdutoCorTamanhoV2Service
    {
        private readonly IMediatorHandler _mediator;
        private readonly IAspNetUserInfo _aspNetUserInfo;
        private readonly IStorageService _storageService;
        private readonly IProdutoCorTamanhoRepository _produtoCorTamanhoRepository;
        private readonly IProdutoCorTamanhoEstoqueRepository _produtoCorTamanhoEstoqueRepository;
        private readonly IProdutoCorTamanhoKitRepository _produtoCorTamanhoKitRepository;
        private readonly ILocalEstoqueRepository _localEstoqueRepository;
        private readonly IZendarTriggerService _zendarTriggerService;
        private readonly IPopularPesquisaProdutoService _popularPesquisaProdutoService;
        private readonly IRemoverProdutoFichaTecnicaV2Service _removerProdutoFichaTecnicaV2Service;
        private readonly IProdutoVariacaoV2Service _produtoVariacaoV2Service;
        private readonly IProdutoCorTamanhoEstoqueV2Serivce _produtoCorTamanhoEstoqueV2Service;

        public ProdutoCorTamanhoV2Service(
            INotificador notificador,
            IMediatorHandler mediator,
            IAspNetUserInfo aspNetUserInfo,
            IStorageService storageService,
            IProdutoCorTamanhoRepository produtoCorTamanhoRepository,
            IProdutoCorTamanhoEstoqueRepository produtoCorTamanhoEstoqueRepository,
            IProdutoCorTamanhoKitRepository produtoCorTamanhoKitRepository,
            ILocalEstoqueRepository localEstoqueRepository,
            IZendarTriggerService zendarTriggerService,
            IPopularPesquisaProdutoService popularPesquisaProdutoService,
            IRemoverProdutoFichaTecnicaV2Service removerProdutoFichaTecnicaV2Service,
            IProdutoVariacaoV2Service produtoVariacaoV2Service,
            IProdutoCorTamanhoEstoqueV2Serivce produtoCorTamanhoEstoqueV2Service)
            : base(notificador)
        {
            _mediator = mediator;
            _aspNetUserInfo = aspNetUserInfo;
            _storageService = storageService;
            _produtoCorTamanhoRepository = produtoCorTamanhoRepository;
            _produtoCorTamanhoEstoqueRepository = produtoCorTamanhoEstoqueRepository;
            _localEstoqueRepository = localEstoqueRepository;
            _zendarTriggerService = zendarTriggerService;
            _produtoCorTamanhoKitRepository = produtoCorTamanhoKitRepository;
            _popularPesquisaProdutoService = popularPesquisaProdutoService;
            _removerProdutoFichaTecnicaV2Service = removerProdutoFichaTecnicaV2Service;
            _produtoVariacaoV2Service = produtoVariacaoV2Service;
            _produtoCorTamanhoEstoqueV2Service = produtoCorTamanhoEstoqueV2Service;
        }

        public void Dispose()
        {
            _produtoCorTamanhoRepository?.Dispose();
            _produtoCorTamanhoEstoqueRepository?.Dispose();
            _localEstoqueRepository?.Dispose();
            _produtoCorTamanhoKitRepository?.Dispose();
            _popularPesquisaProdutoService?.Dispose();
            _removerProdutoFichaTecnicaV2Service?.Dispose();
            _produtoCorTamanhoEstoqueV2Service?.Dispose();
        }

        public ProdutoCorTamanhoV2ViewModel ObterPorProdutoCorIdETamanhoId(Guid produtoCorId, Guid tamanhoId)
        {
            var produtoCorTamanho =
                _produtoCorTamanhoRepository.Where(c => c.ProdutoCorId == produtoCorId &&
                                                        c.TamanhoId == tamanhoId)
                                            .Select(c => c)
                                            .FirstOrDefault();

            return produtoCorTamanho?.ToViewModel();
        }

        public GridPaginadaRetorno<ItemGradeVariacaoV2ViewModel> ObterGradePaginado(
            GridPaginadaConsulta gridConsulta,
            Guid produtoId,
            StatusConsulta status,
            Guid lojaId)
        {
            GridPaginadaRetorno<ItemGradeVariacaoV2ViewModel> gridRetorno = new();

            var queryProdutoCorTamanho =
                _produtoCorTamanhoRepository.Where(
                    pct => pct.ProdutoCor.ProdutoId == produtoId &&
                           !(pct.ProdutoCor.Cor.PadraoSistema && pct.Tamanho.PadraoSistema));

            queryProdutoCorTamanho = queryProdutoCorTamanho
                        .Where(pct => (!pct.ProdutoCor.Produto.ProdutoCores.Skip(1).Any() || !pct.ProdutoCor.Cor.PadraoSistema)
                                   && (!pct.ProdutoCor.ProdutoCorTamanhos.Skip(1).Any() || !pct.Tamanho.PadraoSistema));

            switch (status)
            {
                case StatusConsulta.Ativos:
                    queryProdutoCorTamanho = queryProdutoCorTamanho.Where(pct => pct.Ativo);
                    break;

                case StatusConsulta.Inativos:
                    queryProdutoCorTamanho = queryProdutoCorTamanho.Where(pct => !pct.Ativo);
                    break;
            }

            var produtoCorTamanho = queryProdutoCorTamanho
                .Select(pct => new ItemGradeVariacaoV2ViewModel()
                {
                    Ativo = pct.Ativo,
                    ProdutoCorTamanho = new ProdutoCorTamanhoDetalhadoV2ViewModel
                    {
                        ProdutoCorTamanhoId = pct.Id,
                        Tamanho = pct.Tamanho.PadraoSistema
                                ? string.Empty
                                : pct.Tamanho.Descricao,
                        Cor = pct.ProdutoCor.Cor.PadraoSistema
                            ? string.Empty
                            : pct.ProdutoCor.Cor.Descricao,
                        Imagem = pct.ProdutoCor.Cor.Imagem
                    },
                    Identificadores = new ProdutoCorTamanhoIdentificadoresV2ViewModel
                    {
                        CodigoBarrasInterno = pct.CodigoBarrasInterno,
                        CodigoExterno = pct.CodigoExterno,
                        CodigoGTINEAN = pct.CodigoGTINEAN,
                        SKU = pct.SKU,
                    },
                    Estoque = pct.ProdutoCorTamanhoEstoques
                                    .Where(e => e.LocalEstoque.LojaId == lojaId)
                                    .Sum(e => e.EstoqueAtual),
                    PrecoVendaExclusivo = pct.TabelaPrecoProdutoCorTamanhos.Where(tp => tp.TabelaPreco.PadraoSistema)
                                                                            .Select(tp => tp.PrecoVenda)
                                                                            .FirstOrDefault(),
                    PrecoVendaPadrao = pct.ProdutoCor.Produto.ProdutoPrecoLojas.FirstOrDefault(pl => pl.LojaId == lojaId).PrecoVenda

                });

            gridRetorno.CarregarPaginacao(produtoCorTamanho, gridConsulta);

            foreach (var registro in gridRetorno.Registros)
            {
                if (!string.IsNullOrEmpty(registro?.ProdutoCorTamanho?.Imagem))
                {
                    registro.ProdutoCorTamanho.Imagem =
                        _storageService.ObterUrlArquivoAcessoTemporario(StorageContaArmazenamento.Imagens,
                                                                        registro?.ProdutoCorTamanho?.Imagem);
                }
            }

            return gridRetorno;
        }

        public async Task<List<ListaProdutoCorTamanhoViewModel>> ObterListaProdutoCorTamanhoPorProdutoId(
            Guid produtoId)
        {
            var lista = new List<ListaProdutoCorTamanhoViewModel>();

            var listaProdutoCorTamanho =
                await _produtoCorTamanhoRepository.ObterProdutoCorTamanhoPorProdutoId(produtoId,
                                                                                      _aspNetUserInfo.LojaId.Value);

            if (listaProdutoCorTamanho != null)
            {
                lista =
                    listaProdutoCorTamanho
                    .Select(item => new ListaProdutoCorTamanhoViewModel
                    {
                        Id = item.Id,
                        Cor = item.ProdutoCor.Cor.Descricao,
                        Tamanho = item.Tamanho.Descricao
                    })
                    .ToList();
            }

            return lista;
        }

        public async Task<IEnumerable<ProdutoCorTamanhoDetalhadoV2ViewModel>> ListarPorProduto(
            Guid produtoId)
        {
            var produtoCorTamanho =
                await _produtoCorTamanhoRepository.FindAllSelectAsNoTracking(produtoCorTamanho => produtoCorTamanho.ProdutoCor.ProdutoId == produtoId &&
                                                                                                  produtoCorTamanho.Ativo,
                                                                             produtoCorTamanho => new ProdutoCorTamanho
                                                                             {
                                                                                 Id = produtoCorTamanho.Id,
                                                                                 Tamanho = new Tamanho
                                                                                 {
                                                                                     Descricao = produtoCorTamanho.Tamanho.Descricao,
                                                                                     DescricaoEcommerce = produtoCorTamanho.Tamanho.DescricaoEcommerce,
                                                                                     PadraoSistema = produtoCorTamanho.Tamanho.PadraoSistema
                                                                                 },
                                                                                 ProdutoCor = new ProdutoCor
                                                                                 {
                                                                                     Cor = new Cor
                                                                                     {
                                                                                         Descricao = produtoCorTamanho.ProdutoCor.Cor.Descricao,
                                                                                         DescricaoEcommerce = produtoCorTamanho.ProdutoCor.Cor.DescricaoEcommerce,
                                                                                         PadraoSistema = produtoCorTamanho.ProdutoCor.Cor.PadraoSistema
                                                                                     }
                                                                                 }
                                                                             });

            if (!produtoCorTamanho.Any())
            {
                NotificarAvisoRegistroNaoEncontrada("variação");

                return Enumerable.Empty<ProdutoCorTamanhoDetalhadoV2ViewModel>();
            }

            return produtoCorTamanho.Select(pct => new ProdutoCorTamanhoDetalhadoV2ViewModel
            {
                ProdutoCorTamanhoId = pct.Id,
                Tamanho = pct.ObterTamanhoDescricao(),
                Cor = pct.ProdutoCor?.ObterCorDescricao()
            });
        }

        public async Task<ProdutoCorTamanhoV2ViewModel> Obter(
            Guid produtoCorTamanhoId,
            Guid lojaId)
        {
            var produtoCorTamanho =
                await _produtoCorTamanhoRepository.FirstOrDefaultAsNoTracking(pct => pct.Id == produtoCorTamanhoId,
                                                                              pct => new ProdutoCorTamanho
                                                                              {
                                                                                  Id = pct.Id,
                                                                                  CodigoBarrasInterno = pct.CodigoBarrasInterno,
                                                                                  CodigoExterno = pct.CodigoExterno,
                                                                                  CodigoGTINEAN = pct.CodigoGTINEAN,
                                                                                  SKU = pct.SKU,
                                                                                  Altura = pct.Altura,
                                                                                  Largura = pct.Largura,
                                                                                  Profundidade = pct.Profundidade,
                                                                                  PesoLiquido = pct.PesoLiquido,
                                                                                  PesoBruto = pct.PesoBruto,
                                                                                  PesoEmbalagem = pct.PesoEmbalagem,
                                                                                  EstoqueMinimo = pct.EstoqueMinimo,
                                                                                  Ativo = pct.Ativo,
                                                                                  Tamanho = new Tamanho
                                                                                  {
                                                                                      Descricao = pct.Tamanho.Descricao,
                                                                                      DescricaoEcommerce = pct.Tamanho.DescricaoEcommerce,
                                                                                      PadraoSistema = pct.Tamanho.PadraoSistema
                                                                                  },
                                                                                  ProdutoCor = new ProdutoCor
                                                                                  {
                                                                                      Cor = new Cor
                                                                                      {
                                                                                          Descricao = pct.ProdutoCor.Cor.Descricao,
                                                                                          DescricaoEcommerce = pct.ProdutoCor.Cor.DescricaoEcommerce,
                                                                                          PadraoSistema = pct.ProdutoCor.Cor.PadraoSistema,
                                                                                          Imagem = pct.ProdutoCor.Cor.Imagem
                                                                                      }
                                                                                  },
                                                                                  ProdutoCorTamanhoEstoques = pct.ProdutoCorTamanhoEstoques
                                                                                                                 .Where(e => e.LocalEstoque.LojaId == lojaId)
                                                                                                                 .ToList()
                                                                              });

            if (produtoCorTamanho == null)
            {
                NotificarAvisoRegistroNaoEncontrada("variação");

                return null;
            }

            var produtoCorTamanhoVm =
                produtoCorTamanho.ToViewModel();

            if (!string.IsNullOrEmpty(produtoCorTamanhoVm.ProdutoCorTamanho?.Imagem))
            {
                produtoCorTamanhoVm.ProdutoCorTamanho.Imagem = _storageService.ObterUrlArquivoAcessoTemporario(StorageContaArmazenamento.Imagens,
                                                                                                               produtoCorTamanhoVm.ProdutoCorTamanho?.Imagem);
            }

            return produtoCorTamanhoVm;
        }

        /// <summary>
        /// Esse método não disponibiliza o produto para pesquisa.
        /// </summary>
        public async Task<IEnumerable<Guid>> Cadastrar(
            ProdutoCorTamanhoV2ViewModel[] produtoCorTamanhosVm)
        {
            #region [Recuperar existentes]

            var produtosCores = produtoCorTamanhosVm.Select(pct => pct.ProdutoCorId).Distinct();

            IEnumerable<ProdutoCorTamanho> produtoCorTamanhosJaExistentes =
                await _produtoCorTamanhoRepository.FindAllSelectAsNoTracking(pct => produtosCores.Contains(pct.ProdutoCorId),
                                                                             pct => new ProdutoCorTamanho
                                                                             {
                                                                                 Id = pct.Id,
                                                                                 ProdutoCorId = pct.ProdutoCorId,
                                                                                 TamanhoId = pct.TamanhoId,
                                                                                 Tamanho = new Tamanho { PadraoSistema = pct.Tamanho.PadraoSistema }
                                                                             });

            #endregion

            #region [Cadastro]

            List<ProdutoCorTamanho> produtoCorTamanhos =
                produtoCorTamanhosVm.Where(pct => !produtoCorTamanhosJaExistentes.Any(t => pct.TamanhoId == t.TamanhoId &&
                                                                                           pct.ProdutoCorId == t.ProdutoCorId))
                                    .Select(pct => pct.ToModel())
                                    .ToList();

            if (produtoCorTamanhosVm.Any(pct => produtoCorTamanhosJaExistentes.Any(t => pct.TamanhoId == t.TamanhoId &&
                                                                                        pct.ProdutoCorId == t.ProdutoCorId &&
                                                                                        !t.Tamanho.PadraoSistema)))
            {
                NotificarAviso("Um ou mais tamanhos já estavam cadastrados no produto.");
            }

            await _produtoCorTamanhoRepository.InsertRange(produtoCorTamanhos);

            #endregion

            var produtoId =
                _produtoCorTamanhoRepository.Where(x => x.Id == produtoCorTamanhos[0].Id)
                                            .Select(x => x.ProdutoCor.ProdutoId)
                                            .First();

            #region Trigger

            await _zendarTriggerService.ExecuteGuid(produtoId,
                                                TabelaTrigger.PRODUTO,
                                                OperacaoTrigger.CADASTRAR);

            #endregion

            #region [Iniciar Estoque]

            var locaisEstoque = await _localEstoqueRepository.FindAllSelectAsNoTracking(
                x => new LocalEstoque { Id = x.Id });

            List<ProdutoCorTamanhoEstoque> produtoEstoques = new();

            foreach (var produtoCorTamanho in produtoCorTamanhos)
            {
                produtoEstoques.AddRange(locaisEstoque
                .Select(localEstoque => new ProdutoCorTamanhoEstoque
                {
                    ProdutoCorTamanhoId = produtoCorTamanho.Id,
                    LocalEstoqueId = localEstoque.Id,
                    EstoqueAtual = 0
                })
                .ToList());
            }

            if (produtoEstoques.Count > 0)
                await _produtoCorTamanhoEstoqueRepository.InsertRange(produtoEstoques);

            #endregion

            #region [Estoque inicial]

            foreach (var produtoCorTamanhoVm in produtoCorTamanhosVm)
            {
                var tamanhoAdicionado =
                    produtoCorTamanhos.FirstOrDefault(pct => pct.TamanhoId == produtoCorTamanhoVm.TamanhoId &&
                                                             pct.ProdutoCorId == produtoCorTamanhoVm.ProdutoCorId);

                if (tamanhoAdicionado != null)
                    produtoCorTamanhoVm.Id = tamanhoAdicionado.Id;
            }

            var produtoCorTamanhoEstoqueInicial =
                produtoCorTamanhosVm.Where(pct => pct.EstoqueAtual > 0 &&
                                                  pct.Id != default)
                                    .Select(pct => (pct.Id, pct.EstoqueAtual, pct.LocalEstoqueId))
                                    .ToArray();

            await _mediator.EnviarComando(
                new AtualizarEstoqueVariacoesCommand(
                    produtoId,
                    _aspNetUserInfo.LojaId.Value,
                    Guid.Parse(_aspNetUserInfo.Id),
                    produtoCorTamanhoEstoqueInicial));

            #endregion

            return produtoCorTamanhos.Select(pct => pct.Id)
                                     .Union(produtoCorTamanhosJaExistentes.Select(pcte => pcte.Id));
        }

        public async Task Alterar(
            Guid produtoId,
            ProdutoCorTamanhoV2ViewModel produtoCorTamanhoVm)
        {
            await _produtoVariacaoV2Service.AlterarVariacao(produtoId, produtoCorTamanhoVm.Id, produtoCorTamanhoVm);

            if (produtoCorTamanhoVm.EstoqueAtual == 0)
                return;

            var estoqueAtualBanco = await _produtoCorTamanhoEstoqueV2Service.ObterEstoqueAtual(produtoCorTamanhoVm.Id, _aspNetUserInfo.LojaId.Value);

            if (estoqueAtualBanco != produtoCorTamanhoVm.EstoqueAtual)
            {
                if (await _produtoCorTamanhoRepository.VariacaoFoiMovimentada(produtoCorTamanhoVm.Id))
                {
                    NotificarAviso("Não é possível alterar o estoque novamente, pois já foi informado.");
                    return;
                }
            }

            var produtoCorTamanhoEstoque = new[] { (produtoCorTamanhoVm.Id, produtoCorTamanhoVm.EstoqueAtual, produtoCorTamanhoVm.LocalEstoqueId) };

            await _mediator.EnviarComando(
                new AtualizarEstoqueVariacoesCommand(
                    produtoId,
                    _aspNetUserInfo.LojaId.Value,
                    Guid.Parse(_aspNetUserInfo.Id),
                    produtoCorTamanhoEstoque));
        }

        public async Task Alterar(
            Guid produtoId,
            ProdutoCorTamanhoV2ViewModel[] produtoCorTamanhosVm)
        {
            foreach (var produtoCorTamanhoVm in produtoCorTamanhosVm)
            {
                await _produtoVariacaoV2Service.AlterarVariacao(produtoId, produtoCorTamanhoVm.Id, produtoCorTamanhoVm);

                if (PossuiAvisos() || PossuiErros()) return;
            }

            var produtoCorTamanhoEstoques =
                produtoCorTamanhosVm.Where(pct => pct.EstoqueAtual > 0)
                                    .Select(pct => (pct.Id, pct.EstoqueAtual, pct.LocalEstoqueId))
                                    .ToArray();

            await _mediator.EnviarComando(
                new AtualizarEstoqueVariacoesCommand(
                    produtoId,
                    _aspNetUserInfo.LojaId.Value,
                    Guid.Parse(_aspNetUserInfo.Id),
                    produtoCorTamanhoEstoques));
        }

        public async Task Remover(
            Guid produtoId,
            Guid tamanhoId)
        {
            var produtoCorTamanhos =
                await _produtoCorTamanhoRepository.Where(pct => pct.TamanhoId == tamanhoId &&
                                                                pct.ProdutoCor.ProdutoId == produtoId)
                                                  .ToListAsync();

            if (produtoCorTamanhos.Count == 0)
                return;

            await _removerProdutoFichaTecnicaV2Service.RemoverTamanho(produtoId, tamanhoId);

            await _produtoCorTamanhoRepository.DeleteRange(produtoCorTamanhos);

            #region Trigger

            foreach (var produtoCorTamanho in produtoCorTamanhos)
            {
                await _zendarTriggerService.ExecuteGuid(produtoCorTamanho.Id,
                                                    TabelaTrigger.VARIACAO,
                                                    OperacaoTrigger.REMOVER);
            }

            await _zendarTriggerService.ExecuteGuid(produtoId,
                                                TabelaTrigger.PRODUTO,
                                                OperacaoTrigger.ALTERAR);

            #endregion
        }

        public async Task Ativar(
            Guid[] produtoCorTamanhoId)
        {
            var produtoCorTamanhos =
                await _produtoCorTamanhoRepository.Where(pct => !pct.Ativo &&
                                                                produtoCorTamanhoId.Contains(pct.Id))
                                                  .Include(c => c.ProdutoCor)
                                                  .ToListAsync();

            if (produtoCorTamanhos.Count == 0)
                return;

            foreach (var produtoCorTamanho in produtoCorTamanhos)
                produtoCorTamanho.Ativo = true;

            await _produtoCorTamanhoRepository.SaveChanges();

            #region Trigger

            var produtoId =
                produtoCorTamanhos.Select(c => c.ProdutoCor.ProdutoId)
                                  .FirstOrDefault();

            if (produtoId != Guid.Empty)
                await _zendarTriggerService.ExecuteGuid(produtoId,
                                                    TabelaTrigger.PRODUTO,
                                                    OperacaoTrigger.ALTERAR);

            #endregion
        }

        public async Task Inativar(
            Guid[] produtoCorTamanhoId)
        {
            var produtoCorTamanhos =
                await _produtoCorTamanhoRepository.Where(pct => pct.Ativo &&
                                                                produtoCorTamanhoId.Contains(pct.Id))
                                                  .Include(c => c.ProdutoCor)
                                                  .ToListAsync();

            if (produtoCorTamanhos.Count == 0)
                return;

            var itensKit =
                await _produtoCorTamanhoKitRepository.FindAllSelectAsNoTracking(x => produtoCorTamanhoId.Contains(x.ProdutoCorTamanhoItemId),
                                                                                x => new ProdutoCorTamanhoKit { ProdutoCorTamanhoItemId = x.ProdutoCorTamanhoItemId });

            if (itensKit.Any())
                NotificarAviso("Algumas variações não foram inativadas, pois fazem parte de um produto kit.");

            var idsItensKit =
                itensKit.Select(x => x.ProdutoCorTamanhoItemId)
                        .Distinct();

            foreach (var produtoCorTamanho in produtoCorTamanhos.Where(pct => !idsItensKit.Contains(pct.Id)))
                produtoCorTamanho.Ativo = false;

            await _produtoCorTamanhoRepository.SaveChanges();

            #region Trigger

            foreach (var produtoCorTamanho in produtoCorTamanhos)
            {
                await _zendarTriggerService.ExecuteGuid(produtoCorTamanho.Id,
                                                    TabelaTrigger.VARIACAO,
                                                    OperacaoTrigger.REMOVER);
            }

            var produtoId =
                produtoCorTamanhos.Select(c => c.ProdutoCor.ProdutoId)
                                  .FirstOrDefault();

            if (produtoId != Guid.Empty)
            {
                await _zendarTriggerService.ExecuteGuid(produtoId,
                                                    TabelaTrigger.PRODUTO,
                                                    OperacaoTrigger.ALTERAR);
            }

            #endregion
        }
    }
}