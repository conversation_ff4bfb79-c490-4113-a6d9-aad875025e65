import { Box, Flex } from '@chakra-ui/react';
import { useState, useRef, useCallback, useEffect } from 'react';

import { PaginationData } from 'components/update/Pagination';
import {
  PagedTable,
  PagedTableForwardRefData,
} from 'components/update/Table/PagedTable';

import { EnumTelaConsultaProdutosPdv } from '..';
import { VariacoesResponseProps, VariacaoItem } from '../../VariacaoItem';

interface ConsultaVariacoesProps {
  tela: EnumTelaConsultaProdutosPdv;
  paginationHandleGrade: (
    paginationData: PaginationData,
    produtoId?: string
  ) => Promise<void>;
  totalRegistrosGrade: number;
  listarVariacoes: VariacoesResponseProps[];
  casasDecimais: {
    casasDecimaisQuantidade: number;
  };
  toggleSelectVariacao: (variacao: VariacoesResponseProps) => void;
  variacaoSelecionada: VariacoesResponseProps | null;
  handleDoubleClickVariacao: (variacao: VariacoesResponseProps) => void;
  isOpenDrawer?: boolean;
}

export const ConsultaVariacoes = ({
  tela,
  paginationHandleGrade,
  totalRegistrosGrade,
  listarVariacoes,
  casasDecimais,
  toggleSelectVariacao,
  variacaoSelecionada,
  handleDoubleClickVariacao,
  isOpenDrawer = false,
}: ConsultaVariacoesProps) => {
  const [focusedIndex, setFocusedIndex] = useState<number>(-1);
  const [tabelaEstaFocada, setTabelaEstaFocada] = useState<boolean>(false);
  const tabelaPaginadaRef = useRef<PagedTableForwardRefData>(null);
  const containerTabelaRef = useRef<HTMLDivElement>(null);

  const scrollToFocusedItem = useCallback((index: number) => {
    const containerTabela = containerTabelaRef.current;
    if (!containerTabela) return;

    const linha = containerTabela.querySelectorAll('tbody tr');
    const linhaFocada = linha[index] as HTMLElement;

    if (linhaFocada) {
      const containerRect = containerTabela.getBoundingClientRect();
      const linhaRect = linhaFocada.getBoundingClientRect();

      const cabecalhoTabela = containerTabela.querySelector('thead');
      const tamanhoCabecalho = cabecalhoTabela
        ? cabecalhoTabela.getBoundingClientRect().height
        : 0;

      const topoTabelaVisivel = containerRect.top + tamanhoCabecalho;
      const fimTabelaVisivel = containerRect.bottom;

      const estaAcimaDaTabelaVisivel = linhaRect.top < topoTabelaVisivel;
      const estaAbaixoDaTabelaVisivel = linhaRect.bottom > fimTabelaVisivel;

      if (estaAcimaDaTabelaVisivel || estaAbaixoDaTabelaVisivel) {
        if (index === 0) {
          containerTabela.scrollTo({
            top: 0,
            behavior: 'smooth',
          });
        } else if (index === linha.length - 1) {
          containerTabela.scrollTo({
            top: containerTabela.scrollHeight - containerTabela.clientHeight,
            behavior: 'smooth',
          });
        } else {
          linhaFocada.scrollIntoView({
            behavior: 'smooth',
            block: 'center',
          });
        }
      }
    }
  }, []);

  const handleKeyDown = useCallback(
    (e: KeyboardEvent) => {
      const activeElement = document.activeElement;
      const isInputFocused =
        activeElement?.tagName === 'INPUT' ||
        activeElement?.tagName === 'SELECT' ||
        activeElement?.tagName === 'TEXTAREA' ||
        activeElement?.getAttribute('role') === 'combobox';

      if (
        isOpenDrawer ||
        !tabelaEstaFocada ||
        isInputFocused ||
        !listarVariacoes?.length
      )
        return;

      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault();
          setFocusedIndex((prev) => {
            const newIndex = Math.min(prev + 1, listarVariacoes.length - 1);
            scrollToFocusedItem(newIndex);
            return newIndex;
          });
          break;
        case 'ArrowUp':
          e.preventDefault();
          setFocusedIndex((prev) => {
            const newIndex = Math.max(prev - 1, 0);
            scrollToFocusedItem(newIndex);
            return newIndex;
          });
          break;
        case 'Enter':
          e.preventDefault();
          if (focusedIndex >= 0 && listarVariacoes[focusedIndex]) {
            const variacao = listarVariacoes[focusedIndex];
            const variacaoEstaSelecionada =
              variacaoSelecionada?.produtoCorTamanho.produtoCorTamanhoId ===
              variacao?.produtoCorTamanho.produtoCorTamanhoId;

            if (e.shiftKey) {
              handleDoubleClickVariacao(variacao);
            } else {
              toggleSelectVariacao(variacao);

              if (variacaoEstaSelecionada) {
                handleDoubleClickVariacao(variacao);
              }
            }
          }
          break;
      }
    },
    [
      isOpenDrawer,
      tabelaEstaFocada,
      listarVariacoes,
      focusedIndex,
      toggleSelectVariacao,
      handleDoubleClickVariacao,
      scrollToFocusedItem,
      variacaoSelecionada,
    ]
  );

  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [handleKeyDown]);

  useEffect(() => {
    if (listarVariacoes?.length > 0) {
      setFocusedIndex((prev) => {
        if (prev < 0 || prev >= listarVariacoes.length) {
          return 0;
        }
        return prev;
      });
      setTabelaEstaFocada(true);
      setTimeout(() => {
        containerTabelaRef.current?.focus();
      }, 100);
    } else {
      setFocusedIndex(-1);
    }
  }, [listarVariacoes]);

  useEffect(() => {
    if (focusedIndex >= 0 && listarVariacoes?.length > 0) {
      setTimeout(() => {
        scrollToFocusedItem(focusedIndex);
      }, 50);
    }
  }, [focusedIndex, scrollToFocusedItem, listarVariacoes]);

  const handleTableClick = useCallback(() => {
    setTabelaEstaFocada(true);
    containerTabelaRef.current?.focus();
  }, []);

  const handleTableBlur = useCallback(() => {
    setTabelaEstaFocada(false);
  }, []);

  if (tela !== EnumTelaConsultaProdutosPdv.VARIACOES) {
    return null;
  }

  return (
    <Box
      ref={containerTabelaRef}
      tabIndex={0}
      onClick={handleTableClick}
      onBlur={handleTableBlur}
      onFocus={() => setTabelaEstaFocada(true)}
      outline="none"
      _focus={{ outline: 'none' }}
    >
      <PagedTable
        ref={tabelaPaginadaRef}
        variant="card"
        bg="transparent"
        boxShadow="none"
        sx={{
          '& div': {
            padding: '0px 2px',
          },
        }}
        paginationHasDivider={false}
        loadColumnsData={paginationHandleGrade}
        itensPerPage={25}
        itemsTotalCount={totalRegistrosGrade}
        defaultKeyOrdered="nome"
        tableHeaders={[
          {
            key: 'grade',
            content: 'Grade de variações',
            isOrderable: false,
            pl: '58px !important',
          },
          {
            content: (
              <Flex alignItems="center" justifyContent="flex-end">
                Preço de venda
              </Flex>
            ),
            key: 'PrecoVenda',
            isOrderable: false,
          },
          {
            content: (
              <Flex alignItems="center" justifyContent="flex-end">
                Estoque
              </Flex>
            ),
            key: 'Estoque',
            isOrderable: false,
          },
        ]}
        renderTableRows={(listarVariacoes || []).map((variacao, index) => (
          <VariacaoItem
            key={variacao.produtoCorTamanho.produtoCorTamanhoId}
            variacao={variacao}
            casasDecimais={casasDecimais}
            toggleSelectVariacao={toggleSelectVariacao}
            variacaoSelecionada={variacaoSelecionada}
            handleDoubleClickVariacao={handleDoubleClickVariacao}
            isFocused={focusedIndex === index}
            index={index}
            setFocusedIndex={setFocusedIndex}
            setTabelaEstaFocada={setTabelaEstaFocada}
            containerTabelaRef={containerTabelaRef}
          />
        ))}
      />
    </Box>
  );
};
