import auth from 'modules/auth';

import TipoSistemaEnum from 'constants/enum/tipoSistema';

import GenIcon from '../GenIcon';
import { StrokeRounded, IconType } from '../types';

const sistema = auth.getSistema();
const isFomer =
  sistema.value === TipoSistemaEnum.FOMER ||
  sistema.value === TipoSistemaEnum.FOMER_SISTEMA;

export const SuporteTecnicoIcon: IconType = ({
  size,
  stroke,
  fill,
  ...props
}) => (
  <GenIcon viewBox="0 0 26 25.1" size={size} {...props}>
    <g id="Grupo_12571" style={StrokeRounded}>
      <path
        id="Caminho_16771"
        data-name="Caminho 16771"
        stroke={stroke}
        fill="none"
        d="m3.9,17.1h-2c-.8,0-1.4-.6-1.4-1.4v-4.2c0-.8.6-1.4,1.4-1.4h2"
      />
      <path
        id="Caminho_16772"
        data-name="Caminho 16772"
        stroke={stroke}
        fill="none"
        d="m22,17.1h2c.8,0,1.4-.6,1.4-1.4h0v-4.2c0-.8-.6-1.4-1.4-1.4h-2"
      />
      <path
        id="Caminho_16773"
        data-name="Caminho 16773"
        stroke={stroke}
        fill="none"
        d="m15.9,20.9v-1.1c0-.6-.5-1.1-1.1-1.1h-.8c-.6,0-1.1.5-1.1,1.1s.5,1.1,1.1,1.1h5.8c1.2,0,2.2-1,2.2-2.2h0v-10.1c0-1.2-1-2.2-2.2-2.2,0,0,0,0,0,0H6.1c-1.2,0-2.2,1-2.2,2.2,0,0,0,0,0,0v10.1c0,1.2,1,2.2,2.2,2.2h3.9"
      />
      <path
        id="Caminho_16774"
        data-name="Caminho 16774"
        stroke={stroke}
        fill="none"
        d="m8.1,20.9v3.1c0,.3.3.6.6.6.1,0,.2,0,.3,0l3.8-2.2"
      />
      <path
        id="Caminho_16775"
        data-name="Caminho 16775"
        stroke={stroke}
        fill="none"
        d="m1.8,10.2C1.8,4.9,6.1.6,11.5.6h0s3,0,3,0c5.3,0,9.6,4.3,9.6,9.6"
      />
      <path
        id="Caminho_16776"
        data-name="Caminho 16776"
        strokeWidth="1px"
        stroke={stroke}
        fill={fill}
        d="m11.9,11.1c0,.5-.4.9-.9.9s-.9-.4-.9-.9.4-.9.9-.9h0c.5,0,.9.4.9.9"
      />
      <path
        id="Caminho_16777"
        data-name="Caminho 16777"
        stroke={stroke}
        strokeWidth="1px"
        fill={fill}
        d="m15.8,11.1c0,.5-.4.9-.9.9s-.9-.4-.9-.9.4-.9.9-.9.9.4.9.9h0"
      />
      <path
        id="Caminho_16778"
        data-name="Caminho 16778"
        stroke={stroke}
        fill="none"
        d="m9,13c.3,2.2,2.3,3.8,4.5,3.5,1.9-.2,3.3-1.7,3.5-3.5"
      />
    </g>
  </GenIcon>
);

export const AreaArtigoIcon: IconType = ({
  size,
  stroke = 'black',
  fill = 'transparent',
  ...props
}) => (
  <GenIcon
    viewBox="0 0 22.81 25.28"
    size={size}
    stroke={stroke}
    fill={fill}
    {...props}
  >
    <path
      fill={fill}
      stroke={stroke}
      d="M7.05,1.75L1.55,7.5c-.63.66-.98,1.53-.98,2.43v11.16c0,1.94,1.58,3.52,3.52,3.52h14.65c1.94,0,3.52-1.58,3.52-3.52V4.18c0-1.94-1.58-3.52-3.52-3.52h-9.15c-.96,0-1.88.39-2.54,1.09Z"
    />
    <path d="M9.35.66c-.87.06-1.69.43-2.3,1.06L1.55,7.47c-.52.55-.84,1.25-.93,1.99h5.23c1.94,0,3.5-1.57,3.5-3.5V.66Z" />
    <line
      fill={fill}
      stroke={stroke}
      x1="6.35"
      y1="19.49"
      x2="16.23"
      y2="19.49"
    />
    <line
      fill={fill}
      stroke={stroke}
      x1="6.35"
      y1="15.08"
      x2="16.23"
      y2="15.08"
    />
    <line
      fill={fill}
      stroke={stroke}
      x1="12.04"
      y1="10.55"
      x2="16.23"
      y2="10.55"
    />
  </GenIcon>
);

export const TeclasAtalhoIcon: IconType = ({
  size,
  stroke,
  fill = 'transparent',
  ...props
}) => {
  const strokeColor = stroke || (isFomer ? 'white' : 'black');

  return (
    <GenIcon
      viewBox="0 0 19.5 19.28"
      size={size}
      stroke={strokeColor}
      fill={fill}
      {...props}
    >
      <rect
        id="Retângulo_25394"
        data-name="Retângulo 25394"
        fill={fill}
        stroke={strokeColor}
        x=".7"
        y="3.5"
        width="18.15"
        height="15.13"
        rx="2.79"
        ry="2.79"
      />
      <path
        id="Caminho_22408"
        data-name="Caminho 22408"
        fill={fill}
        stroke={strokeColor}
        d="M6.2,2.2c0-1.58,1.51-1.9,2.57-1.24,2.25,1.42,4.31,1.99,6.77,0"
      />
      <path
        id="Caminho_22409"
        data-name="Caminho 22409"
        fill={fill}
        stroke={strokeColor}
        d="M8.18,3.5c0-.61-.5-1.11-1.11-1.11h-1.65c-.61,0-1.11.5-1.11,1.11h0"
      />
      <circle fill={fill} stroke={strokeColor} cx="3.7" cy="7.62" r=".49" />
      <circle fill={fill} stroke={strokeColor} cx="5.73" cy="11.11" r=".49" />
      <circle fill={fill} stroke={strokeColor} cx="9.78" cy="11.11" r=".49" />
      <circle fill={fill} stroke={strokeColor} cx="13.82" cy="11.11" r=".49" />
      <circle fill={fill} stroke={strokeColor} cx="7.76" cy="7.62" r=".49" />
      <circle fill={fill} stroke={strokeColor} cx="11.8" cy="7.62" r=".49" />
      <circle fill={fill} stroke={strokeColor} cx="15.85" cy="7.62" r=".49" />
      <rect
        fill={fill}
        stroke={strokeColor}
        x="7.27"
        y="14.4"
        width="5.02"
        height=".99"
        rx=".49"
        ry=".49"
      />
    </GenIcon>
  );
};
export const AreaVideosIcon: IconType = ({
  size,
  stroke = 'black',
  fill = 'transparent',
  ...props
}) => (
  <GenIcon
    viewBox="0 0 32.61 25.08"
    size={size}
    stroke={stroke}
    fill={fill}
    {...props}
  >
    <g
      xmlns="http://www.w3.org/2000/svg"
      id="Grupo_16127"
      data-name="Grupo 16127"
    >
      <path
        id="Caminho_16817"
        data-name="Caminho 16817"
        fill={fill}
        stroke={stroke}
        d="M19.97,8.26l-5.91-3.56c-.35-.18-.78-.04-.96.31-.06.13-.09.27-.08.41v7.08c-.04.39.24.75.63.79.14.02.28,0,.41-.07l5.91-3.52c.4-.27.5-.81.23-1.2-.06-.09-.14-.17-.23-.23Z"
      />
      <rect
        id="Retângulo_22230"
        data-name="Retângulo 22230"
        fill={fill}
        stroke={stroke}
        x=".55"
        y=".53"
        width="31.5"
        height="19.75"
        rx="1.68"
        ry="1.68"
      />
      <line
        id="Linha_1770"
        data-name="Linha 1770"
        fill={fill}
        stroke={stroke}
        x1=".55"
        y1="17.35"
        x2="32.05"
        y2="17.35"
      />
      <line
        id="Linha_1771"
        data-name="Linha 1771"
        fill={fill}
        stroke={stroke}
        x1="12.22"
        y1="24.53"
        x2="20.35"
        y2="24.53"
      />
      <line
        id="Linha_1772"
        data-name="Linha 1772"
        fill={fill}
        stroke={stroke}
        x1="16.29"
        y1="24.53"
        x2="16.29"
        y2="20.29"
      />
    </g>
  </GenIcon>
);

export const AreaSuporteTecnicoIcon: IconType = ({
  size,
  stroke = 'black',
  fill = 'transparent',
  ...props
}) => (
  <GenIcon
    viewBox="0 0 24.23 29.13"
    size={size}
    stroke={stroke}
    fill={fill}
    {...props}
  >
    <g
      xmlns="http://www.w3.org/2000/svg"
      id="Grupo_16193"
      data-name="Grupo 16193"
    >
      <path
        id="Caminho_20227"
        data-name="Caminho 20227"
        stroke={stroke}
        d="M17.24,5.85c0,3.31-2.32,6.69-5.18,6.69s-5.17-3.37-5.17-6.69c-.16-2.77,1.95-5.14,4.71-5.31.15,0,.31-.01.46,0,2.77-.09,5.09,2.08,5.18,4.85,0,.15,0,.31,0,.46Z"
      />
      <path
        id="Caminho_20228"
        data-name="Caminho 20228"
        stroke={stroke}
        d="M6.95,4.95h-.61c-.8,0-1.44.65-1.44,1.44v1.8c0,.8.65,1.44,1.44,1.44h5.6"
      />
      <path
        id="Caminho_20229"
        data-name="Caminho 20229"
        stroke={stroke}
        d="M17.18,4.95h.36c.8,0,1.44.65,1.44,1.44v1.8c0,.8-.65,1.44-1.44,1.44h-1.29"
      />
      <path
        id="Caminho_20230"
        data-name="Caminho 20230"
        stroke={stroke}
        d="M4.94,17.72c.89-3.16,3.82-5.3,7.1-5.18,3.48,0,6.49,2.26,7.13,5.18"
      />
      <path
        id="Caminho_20231"
        data-name="Caminho 20231"
        stroke={stroke}
        d="M2.19,24.88l-.83-5.95c-.08-.58.32-1.12.9-1.2.05,0,.1-.01.15-.01h19.23c.59,0,1.06.47,1.06,1.06,0,.05,0,.09,0,.14l-.78,5.96"
      />
      <line
        id="Linha_1843"
        data-name="Linha 1843"
        stroke={stroke}
        x1=".55"
        y1="28.54"
        x2="23.67"
        y2="28.54"
      />
      <path
        id="Caminho_20232"
        data-name="Caminho 20232"
        stroke={stroke}
        d="M13.05,23.13c0,.56-.45,1.01-1.01,1.01s-1.01-.45-1.01-1.01c0-.56.45-1.01,1.01-1.01s1.01.45,1.01,1.01h0Z"
      />
    </g>
  </GenIcon>
);
export const AjudaChat: IconType = ({ size, ...props }) => (
  <GenIcon viewBox="0 0 25.9 21.8" size={size} {...props}>
    <g id="Grupo_12571" style={StrokeRounded}>
      <line
        id="Linha_1251"
        data-name="Linha 1251"
        stroke="black"
        fill="none"
        x1="16.1"
        y1="12.7"
        x2="20.8"
        y2="12.7"
      />
      <line
        id="Linha_1252"
        data-name="Linha 1252"
        stroke="black"
        fill="none"
        x1="16.1"
        y1="15.2"
        x2="20.8"
        y2="15.2"
      />
      <path
        id="Caminho_16779"
        data-name="Caminho 16779"
        stroke="black"
        fill="none"
        d="m23.2,17.9c1.3-.9,2.1-2.3,2.1-3.9,0-3-3.1-5.4-6.9-5.4s-6.9,2.4-6.9,5.4,3.1,5.4,6.9,5.4c.8,0,1.6-.1,2.3-.3l3.3,2.2-.8-3.4Z"
      />
      <path
        id="Caminho_16780"
        data-name="Caminho 16780"
        stroke="black"
        fill="none"
        d="m20.8,7.1c-1-3.7-5.1-6.5-10-6.5S.5,4.2.5,8.8c0,2.6,1.5,5,3.7,6.4l-1.5,4.5,4.4-3.2c1.2.4,2.4.6,3.7.6"
      />
    </g>
  </GenIcon>
);

export const AjudaContato: IconType = ({ size, ...props }) => (
  <GenIcon viewBox="0 0 25.1 25.1" size={size} {...props}>
    <g id="Grupo_12571" style={StrokeRounded}>
      <path
        stroke="black"
        fill="none"
        d="m22.8,19.5s-2.7-2.6-3.7-3.6c-.5-.5-1.3-.6-1.8-.1,0,0-.1.1-.2.2,0,0-.3.3-1.9,2-.2.3-.6.4-.9.2-1.5-.8-2.8-1.8-3.9-3-1.3-1.2-2.4-2.6-3.3-4.2-.2-.3-.1-.7.2-.9,1.7-1.6,2-1.9,2-1.9.6-.4.7-1.2.2-1.8,0,0-.1-.1-.2-.2-1-1-3.6-3.7-3.6-3.7-.5-.5-1.3-.5-1.8,0,0,0,0,0,0,0-1,.9-2.6,2.6-2.6,2.6,0,0-3.1,3,4.2,12.2.3.4.8,1,1.1,1.3,0,0,0,0,.1.1.4.4.9.8,1.4,1.2,9.1,7.3,12.2,4.2,12.2,4.2,0,0,1.7-1.6,2.6-2.6.5-.5.5-1.3,0-1.8,0,0,0,0,0,0Z"
      />
      <path
        stroke="black"
        fill="none"
        d="m13.4,5.2c1.6.2,3.1.9,4.3,2,1.3,1.2,2.2,2.8,2.4,4.5"
      />
      <path
        stroke="black"
        fill="none"
        d="m13.5.5c2.6.3,5.1,1.5,7,3.3,2.2,2,3.6,4.7,4,7.7"
      />
    </g>
  </GenIcon>
);

export const AjudaEmail: IconType = ({ size, ...props }) => (
  <GenIcon viewBox="0 0 25.1 17.9" size={size} {...props}>
    <g id="Grupo_12580" style={StrokeRounded}>
      <rect
        id="Retângulo_6907"
        data-name="Retângulo 6907"
        stroke="black"
        fill="none"
        x=".6"
        y=".5"
        width="24"
        height="16.8"
        rx="1"
        ry="1"
      />
      <path
        id="Caminho_2255"
        data-name="Caminho 2255"
        stroke="black"
        fill="none"
        d="m24,1.2l-10.3,10.1c-.7.6-1.7.6-2.4,0L1.1,1.1"
      />
      <line
        id="Linha_1648"
        data-name="Linha 1648"
        stroke="black"
        fill="none"
        x1="9.1"
        y1="9"
        x2="1.1"
        y2="16.7"
      />
      <line
        id="Linha_1649"
        data-name="Linha 1649"
        stroke="black"
        fill="none"
        x1="16.1"
        y1="9"
        x2="24.1"
        y2="16.7"
      />
    </g>
  </GenIcon>
);

export const AjudaTreinamento: IconType = ({ size, ...props }) => (
  <GenIcon viewBox="0 0 25.1 19.4" size={size} {...props}>
    <g id="Grupo_12571" style={StrokeRounded}>
      <path
        id="Caminho_16817"
        data-name="Caminho 16817"
        stroke="#4f01b1"
        fill="none"
        d="m15.3,6.4l-4.5-2.7c-.3-.1-.6,0-.7.2,0,0,0,.2,0,.3v5.4c0,.3.2.6.5.6.1,0,.2,0,.3,0l4.5-2.7c.3-.2.4-.6.2-.9,0,0-.1-.1-.2-.2Z"
      />
      <rect
        id="Retângulo_22230"
        data-name="Retângulo 22230"
        stroke="#4f01b1"
        fill="none"
        x=".5"
        y=".5"
        width="24"
        height="15.1"
        rx="1.7"
        ry="1.7"
      />
      <line
        id="Linha_1770"
        data-name="Linha 1770"
        stroke="#4f01b1"
        fill="none"
        x1=".5"
        y1="13.3"
        x2="24.5"
        y2="13.3"
      />
      <line
        id="Linha_1771"
        data-name="Linha 1771"
        stroke="#4f01b1"
        fill="none"
        x1="9.4"
        y1="18.8"
        x2="15.6"
        y2="18.8"
      />
      <line
        id="Linha_1772"
        data-name="Linha 1772"
        stroke="#4f01b1"
        fill="none"
        x1="12.5"
        y1="18.8"
        x2="12.5"
        y2="15.6"
      />
    </g>
  </GenIcon>
);
