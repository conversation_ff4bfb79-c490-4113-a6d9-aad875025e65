import {
  useMedia<PERSON>uery,
  <PERSON>dal<PERSON>ontent,
  ModalHeader,
  ModalCloseButton,
  ModalBody,
} from '@chakra-ui/react';
import React from 'react';

import { MobileModalMenu } from 'components/PDV/Layout/MobileModalMenu';
import ModalPadraoChakra from 'components/PDV/Modal/ModalPadraoChakra';

import { ConteudoModalAtalhosConsulta } from './ConteudoModalAtalhosConsulta';

interface ModalAtalhosConsultaProps {
  isOpen: boolean;
  onClose: () => void;
  asMobileView?: boolean;
}

const ModalAtalhosConsulta: React.FC<ModalAtalhosConsultaProps> = ({
  isOpen,
  onClose,
  asMobileView = false,
}) => {
  const [isLargerThan900] = useMediaQuery('(min-width: 900px)');

  if (asMobileView) {
    return (
      <MobileModalMenu isOpen={isOpen} onClose={onClose} title="Atalhos">
        <ConteudoModalAtalhosConsulta />
      </MobileModalMenu>
    );
  }

  return (
    <ModalPadraoChakra
      isOpen={isOpen}
      onClose={onClose}
      size={isLargerThan900 ? '2xl' : 'full'}
      scrollBehavior={isLargerThan900 ? 'inside' : 'outside'}
      isCentered={isLargerThan900}
    >
      <ModalContent
        margin={0}
        maxW={isLargerThan900 ? 800 : undefined}
        borderRadius={isLargerThan900 ? 'md' : 0}
        bg="gray.50"
        h="450px"
      >
        <ModalHeader
          color="primary.500"
          mt={isLargerThan900 ? undefined : 12}
          mb={isLargerThan900 ? undefined : 8}
        >
          Atalhos
        </ModalHeader>
        <ModalCloseButton
          mt={isLargerThan900 ? undefined : 14}
          mr={isLargerThan900 ? undefined : 6}
        />
        <ModalBody pb={6}>
          <ConteudoModalAtalhosConsulta />
        </ModalBody>
      </ModalContent>
    </ModalPadraoChakra>
  );
};

export default ModalAtalhosConsulta;
