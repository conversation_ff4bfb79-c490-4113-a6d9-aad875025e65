﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net5.0</TargetFramework>
	</PropertyGroup>

	<ItemGroup>
		<Compile Remove="Services\V1\PesquisaProdutoServices\Listar\**" />
		<Compile Remove="Services\V1\PesquisaProdutoServices\Obter\**" />
		<Compile Remove="Services\V2\ProdutoEtapaV2Services\AlterarItensEtapaProdutoV2Service\**" />
		<EmbeddedResource Remove="Services\V1\PesquisaProdutoServices\Listar\**" />
		<EmbeddedResource Remove="Services\V1\PesquisaProdutoServices\Obter\**" />
		<EmbeddedResource Remove="Services\V2\ProdutoEtapaV2Services\AlterarItensEtapaProdutoV2Service\**" />
		<None Remove="Services\V1\PesquisaProdutoServices\Listar\**" />
		<None Remove="Services\V1\PesquisaProdutoServices\Obter\**" />
		<None Remove="Services\V2\ProdutoEtapaV2Services\AlterarItensEtapaProdutoV2Service\**" />
	</ItemGroup>

	<ItemGroup>
		<None Remove="Helpers\NotaFiscal\PacoteLiberacao\Evento_Generico_PL_v1_01\envEvento_v1.00.xsd" />
		<None Remove="Helpers\NotaFiscal\PacoteLiberacao\Evento_Generico_PL_v1_01\leiauteEvento_v1.00.xsd" />
		<None Remove="Helpers\NotaFiscal\PacoteLiberacao\Evento_Generico_PL_v1_01\procEventoNFe_v1.00.xsd" />
		<None Remove="Helpers\NotaFiscal\PacoteLiberacao\Evento_Generico_PL_v1_01\retEnvEvento_v1.00.xsd" />
		<None Remove="Helpers\NotaFiscal\PacoteLiberacao\Evento_Generico_PL_v1_01\tiposBasico_v1.03.xsd" />
		<None Remove="Helpers\NotaFiscal\PacoteLiberacao\Evento_Generico_PL_v1_01\xmldsig-core-schema_v1.01.xsd" />
		<None Remove="Helpers\NotaFiscal\PacoteLiberacao\PL_009g_V4_00_NT_2020_005_v120\consReciNFe_v4.00.xsd" />
		<None Remove="Helpers\NotaFiscal\PacoteLiberacao\PL_009g_V4_00_NT_2020_005_v120\consSitNFe_v4.00.xsd" />
		<None Remove="Helpers\NotaFiscal\PacoteLiberacao\PL_009g_V4_00_NT_2020_005_v120\consStatServ_v4.00.xsd" />
		<None Remove="Helpers\NotaFiscal\PacoteLiberacao\PL_009g_V4_00_NT_2020_005_v120\enviNFe_v4.00.xsd" />
		<None Remove="Helpers\NotaFiscal\PacoteLiberacao\PL_009g_V4_00_NT_2020_005_v120\inutNFe_v4.00.xsd" />
		<None Remove="Helpers\NotaFiscal\PacoteLiberacao\PL_009g_V4_00_NT_2020_005_v120\leiauteConsSitNFe_v4.00.xsd" />
		<None Remove="Helpers\NotaFiscal\PacoteLiberacao\PL_009g_V4_00_NT_2020_005_v120\leiauteConsStatServ_v4.00.xsd" />
		<None Remove="Helpers\NotaFiscal\PacoteLiberacao\PL_009g_V4_00_NT_2020_005_v120\leiauteInutNFe_v4.00.xsd" />
		<None Remove="Helpers\NotaFiscal\PacoteLiberacao\PL_009g_V4_00_NT_2020_005_v120\leiauteNFe_v4.00.xsd" />
		<None Remove="Helpers\NotaFiscal\PacoteLiberacao\PL_009g_V4_00_NT_2020_005_v120\nfe_v4.00.xsd" />
		<None Remove="Helpers\NotaFiscal\PacoteLiberacao\PL_009g_V4_00_NT_2020_005_v120\procInutNFe_v4.00.xsd" />
		<None Remove="Helpers\NotaFiscal\PacoteLiberacao\PL_009g_V4_00_NT_2020_005_v120\procNFe_v4.00.xsd" />
		<None Remove="Helpers\NotaFiscal\PacoteLiberacao\PL_009g_V4_00_NT_2020_005_v120\retConsReciNFe_v4.00.xsd" />
		<None Remove="Helpers\NotaFiscal\PacoteLiberacao\PL_009g_V4_00_NT_2020_005_v120\retConsSitNFe_v4.00.xsd" />
		<None Remove="Helpers\NotaFiscal\PacoteLiberacao\PL_009g_V4_00_NT_2020_005_v120\retConsStatServ_v4.00.xsd" />
		<None Remove="Helpers\NotaFiscal\PacoteLiberacao\PL_009g_V4_00_NT_2020_005_v120\retEnviNFe_v4.00.xsd" />
		<None Remove="Helpers\NotaFiscal\PacoteLiberacao\PL_009g_V4_00_NT_2020_005_v120\retInutNFe_v4.00.xsd" />
		<None Remove="Helpers\NotaFiscal\PacoteLiberacao\PL_009g_V4_00_NT_2020_005_v120\tiposBasico_v4.00.xsd" />
		<None Remove="Helpers\NotaFiscal\PacoteLiberacao\PL_009g_V4_00_NT_2020_005_v120\xmldsig-core-schema_v1.01.xsd" />
		<None Remove="Helpers\NotaFiscal\PacoteLiberacao\PL_009_V4_00_NT_2020_005_v1_10\consReciNFe_v4.00.xsd" />
		<None Remove="Helpers\NotaFiscal\PacoteLiberacao\PL_009_V4_00_NT_2020_005_v1_10\consSitNFe_v4.00.xsd" />
		<None Remove="Helpers\NotaFiscal\PacoteLiberacao\PL_009_V4_00_NT_2020_005_v1_10\consStatServ_v4.00.xsd" />
		<None Remove="Helpers\NotaFiscal\PacoteLiberacao\PL_009_V4_00_NT_2020_005_v1_10\enviNFe_v4.00.xsd" />
		<None Remove="Helpers\NotaFiscal\PacoteLiberacao\PL_009_V4_00_NT_2020_005_v1_10\inutNFe_v4.00.xsd" />
		<None Remove="Helpers\NotaFiscal\PacoteLiberacao\PL_009_V4_00_NT_2020_005_v1_10\leiauteConsSitNFe_v4.00.xsd" />
		<None Remove="Helpers\NotaFiscal\PacoteLiberacao\PL_009_V4_00_NT_2020_005_v1_10\leiauteConsStatServ_v4.00.xsd" />
		<None Remove="Helpers\NotaFiscal\PacoteLiberacao\PL_009_V4_00_NT_2020_005_v1_10\leiauteInutNFe_v4.00.xsd" />
		<None Remove="Helpers\NotaFiscal\PacoteLiberacao\PL_009_V4_00_NT_2020_005_v1_10\leiauteNFe_v4.00.xsd" />
		<None Remove="Helpers\NotaFiscal\PacoteLiberacao\PL_009_V4_00_NT_2020_005_v1_10\nfe_v4.00.xsd" />
		<None Remove="Helpers\NotaFiscal\PacoteLiberacao\PL_009_V4_00_NT_2020_005_v1_10\procInutNFe_v4.00.xsd" />
		<None Remove="Helpers\NotaFiscal\PacoteLiberacao\PL_009_V4_00_NT_2020_005_v1_10\procNFe_v4.00.xsd" />
		<None Remove="Helpers\NotaFiscal\PacoteLiberacao\PL_009_V4_00_NT_2020_005_v1_10\retConsReciNFe_v4.00.xsd" />
		<None Remove="Helpers\NotaFiscal\PacoteLiberacao\PL_009_V4_00_NT_2020_005_v1_10\retConsSitNFe_v4.00.xsd" />
		<None Remove="Helpers\NotaFiscal\PacoteLiberacao\PL_009_V4_00_NT_2020_005_v1_10\retConsStatServ_v4.00.xsd" />
		<None Remove="Helpers\NotaFiscal\PacoteLiberacao\PL_009_V4_00_NT_2020_005_v1_10\retEnviNFe_v4.00.xsd" />
		<None Remove="Helpers\NotaFiscal\PacoteLiberacao\PL_009_V4_00_NT_2020_005_v1_10\retInutNFe_v4.00.xsd" />
		<None Remove="Helpers\NotaFiscal\PacoteLiberacao\PL_009_V4_00_NT_2020_005_v1_10\tiposBasico_v4.00.xsd" />
		<None Remove="Helpers\NotaFiscal\PacoteLiberacao\PL_009_V4_00_NT_2020_005_v1_10\xmldsig-core-schema_v1.01.xsd" />
	</ItemGroup>

	<ItemGroup>
		<Content Include="Helpers\NotaFiscal\PacoteLiberacao\Evento_Generico_PL_v1_01\envEvento_v1.00.xsd">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Include="Helpers\NotaFiscal\PacoteLiberacao\Evento_Generico_PL_v1_01\leiauteEvento_v1.00.xsd">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Include="Helpers\NotaFiscal\PacoteLiberacao\Evento_Generico_PL_v1_01\procEventoNFe_v1.00.xsd">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Include="Helpers\NotaFiscal\PacoteLiberacao\Evento_Generico_PL_v1_01\retEnvEvento_v1.00.xsd">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Include="Helpers\NotaFiscal\PacoteLiberacao\Evento_Generico_PL_v1_01\tiposBasico_v1.03.xsd">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Include="Helpers\NotaFiscal\PacoteLiberacao\Evento_Generico_PL_v1_01\xmldsig-core-schema_v1.01.xsd">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Include="Helpers\NotaFiscal\PacoteLiberacao\PL_009g_V4_00_NT_2020_005_v120\consReciNFe_v4.00.xsd">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Include="Helpers\NotaFiscal\PacoteLiberacao\PL_009g_V4_00_NT_2020_005_v120\consSitNFe_v4.00.xsd">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Include="Helpers\NotaFiscal\PacoteLiberacao\PL_009g_V4_00_NT_2020_005_v120\consStatServ_v4.00.xsd">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Include="Helpers\NotaFiscal\PacoteLiberacao\PL_009g_V4_00_NT_2020_005_v120\enviNFe_v4.00.xsd">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Include="Helpers\NotaFiscal\PacoteLiberacao\PL_009g_V4_00_NT_2020_005_v120\inutNFe_v4.00.xsd">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Include="Helpers\NotaFiscal\PacoteLiberacao\PL_009g_V4_00_NT_2020_005_v120\leiauteConsSitNFe_v4.00.xsd">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Include="Helpers\NotaFiscal\PacoteLiberacao\PL_009g_V4_00_NT_2020_005_v120\leiauteConsStatServ_v4.00.xsd">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Include="Helpers\NotaFiscal\PacoteLiberacao\PL_009g_V4_00_NT_2020_005_v120\leiauteInutNFe_v4.00.xsd">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Include="Helpers\NotaFiscal\PacoteLiberacao\PL_009g_V4_00_NT_2020_005_v120\leiauteNFe_v4.00.xsd">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Include="Helpers\NotaFiscal\PacoteLiberacao\PL_009g_V4_00_NT_2020_005_v120\nfe_v4.00.xsd">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Include="Helpers\NotaFiscal\PacoteLiberacao\PL_009g_V4_00_NT_2020_005_v120\procInutNFe_v4.00.xsd">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Include="Helpers\NotaFiscal\PacoteLiberacao\PL_009g_V4_00_NT_2020_005_v120\procNFe_v4.00.xsd">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Include="Helpers\NotaFiscal\PacoteLiberacao\PL_009g_V4_00_NT_2020_005_v120\retConsReciNFe_v4.00.xsd">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Include="Helpers\NotaFiscal\PacoteLiberacao\PL_009g_V4_00_NT_2020_005_v120\retConsSitNFe_v4.00.xsd">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Include="Helpers\NotaFiscal\PacoteLiberacao\PL_009g_V4_00_NT_2020_005_v120\retConsStatServ_v4.00.xsd">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Include="Helpers\NotaFiscal\PacoteLiberacao\PL_009g_V4_00_NT_2020_005_v120\retEnviNFe_v4.00.xsd">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Include="Helpers\NotaFiscal\PacoteLiberacao\PL_009g_V4_00_NT_2020_005_v120\retInutNFe_v4.00.xsd">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Include="Helpers\NotaFiscal\PacoteLiberacao\PL_009g_V4_00_NT_2020_005_v120\tiposBasico_v4.00.xsd">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Include="Helpers\NotaFiscal\PacoteLiberacao\PL_009g_V4_00_NT_2020_005_v120\xmldsig-core-schema_v1.01.xsd">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Include="Helpers\NotaFiscal\PacoteLiberacao\PL_009_V4_00_NT_2020_005_v1_10\consReciNFe_v4.00.xsd">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Include="Helpers\NotaFiscal\PacoteLiberacao\PL_009_V4_00_NT_2020_005_v1_10\consSitNFe_v4.00.xsd">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Include="Helpers\NotaFiscal\PacoteLiberacao\PL_009_V4_00_NT_2020_005_v1_10\consStatServ_v4.00.xsd">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Include="Helpers\NotaFiscal\PacoteLiberacao\PL_009_V4_00_NT_2020_005_v1_10\enviNFe_v4.00.xsd">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Include="Helpers\NotaFiscal\PacoteLiberacao\PL_009_V4_00_NT_2020_005_v1_10\inutNFe_v4.00.xsd">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Include="Helpers\NotaFiscal\PacoteLiberacao\PL_009_V4_00_NT_2020_005_v1_10\leiauteConsSitNFe_v4.00.xsd">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Include="Helpers\NotaFiscal\PacoteLiberacao\PL_009_V4_00_NT_2020_005_v1_10\leiauteConsStatServ_v4.00.xsd">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Include="Helpers\NotaFiscal\PacoteLiberacao\PL_009_V4_00_NT_2020_005_v1_10\leiauteInutNFe_v4.00.xsd">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Include="Helpers\NotaFiscal\PacoteLiberacao\PL_009_V4_00_NT_2020_005_v1_10\leiauteNFe_v4.00.xsd">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Include="Helpers\NotaFiscal\PacoteLiberacao\PL_009_V4_00_NT_2020_005_v1_10\nfe_v4.00.xsd">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Include="Helpers\NotaFiscal\PacoteLiberacao\PL_009_V4_00_NT_2020_005_v1_10\procInutNFe_v4.00.xsd">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Include="Helpers\NotaFiscal\PacoteLiberacao\PL_009_V4_00_NT_2020_005_v1_10\procNFe_v4.00.xsd">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Include="Helpers\NotaFiscal\PacoteLiberacao\PL_009_V4_00_NT_2020_005_v1_10\retConsReciNFe_v4.00.xsd">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Include="Helpers\NotaFiscal\PacoteLiberacao\PL_009_V4_00_NT_2020_005_v1_10\retConsSitNFe_v4.00.xsd">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Include="Helpers\NotaFiscal\PacoteLiberacao\PL_009_V4_00_NT_2020_005_v1_10\retConsStatServ_v4.00.xsd">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Include="Helpers\NotaFiscal\PacoteLiberacao\PL_009_V4_00_NT_2020_005_v1_10\retEnviNFe_v4.00.xsd">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Include="Helpers\NotaFiscal\PacoteLiberacao\PL_009_V4_00_NT_2020_005_v1_10\retInutNFe_v4.00.xsd">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Include="Helpers\NotaFiscal\PacoteLiberacao\PL_009_V4_00_NT_2020_005_v1_10\tiposBasico_v4.00.xsd">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Include="Helpers\NotaFiscal\PacoteLiberacao\PL_009_V4_00_NT_2020_005_v1_10\xmldsig-core-schema_v1.01.xsd">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="AutoMapper" Version="10.1.1" />
		<PackageReference Include="Azure.Storage.Blobs" Version="12.9.1" />
		<PackageReference Include="BarcodeLib" Version="2.4.0" />
		<PackageReference Include="FluentValidation" Version="10.2.3" />
		<PackageReference Include="Hangfire.Core" Version="1.7.24" />
		<PackageReference Include="MediatR" Version="10.0.1" />
		<PackageReference Include="Microsoft.AspNet.SignalR.Core" Version="2.4.3" />
		<PackageReference Include="Microsoft.AspNetCore.Hosting" Version="2.2.7" />
		<PackageReference Include="Microsoft.AspNetCore.Hosting.Abstractions" Version="2.2.0" />
		<PackageReference Include="Microsoft.AspNetCore.Identity" Version="2.2.0" />
		<PackageReference Include="Microsoft.AspNetCore.SignalR.Core" Version="1.1.0" />
		<PackageReference Include="Microsoft.Azure.Management.Fluent" Version="1.37.1" />
		<PackageReference Include="Microsoft.Azure.Management.ResourceManager.Fluent" Version="1.37.1" />
		<PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="5.0.0" />
		<PackageReference Include="Microsoft.Extensions.Configuration.Binder" Version="5.0.0" />
		<PackageReference Include="Microsoft.Extensions.Http" Version="5.0.0" />
		<PackageReference Include="Microsoft.IdentityModel.Tokens" Version="6.11.1" />
		<PackageReference Include="PDFClown" Version="0.1.2" />
		<PackageReference Include="PdfSharpCore" Version="1.3.14" />
		<PackageReference Include="Polly" Version="7.2.3" />
		<PackageReference Include="Refit" Version="6.3.2" />
		<PackageReference Include="Refit.HttpClientFactory" Version="6.3.2" />
		<PackageReference Include="SharpCompress" Version="0.30.0" />
		<PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="6.11.1" />
		<PackageReference Include="System.ServiceModel.Http" Version="4.8.1" />
		<PackageReference Include="System.ServiceModel.Primitives" Version="4.8.1" />
		<PackageReference Include="ExcelDataReader" Version="3.6.0" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\Zendar.Data\Zendar.Data.csproj" />
		<ProjectReference Include="..\Zendar.Integracao\Zendar.Integracao.csproj" />
		<ProjectReference Include="..\Zendar.QueueService\Zendar.QueueService.csproj" />
		<ProjectReference Include="..\Zendar.Shared\Zendar.Shared.csproj" />
	</ItemGroup>

	<ItemGroup>
		<Reference Include="Multiempresa.Data">
			<HintPath>DLL\Multiempresa.Data.dll</HintPath>
		</Reference>
		<Reference Include="Multiempresa.Shared">
			<HintPath>..\Zendar.Data\DLL\Multiempresa.Shared.dll</HintPath>
		</Reference>
		<Reference Include="MultiPay.Portable">
			<HintPath>DLL\MultiPay.Portable.dll</HintPath>
		</Reference>
		<Reference Include="MultiPay.Shared">
			<HintPath>DLL\MultiPay.Shared.dll</HintPath>
		</Reference>
		<Reference Include="System.ServiceModel" />
		<Reference Include="ZendarPackage.NotaFiscal">
			<HintPath>DLL\ZendarPackage.NotaFiscal.dll</HintPath>
		</Reference>
	</ItemGroup>

	<ItemGroup>
		<WCFMetadata Include="Connected Services" />
	</ItemGroup>

	<ItemGroup>
		<Content Update="C:\Users\<USER>\.nuget\packages\zendarpackage.notafiscal\1.0.22\contentFiles\any\net5.0\PacoteLiberacao\Evento_Generico_PL_v1_01\envEvento_v1.00.xsd">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Update="C:\Users\<USER>\.nuget\packages\zendarpackage.notafiscal\1.0.22\contentFiles\any\net5.0\PacoteLiberacao\Evento_Generico_PL_v1_01\leiauteEvento_v1.00.xsd">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Update="C:\Users\<USER>\.nuget\packages\zendarpackage.notafiscal\1.0.22\contentFiles\any\net5.0\PacoteLiberacao\Evento_Generico_PL_v1_01\procEventoNFe_v1.00.xsd">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Update="C:\Users\<USER>\.nuget\packages\zendarpackage.notafiscal\1.0.22\contentFiles\any\net5.0\PacoteLiberacao\Evento_Generico_PL_v1_01\retEnvEvento_v1.00.xsd">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Update="C:\Users\<USER>\.nuget\packages\zendarpackage.notafiscal\1.0.22\contentFiles\any\net5.0\PacoteLiberacao\Evento_Generico_PL_v1_01\tiposBasico_v1.03.xsd">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Update="C:\Users\<USER>\.nuget\packages\zendarpackage.notafiscal\1.0.22\contentFiles\any\net5.0\PacoteLiberacao\Evento_Generico_PL_v1_01\xmldsig-core-schema_v1.01.xsd">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Update="C:\Users\<USER>\.nuget\packages\zendarpackage.notafiscal\1.0.22\contentFiles\any\net5.0\PacoteLiberacao\PL_009_V4_00_NT_2020_005_v1_10\consReciNFe_v4.00.xsd">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Update="C:\Users\<USER>\.nuget\packages\zendarpackage.notafiscal\1.0.22\contentFiles\any\net5.0\PacoteLiberacao\PL_009_V4_00_NT_2020_005_v1_10\consSitNFe_v4.00.xsd">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Update="C:\Users\<USER>\.nuget\packages\zendarpackage.notafiscal\1.0.22\contentFiles\any\net5.0\PacoteLiberacao\PL_009_V4_00_NT_2020_005_v1_10\consStatServ_v4.00.xsd">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Update="C:\Users\<USER>\.nuget\packages\zendarpackage.notafiscal\1.0.22\contentFiles\any\net5.0\PacoteLiberacao\PL_009_V4_00_NT_2020_005_v1_10\enviNFe_v4.00.xsd">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Update="C:\Users\<USER>\.nuget\packages\zendarpackage.notafiscal\1.0.22\contentFiles\any\net5.0\PacoteLiberacao\PL_009_V4_00_NT_2020_005_v1_10\inutNFe_v4.00.xsd">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Update="C:\Users\<USER>\.nuget\packages\zendarpackage.notafiscal\1.0.22\contentFiles\any\net5.0\PacoteLiberacao\PL_009_V4_00_NT_2020_005_v1_10\leiauteConsSitNFe_v4.00.xsd">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Update="C:\Users\<USER>\.nuget\packages\zendarpackage.notafiscal\1.0.22\contentFiles\any\net5.0\PacoteLiberacao\PL_009_V4_00_NT_2020_005_v1_10\leiauteConsStatServ_v4.00.xsd">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Update="C:\Users\<USER>\.nuget\packages\zendarpackage.notafiscal\1.0.22\contentFiles\any\net5.0\PacoteLiberacao\PL_009_V4_00_NT_2020_005_v1_10\leiauteInutNFe_v4.00.xsd">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Update="C:\Users\<USER>\.nuget\packages\zendarpackage.notafiscal\1.0.22\contentFiles\any\net5.0\PacoteLiberacao\PL_009_V4_00_NT_2020_005_v1_10\leiauteNFe_v4.00.xsd">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Update="C:\Users\<USER>\.nuget\packages\zendarpackage.notafiscal\1.0.22\contentFiles\any\net5.0\PacoteLiberacao\PL_009_V4_00_NT_2020_005_v1_10\nfe_v4.00.xsd">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Update="C:\Users\<USER>\.nuget\packages\zendarpackage.notafiscal\1.0.22\contentFiles\any\net5.0\PacoteLiberacao\PL_009_V4_00_NT_2020_005_v1_10\procInutNFe_v4.00.xsd">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Update="C:\Users\<USER>\.nuget\packages\zendarpackage.notafiscal\1.0.22\contentFiles\any\net5.0\PacoteLiberacao\PL_009_V4_00_NT_2020_005_v1_10\procNFe_v4.00.xsd">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Update="C:\Users\<USER>\.nuget\packages\zendarpackage.notafiscal\1.0.22\contentFiles\any\net5.0\PacoteLiberacao\PL_009_V4_00_NT_2020_005_v1_10\retConsReciNFe_v4.00.xsd">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Update="C:\Users\<USER>\.nuget\packages\zendarpackage.notafiscal\1.0.22\contentFiles\any\net5.0\PacoteLiberacao\PL_009_V4_00_NT_2020_005_v1_10\retConsSitNFe_v4.00.xsd">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Update="C:\Users\<USER>\.nuget\packages\zendarpackage.notafiscal\1.0.22\contentFiles\any\net5.0\PacoteLiberacao\PL_009_V4_00_NT_2020_005_v1_10\retConsStatServ_v4.00.xsd">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Update="C:\Users\<USER>\.nuget\packages\zendarpackage.notafiscal\1.0.22\contentFiles\any\net5.0\PacoteLiberacao\PL_009_V4_00_NT_2020_005_v1_10\retEnviNFe_v4.00.xsd">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Update="C:\Users\<USER>\.nuget\packages\zendarpackage.notafiscal\1.0.22\contentFiles\any\net5.0\PacoteLiberacao\PL_009_V4_00_NT_2020_005_v1_10\retInutNFe_v4.00.xsd">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Update="C:\Users\<USER>\.nuget\packages\zendarpackage.notafiscal\1.0.22\contentFiles\any\net5.0\PacoteLiberacao\PL_009_V4_00_NT_2020_005_v1_10\tiposBasico_v4.00.xsd">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Update="C:\Users\<USER>\.nuget\packages\zendarpackage.notafiscal\1.0.22\contentFiles\any\net5.0\PacoteLiberacao\PL_009_V4_00_NT_2020_005_v1_10\xmldsig-core-schema_v1.01.xsd">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
	</ItemGroup>

	<ItemGroup>
		<None Update="DLL\MultiPay.Portable.dll">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="DLL\MultiPay.Shared.dll">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Helpers\NotaFiscal\PacoteLiberacao\PL_009o_NT2024_001_v100\consReciNFe_v4.00.xsd">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Helpers\NotaFiscal\PacoteLiberacao\PL_009o_NT2024_001_v100\consSitNFe_v4.00.xsd">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Helpers\NotaFiscal\PacoteLiberacao\PL_009o_NT2024_001_v100\consStatServ_v4.00.xsd">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Helpers\NotaFiscal\PacoteLiberacao\PL_009o_NT2024_001_v100\enviNFe_v4.00.xsd">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Helpers\NotaFiscal\PacoteLiberacao\PL_009o_NT2024_001_v100\inutNFe_v4.00.xsd">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Helpers\NotaFiscal\PacoteLiberacao\PL_009o_NT2024_001_v100\leiauteConsSitNFe_v4.00.xsd">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Helpers\NotaFiscal\PacoteLiberacao\PL_009o_NT2024_001_v100\leiauteConsStatServ_v4.00.xsd">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Helpers\NotaFiscal\PacoteLiberacao\PL_009o_NT2024_001_v100\leiauteInutNFe_v4.00.xsd">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Helpers\NotaFiscal\PacoteLiberacao\PL_009o_NT2024_001_v100\leiauteNFe_v4.00.xsd">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Helpers\NotaFiscal\PacoteLiberacao\PL_009o_NT2024_001_v100\nfe_v4.00.xsd">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Helpers\NotaFiscal\PacoteLiberacao\PL_009o_NT2024_001_v100\procInutNFe_v4.00.xsd">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Helpers\NotaFiscal\PacoteLiberacao\PL_009o_NT2024_001_v100\procNFe_v4.00.xsd">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Helpers\NotaFiscal\PacoteLiberacao\PL_009o_NT2024_001_v100\retConsReciNFe_v4.00.xsd">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Helpers\NotaFiscal\PacoteLiberacao\PL_009o_NT2024_001_v100\retConsSitNFe_v4.00.xsd">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Helpers\NotaFiscal\PacoteLiberacao\PL_009o_NT2024_001_v100\retConsStatServ_v4.00.xsd">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Helpers\NotaFiscal\PacoteLiberacao\PL_009o_NT2024_001_v100\retEnviNFe_v4.00.xsd">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Helpers\NotaFiscal\PacoteLiberacao\PL_009o_NT2024_001_v100\retInutNFe_v4.00.xsd">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Helpers\NotaFiscal\PacoteLiberacao\PL_009o_NT2024_001_v100\tiposBasico_v4.00.xsd">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Helpers\NotaFiscal\PacoteLiberacao\PL_009o_NT2024_001_v100\xmldsig-core-schema_v1.01.xsd">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
	</ItemGroup>

	<ItemGroup>
		<Folder Include="DI\" />
	</ItemGroup>

</Project>
