﻿using AutoMapper;
using Multiempresa.Data.Repositories.FaturamentoRepositories;
using Multiempresa.Shared.Constants;
using Multiempresa.Shared.Enums;
using Multiempresa.Shared.Helpers.Convertores;
using Multiempresa.Shared.Helpers.Formatadores;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Zendar.Business.Helpers;
using Zendar.Business.Interfaces;
using Zendar.Business.Interfaces.Services;
using Zendar.Business.Interfaces.Services.Fiscal;
using Zendar.Business.Services.IntegracaoServices.IntegracaoService;
using Zendar.Business.Services.V1.IntegracaoServices.SincronizacaoServices.Trigger;
using Zendar.Business.Validations.Models;
using Zendar.Business.Validations.V1.Models;
using Zendar.Business.ViewModels;
using Zendar.Business.ViewModels.Integracao.STi3Dashboard;
using Zendar.Business.ViewModels.V1.Integracao.FrenteCaixa;
using Zendar.Business.ViewModels.V1.Integracao.PdvAutonomo;
using Zendar.Business.ViewModels.V1.Loja;
using Zendar.Data.Enums;
using Zendar.Data.Enums.Integracao;
using Zendar.Data.Enums.Stargate;
using Zendar.Data.Helpers;
using Zendar.Data.Interfaces;
using Zendar.Data.Interfaces.Aplicacao;
using Zendar.Data.Models.Aplicacao;
using Zendar.Data.Resources.Mensagens;
using Zendar.Data.Resources.Validacoes;
using Zendar.Data.ViewModels;
using ZendarPackage.NotaFiscal.Enums;

namespace Zendar.Business.Services
{
    public class LojaService : BaseService, ILojaService
    {
        private readonly ILojaRepository _lojaRepository;
        private readonly IAspNetUserInfo _aspNetUserInfo;
        private readonly IMapper _mapper;
        private readonly IZendarTriggerService _zendarTriggerService;
        private readonly IStorageService _storageService;
        private readonly ILogAuditoriaService _logAuditoriaService;
        private readonly IPaisService _paisService;
        private readonly ICidadeService _cidadeService;
        private readonly ICertificadoDigitalService _certificadoDigitalService;
        private readonly IDatabaseTransaction _databaseTransaction;
        private readonly ITabelaPrecoLojaRepository _tabelaPrecoLojaRepository;
        private readonly IContaFinanceiraRepository _contaFinanceiraRepository;
        private readonly ICacheService _cacheService;
        private readonly ILocalEstoqueRepository _localEstoqueRepository;
        private readonly ILojaImpressaoRelatorioService _lojaImpressaoRelatorioService;
        private readonly ILojaFiscalService _lojaFiscalService;
        private readonly ILojaMultaJurosService _lojaMultaJurosService;
        private readonly IDashboardInicialService _dashboardInicialService;
        private readonly IFaturamentoRepository _faturamentoRepository;
        private readonly IIntegracaoService _integracaoService;

        public LojaService(INotificador notificador,
                          ILojaRepository lojaRepository,
                          ILogAuditoriaService logAuditoriaService,
                          IAspNetUserInfo aspNetUserInfo,
                          IStorageService storageService,
                          IPaisService paisService,
                          ICidadeService cidadeService,
                          ICertificadoDigitalService certificadoDigitalService,
                          IMapper mapper,
                          IZendarTriggerService zendarTriggerService,
                          IDatabaseTransaction databaseTransaction,
                          ITabelaPrecoLojaRepository tabelaPrecoLojaRepository,
                          IContaFinanceiraRepository contaFinanceiraRepository,
                          ILocalEstoqueRepository localEstoqueRepository,
                          ICacheService cacheService,
                          ILojaImpressaoRelatorioService lojaImpressaoRelatorioService,
                          ILojaFiscalService lojaFiscalService,
                          ILojaMultaJurosService lojaMultaJurosService,
                          IDashboardInicialService dashboardInicialService,
                          IFaturamentoRepository faturamentoRepository,
                          IIntegracaoService integracaoService) : base(notificador)
        {
            _lojaRepository = lojaRepository;
            _aspNetUserInfo = aspNetUserInfo;
            _logAuditoriaService = logAuditoriaService;
            _storageService = storageService;
            _paisService = paisService;
            _cidadeService = cidadeService;
            _certificadoDigitalService = certificadoDigitalService;
            _mapper = mapper;
            _zendarTriggerService = zendarTriggerService;
            _databaseTransaction = databaseTransaction;
            _tabelaPrecoLojaRepository = tabelaPrecoLojaRepository;
            _contaFinanceiraRepository = contaFinanceiraRepository;
            _cacheService = cacheService;
            _localEstoqueRepository = localEstoqueRepository;
            _lojaImpressaoRelatorioService = lojaImpressaoRelatorioService;
            _lojaFiscalService = lojaFiscalService;
            _lojaMultaJurosService = lojaMultaJurosService;
            this._dashboardInicialService = dashboardInicialService;
            _faturamentoRepository = faturamentoRepository;
            _integracaoService = integracaoService;
        }

        public async Task<List<LojaEnderecoListaViewModel>> ListarLojaComEnderecoELocalEstoque()
        {
            return await _lojaRepository.ListarLojaComEnderecoELocalEstoque();
        }

        public async Task<List<LojaLocalEstoqueListaViewModel>> ListarLojaTransferenciaEstoque()
        {
            return await _lojaRepository.ListarLojaTransferenciaEstoque();
        }

        public async Task<List<LojaUsuarioListaViewModel>> ListarLojaUsuario(Guid usuarioId)
        {
            var lojas = await _lojaRepository.ObterLojasPorUsuarioComPlanoEndereco(usuarioId);

            return lojas.Select(l => new LojaUsuarioListaViewModel
            {
                Id = l.Id,
                Fantasia = l.Fantasia,
                Endereco = FormatarEndereco.FormatarLogradouroNumeroBairro(l.Logradouro, l.Numero, l.Bairro),
                Logradouro = l.Logradouro,
                NumeroBairro = FormatarEndereco.FormatarNumeroBairro(l.Numero, l.Bairro),
                Cidade = l.Cidade.CidadeUf,
                Bloqueada = l.Bloqueada(DateTime.UtcNow)
            }).ToList();
        }

        public async Task<Loja> ObterLojaComDataBloqueioPlano(Guid lojaId)
        {
            return await _lojaRepository.FirstOrDefaultAsNoTracking(l => l.Id == lojaId, l => new Loja
            {
                LojaServicos = l.LojaServicos.Where(s => s.TipoServico == TipoServicoStargate.PLANO).Select(s => new LojaServicos
                {
                    DataBloqueio = s.DataBloqueio
                }).ToList()
            });
        }

        public async Task<ReferenciaServicoStargate> ObterPlano(Guid lojaId)
        {
            var loja = await _lojaRepository.FirstOrDefaultAsNoTracking(l => l.Id == lojaId, l => new Loja
            {
                LojaServicos = l.LojaServicos.Where(s => s.TipoServico == TipoServicoStargate.PLANO).Select(s => new LojaServicos
                {
                    ReferenciaServico = s.ReferenciaServico
                }).ToList()
            });

            return loja.Plano();
        }

        public async Task<List<LojaVendedorListaViewModel>> ListarLojaVendedor(Guid? vendedorId)
        {
            return await _lojaRepository.ListarLojaVendedor(vendedorId);
        }

        public async Task<List<LojaTabelaPrecoViewModel>> ListarLojaTabelaPreco(Guid? tabelaPrecoId)
        {
            return await _lojaRepository.ListarLojaTabelaPreco(tabelaPrecoId);
        }

        public async Task<List<LojaAtivaListaViewModel>> ListarTodasLojasAtivas()
        {
            return await _lojaRepository.ListarTodasLojas();
        }

        public async Task<LojaRegraFiscalViewModel> ObterRegraFiscalLoja(Guid lojaId)
        {
            return await _lojaRepository.ObterRegraFiscalLoja(lojaId);
        }

        public async Task<LojaNumeroSerieViewModel> ObterNumerosSerie()
        {
            var loja = await _lojaRepository.ObterNumerosSerie(_aspNetUserInfo.LojaId.Value);

            return new LojaNumeroSerieViewModel
            {
                NFCeNumeroSerie = loja.LojaFiscal.NFCeNumeroSerie,
                NFeNumeroSerie = loja.LojaFiscal.NFeNumeroSerie
            };
        }

        public async Task<LojaInformacoesFiscaisViewModel> ObterInformacoesFiscais()
        {
            var loja = await _lojaRepository.ObterInformacoesFiscais(_aspNetUserInfo.LojaId.Value);

            return new LojaInformacoesFiscaisViewModel
            {
                RegimeTributario = loja.LojaFiscal.RegimeTributario,
                IcmsAproveitamentoAliquota = loja.LojaFiscal.IcmsAproveitamentoAliquota,
                NFeNumeroSerie = loja.LojaFiscal.NFeNumeroSerie,
                NFCeNumeroSerie = loja.LojaFiscal.NFCeNumeroSerie,
                PaisId = loja.PaisId,
                EstadoId = loja.Cidade.EstadoId,
                CodigoEstado = loja.Cidade.Estado.Codigo
            };
        }

        public GridPaginadaRetorno<LojaPaginadaViewModel> ListarPaginado(GridPaginadaConsulta gridPaginada, string fantasia)
        {
            return _lojaRepository.ListarPaginado(gridPaginada, fantasia);
        }

        public async Task Alterar(LojaViewModel lojaViewModel)
        {
            var lojaMapper = _mapper.Map<Loja>(lojaViewModel);

            if (!ExecutarValidacao(new LojaValidation(), lojaMapper)) return;

            var lojaParaAlteracao = await _lojaRepository.ObterParaAlteracao(lojaViewModel.Id);

            var lojaImpressao = new LojaImpressaoRelatorioViewModel
            {
                LojaId = lojaParaAlteracao.Id
            };

            if (lojaParaAlteracao.LojaImpressaoRelatorio.LogoQuadrado != _storageService.ObterCaminhoArquivoBancoDados(StorageContaArmazenamento.Imagens, lojaViewModel.LogoQuadrado))
            {
                if (!string.IsNullOrEmpty(lojaParaAlteracao.LojaImpressaoRelatorio.LogoQuadrado))
                {
                    await _storageService.Excluir(StorageContaArmazenamento.Imagens, lojaParaAlteracao.LojaImpressaoRelatorio.LogoQuadrado);
                    lojaImpressao.LogoQuadrado = null;
                }

                if (!string.IsNullOrEmpty(lojaViewModel.LogoQuadrado))
                {
                    var caminhoArquivo = string.Format(CaminhoArquivosStorage.CaminhoLogoQuadrado, Guid.NewGuid());


                    await _storageService.Upload(
                                StorageContaArmazenamento.Imagens,
                                TipoArquivo.OUTROS,
                                caminhoArquivo,
                                lojaViewModel.LogoQuadrado);

                    lojaImpressao.LogoQuadrado = caminhoArquivo;
                }
                else
                {
                    lojaImpressao.LogoQuadrado = null;
                }
            }
            else
            {
                lojaImpressao.LogoQuadrado = lojaParaAlteracao.LojaImpressaoRelatorio.LogoQuadrado;
            }

            if (lojaParaAlteracao.LojaImpressaoRelatorio.LogoRetangular != _storageService.ObterCaminhoArquivoBancoDados(StorageContaArmazenamento.Imagens, lojaViewModel.LogoRetangular))
            {
                if (!string.IsNullOrEmpty(lojaParaAlteracao.LojaImpressaoRelatorio.LogoRetangular))
                {
                    await _storageService.Excluir(StorageContaArmazenamento.Imagens, lojaParaAlteracao.LojaImpressaoRelatorio.LogoRetangular);
                    lojaImpressao.LogoRetangular = null;
                }

                if (!string.IsNullOrEmpty(lojaViewModel.LogoRetangular))
                {
                    var caminhoArquivo = string.Format(CaminhoArquivosStorage.CaminhoLogoRetangular, Guid.NewGuid());

                    await _storageService.Upload(
                                                  StorageContaArmazenamento.Imagens,
                                                  TipoArquivo.OUTROS,
                                                  caminhoArquivo,
                                                  lojaViewModel.LogoRetangular);

                    lojaImpressao.LogoRetangular = caminhoArquivo;
                }
                else
                {
                    lojaImpressao.LogoRetangular = null;
                }
            }
            else
            {
                lojaImpressao.LogoRetangular = lojaParaAlteracao.LojaImpressaoRelatorio.LogoRetangular;
            }

            var lojaFiscal = _mapper.Map<LojaFiscalViewModel>(lojaParaAlteracao.LojaFiscal);
            lojaFiscal = _mapper.Map(lojaViewModel, lojaFiscal);
            lojaFiscal.LojaId = lojaViewModel.Id;

            if (lojaViewModel.TipoCertificado == TipoCertificado.A1)
            {
                string senhaAtual = lojaParaAlteracao.LojaFiscal.SenhaCertificadoDigital;
                string senhaNova = lojaViewModel.SenhaCertificadoDigital;
                lojaFiscal.AlterarSenhaCertificadoDigital(senhaAtual, senhaNova);

                if (!string.IsNullOrEmpty(lojaViewModel.Arquivo))
                {
                    if (lojaParaAlteracao.LojaFiscal.CaminhoCertificadoDigital != null)
                    {
                        await _storageService.Excluir(StorageContaArmazenamento.Certificados, string.Format(CaminhoArquivosStorage.CaminhoCertificadoDigital, lojaParaAlteracao.CpfCnpj.Replace("/", "").Replace(".", "").Replace("-", ""), lojaParaAlteracao.LojaFiscal.CaminhoCertificadoDigital));
                    }

                    if (lojaViewModel.CaminhoCertificadoDigital != null)
                    {

                        await _storageService.Upload(
                                            StorageContaArmazenamento.Certificados,
                                            TipoArquivo.OUTROS,
                                            string.Format(CaminhoArquivosStorage.CaminhoCertificadoDigital, lojaViewModel.Cnpj.Replace("/", "").Replace(".", "").Replace("-", ""), lojaViewModel.CaminhoCertificadoDigital),
                                            lojaViewModel.Arquivo);
                    }
                    lojaFiscal.CaminhoCertificadoDigital = string.IsNullOrEmpty(lojaViewModel.CaminhoCertificadoDigital) ? null : lojaViewModel.CaminhoCertificadoDigital;
                }
            }
            else
            {
                if (lojaParaAlteracao.LojaFiscal.CaminhoCertificadoDigital != null)
                {
                    await _storageService.Excluir(StorageContaArmazenamento.Certificados, string.Format(CaminhoArquivosStorage.CaminhoCertificadoDigital, lojaParaAlteracao.CpfCnpj.Replace("/", "").Replace(".", "").Replace("-", ""), lojaParaAlteracao.LojaFiscal.CaminhoCertificadoDigital));
                }

                lojaFiscal.CaminhoCertificadoDigital = null;
                lojaFiscal.SenhaCertificadoDigital = null;
            }

            lojaParaAlteracao.RazaoSocial = lojaMapper.RazaoSocial;
            lojaParaAlteracao.Fantasia = lojaMapper.Fantasia;
            lojaParaAlteracao.CpfCnpj = lojaMapper.CpfCnpj;
            lojaParaAlteracao.IEIsento = lojaMapper.IEIsento;
            lojaParaAlteracao.Logradouro = lojaMapper.Logradouro;
            lojaParaAlteracao.Numero = lojaMapper.Numero;
            lojaParaAlteracao.Complemento = string.IsNullOrEmpty(lojaMapper.Complemento) ? null : lojaMapper.Complemento;
            lojaParaAlteracao.Bairro = lojaMapper.Bairro;
            lojaParaAlteracao.Cep = lojaMapper.Cep;
            lojaParaAlteracao.CidadeId = lojaMapper.CidadeId;
            lojaParaAlteracao.PaisId = lojaMapper.PaisId;
            lojaParaAlteracao.TelefoneWhatsapp = lojaMapper.TelefoneWhatsapp;
            lojaParaAlteracao.CelularWhatsapp = lojaMapper.CelularWhatsapp;
            lojaParaAlteracao.Telefone = string.IsNullOrEmpty(lojaMapper.Telefone) ? null : lojaMapper.Telefone;
            lojaParaAlteracao.Celular = string.IsNullOrEmpty(lojaMapper.Celular) ? null : lojaMapper.Celular;
            lojaParaAlteracao.Contato = string.IsNullOrEmpty(lojaMapper.Contato) ? null : lojaMapper.Contato;
            lojaParaAlteracao.Email = string.IsNullOrEmpty(lojaMapper.Email) ? null : lojaMapper.Email;
            lojaParaAlteracao.SitePerfil = string.IsNullOrEmpty(lojaMapper.SitePerfil) ? null : lojaMapper.SitePerfil;
            lojaParaAlteracao.CodigosSegurancaContribuinte = lojaMapper.CodigosSegurancaContribuinte;
            lojaParaAlteracao.HorariosFuncionamento = lojaMapper.HorariosFuncionamento;
            lojaParaAlteracao.RgInscricaoEstadual = string.IsNullOrEmpty(lojaMapper.RgInscricaoEstadual) ? null : lojaMapper.RgInscricaoEstadual;
            lojaParaAlteracao.DataHoraUltimaAlteracao = DateTime.UtcNow;
            if (lojaMapper.IEIsento) lojaParaAlteracao.RgInscricaoEstadual = "ISENTO";


            _databaseTransaction.BeginTransaction();

            await _lojaRepository.SaveChanges();

            await _lojaFiscalService.Alterar(lojaFiscal);

            await _lojaImpressaoRelatorioService.Alterar(lojaImpressao);

            _databaseTransaction.Commit();

            await _logAuditoriaService
                  .Inserir(new LogAuditoriaInserirViewModel(LogAuditoriaTela.LOJA,
                                                            LogAuditoriaOperacao.ALTERAR, $"Nome: {lojaParaAlteracao.RazaoSocial}"));
            #region [Integracoes]
            await AtualizarAmbienteFiscalIntegracao<FrenteCaixaConfiguracaoViewModel>(IdentificacaoIntegracao.FRENTE_CAIXA, lojaFiscal);
            await AtualizarAmbienteFiscalIntegracao<PdvAutonomoConfiguracaoViewModel>(IdentificacaoIntegracao.CAIXA_PDV_AUTONOMO, lojaFiscal);
            #endregion

            #region Trigger

            await _zendarTriggerService.ExecuteGuid(lojaParaAlteracao.Id,
                                                TabelaTrigger.LOJA,
                                                OperacaoTrigger.ALTERAR);

            #endregion
        }

        public async Task<LojaViewModel> Obter(Guid id)
        {
            var loja = await _lojaRepository.Obter(id);

            if (loja == null)
            {
                NotificarAviso(ResourceMensagem.ClienteFornecedorService_ClienteNaoEncontrado);
                return null;
            }

            var lojaViewModel = _mapper.Map<LojaViewModel>(loja);
            // Mapeando informações fiscais
            lojaViewModel = _mapper.Map(loja.LojaFiscal, lojaViewModel);

            lojaViewModel.Id = loja.Id;
            lojaViewModel.DataHoraCadastro = loja.DataHoraCadastro;
            lojaViewModel.DataHoraUltimaAlteracao = loja.DataHoraUltimaAlteracao;
            lojaViewModel.SenhaCertificadoDigital = null;
            lojaViewModel.ReferenciaServico = await ObterPlano(loja.Id);

            ConverterDate.AplicarUTC(_aspNetUserInfo.TimezoneOffset.Value, lojaViewModel);

            if (!string.IsNullOrEmpty(loja.LojaFiscal.CaminhoCertificadoDigital) || !string.IsNullOrEmpty(loja.LojaFiscal.SenhaCertificadoDigital))
            {
                try
                {
                    lojaViewModel.DataValidadeCertificadoDigital = _certificadoDigitalService
                        .ObterCertificado(loja.LojaFiscal.CaminhoCertificadoDigital, loja.LojaFiscal.SenhaCertificadoDigital, loja.CpfCnpj)
                        .NotAfter;
                }
                catch { }
            }

            lojaViewModel.RegraFiscalPadraoLabel = loja.LojaFiscal.RegraFiscalPadrao.Nome;

            var pais = await _paisService.Obter(lojaViewModel.PaisId);
            lojaViewModel.PaisId = pais.Id;
            lojaViewModel.PaisNome = pais.Nome;

            var cidade = await _cidadeService.Obter(loja.Cidade.Id, loja.Cidade.CodigoIBGE);
            lojaViewModel.CidadeNome = loja.Cidade.Nome;
            lojaViewModel.EstadoNome = cidade.EstadoNome;


            var contabilistaPais = await _paisService.Obter(loja.LojaFiscal.ContabilistaPaisId);
            lojaViewModel.ContabilistaPaisId = contabilistaPais.Id;
            lojaViewModel.ContabilistaPaisNome = contabilistaPais.Nome;

            var contabilistaCidade = await _cidadeService.Obter(loja.LojaFiscal.ContabilistaCidade.Id, loja.LojaFiscal.ContabilistaCidade.CodigoIBGE);
            lojaViewModel.ContabilistaCidadeNome = loja.LojaFiscal.ContabilistaCidade.Nome;
            lojaViewModel.ContabilistaEstadoNome = contabilistaCidade.EstadoNome;


            lojaViewModel.LogoQuadrado = _storageService.ObterUrlArquivoAcessoTemporario(StorageContaArmazenamento.Imagens, loja.LojaImpressaoRelatorio.LogoQuadrado);
            lojaViewModel.LogoRetangular = _storageService.ObterUrlArquivoAcessoTemporario(StorageContaArmazenamento.Imagens, loja.LojaImpressaoRelatorio.LogoRetangular);

            return lojaViewModel;
        }

        public async Task<LojaCidadeViewModel> ObterCidade()
        {
            var loja = await _lojaRepository.ObterCidade(_aspNetUserInfo.LojaId.Value);

            return new LojaCidadeViewModel
            {
                CidadeId = loja.Cidade.Id,
                CodigoIbge = loja.Cidade.CodigoIBGE
            };
        }

        public async Task Excluir(Guid id)
        {
            var loja = await _lojaRepository.FirstOrDefault(x => x.Id.Equals(id), l => new Loja
            {
                Id = l.Id,
                RazaoSocial = l.RazaoSocial,
                ContasFinanceiras = l.ContasFinanceiras,
                TabelaPrecoLojas = l.TabelaPrecoLojas
            });

            if (loja == null)
            {
                NotificarAviso(ResourceMensagem.Geral_LojaNaoEncontrada);
                return;
            }

            _databaseTransaction.BeginTransaction();

            await _contaFinanceiraRepository.DeleteRange(loja.ContasFinanceiras.ToList());

            await _tabelaPrecoLojaRepository.DeleteRange(loja.TabelaPrecoLojas.ToList());

            await _lojaImpressaoRelatorioService.Excluir(loja.Id);

            await _lojaFiscalService.Excluir(id);

            await _lojaMultaJurosService.Excluir(id);

            await _lojaRepository.Delete(id);

            _databaseTransaction.Commit();

            await _logAuditoriaService
                  .Inserir(new LogAuditoriaInserirViewModel(LogAuditoriaTela.LOJA, LogAuditoriaOperacao.REMOVER, $"Nome: {loja.RazaoSocial}"));

            #region Trigger

            await _zendarTriggerService.ExecuteGuid(id,
                                                TabelaTrigger.LOJA,
                                                OperacaoTrigger.REMOVER);

            #endregion
        }

        public async Task<string> ObterEmailContabilista()
        {
            return await _lojaRepository.ObterEmailContabilista(_aspNetUserInfo.LojaId.Value);
        }

        public async Task<LojaInformacoesFinalizarVendaViewModel> ObterInformacoesFinalizarVenda()
        {
            var loja = await _lojaRepository.ObterInformacoesFinalizarVenda(_aspNetUserInfo.LojaId.Value);

            return new LojaInformacoesFinalizarVendaViewModel
            {
                EmitirNFe = loja.LojaFiscal.EmitirNFe,
                EmitirNFCe = loja.LojaFiscal.EmitirNFCe,
                NFCeExigirCNPJCPF = loja.LojaFiscal.NFCeExigirCNPJCPF,
                NFCeValorExigenciaCNPJCPF = loja.LojaFiscal.NFCeValorExigenciaCNPJCPF,
                PaisId = loja.PaisId,
            };
        }

        public async Task<bool> ObterConfigEmitirNFe()
        {
            return await _lojaRepository.ObterConfigEmitirNFe(_aspNetUserInfo.LojaId.Value);
        }

        public async Task<AmbienteFiscal> ObterAmbienteFiscal()
        {
            return await _lojaRepository.ObterAmbienteFiscal(_aspNetUserInfo.LojaId.Value);
        }

        public async Task<string> GerarPinA3(Guid lojaId)
        {
            var pin = PINGenerator.Generate();
            while (pin.Length < 6)
            {
                pin = PINGenerator.Generate();
            }

            var chave = $"pin_a3_{pin}";

            while (await _cacheService.Existe(chave))
            {
                pin = PINGenerator.Generate();
                chave = $"pin_a3_{pin}";
            }

            var loja = await _lojaRepository.FirstOrDefaultAsNoTracking(x => x.Id == lojaId, s => new Loja { CpfCnpj = s.CpfCnpj, Fantasia = s.Fantasia });

            await _cacheService.AdicionarAsync(new CacheModel
            {
                Chave = chave,
                Valor = new ZendarDesktopRetornoPinViewModel
                {
                    Dominio = _aspNetUserInfo.HostUrl,
                    Fantasia = loja.Fantasia,
                    Cnpj = FormatarTexto.ManterSomenteNumeros(loja.CpfCnpj),
                    LojaId = lojaId.ToString()
                },
                ExpirarAposInatividade = TimeSpan.FromMinutes(30),
                ExpirarEm = DateTime.UtcNow.AddMinutes(30)
            }, false);

            return pin;
        }

        public async Task<ZendarDesktopRetornoPinViewModel> ObterInformacoesPinA3(string pin)
        {
            return await _cacheService.ObterAsync<ZendarDesktopRetornoPinViewModel>($"pin_a3_{pin}", false);
        }

        public async Task<string> ObterNomeFantasia(Guid lojaId)
        {
            var loja = await _lojaRepository.FirstOrDefaultAsNoTracking(x => x.Id == _aspNetUserInfo.LojaId, x => new Loja
            {
                Fantasia = x.Fantasia
            });

            return loja?.Fantasia;
        }

        public async Task<string> ObterRazaoSocial(Guid lojaId)
        {
            var loja = await _lojaRepository.FirstOrDefaultAsNoTracking(x => x.Id == _aspNetUserInfo.LojaId, x => new Loja
            {
                RazaoSocial = x.RazaoSocial
            });

            return loja?.RazaoSocial;
        }

        public async Task<ObterVencimentosViewModel> ObterVencimentos()
        {
            var loja = await _lojaRepository.FirstOrDefaultAsNoTracking(l => l.Id == _aspNetUserInfo.LojaId, loja => new()
            {
                CpfCnpj = loja.CpfCnpj,
                LojaFiscal = new LojaFiscal { SenhaCertificadoDigital = loja.LojaFiscal.SenhaCertificadoDigital, CaminhoCertificadoDigital = loja.LojaFiscal.CaminhoCertificadoDigital },
                LojaServicos = loja.LojaServicos,
                AssinaturaId = loja.AssinaturaId,
            });

            try
            {
                DateTime? vencimentoCertificado = null;
                if (!string.IsNullOrEmpty(loja.LojaFiscal.CaminhoCertificadoDigital))
                {
                    vencimentoCertificado = _certificadoDigitalService.ObterCertificado(loja.LojaFiscal.CaminhoCertificadoDigital, loja.LojaFiscal.SenhaCertificadoDigital, loja.CpfCnpj)?.NotAfter ?? null;
                }
                var dataBloqueio = loja.LojaServicos.First(x => x.TipoServico == TipoServicoStargate.PLANO).DataBloqueio;
                var linkCobranca = "";

                return new()
                {
                    DataVencimentoCertificado = vencimentoCertificado,
                    DataBloqueio = dataBloqueio,
                    LinkCobranca = string.Empty,
                };
            }
            catch
            {
                //desconsiderar caso não localize/recupere o blob 
                return null;
            }
        }

        public async Task<Loja> ObterInformacoesParaComunicarComSefaz(Guid lojaId)
        {
            var loja = await _lojaRepository.FirstOrDefaultAsNoTracking(l => l.Id == lojaId,
            l => new Loja
            {
                CpfCnpj = l.CpfCnpj,
                LojaFiscal = new LojaFiscal
                {
                    TipoCertificado = l.LojaFiscal.TipoCertificado,
                    SenhaCertificadoDigital = l.LojaFiscal.SenhaCertificadoDigital,
                    TipoAmbienteFiscal = l.LojaFiscal.TipoAmbienteFiscal,
                },
                Cidade = new Cidade
                {
                    Estado = new Estado
                    {
                        Codigo = l.Cidade.Estado.Codigo
                    }
                }
            });

            if (loja == null)
            {
                NotificarAviso(ResourceMensagem.Geral_LojaNaoEncontrada);
                return null;
            }

            return loja;
        }

        public async Task<List<LojaContaFinanceiraViewModel>> ListarLojasComContasFinanceiras()
        {
            var lojas = await _lojaRepository.ListarLojasComContasFinanceiras();

            return lojas.Select(x => new LojaContaFinanceiraViewModel
            {
                Id = x.Id,
                Fantasia = x.Fantasia,
                Endereco = FormatarEndereco.FormatarLogradouroNumeroBairro(x.Logradouro, x.Numero, x.Bairro),
                Cidade = x.Cidade.CidadeUf,
                Plano = x.Plano(),
                ContaFinanceiras = x.ContasFinanceiras.Select(y => new ContasFinanceirasLojaViewModel
                {
                    Id = y.Id,
                    Nome = y.Nome,
                }).ToList()
            }).ToList();
        }

        public async Task<List<Loja>> ObterLojasComLocalEstoquePadrao()
        {
            var lojas = await _lojaRepository.FindAllSelectAsNoTracking(x => new Loja
            {
                Id = x.Id,
                LocaisEstoque = x.LocaisEstoque.Where(l => l.PadraoSistema).Select(l => new LocalEstoque { Id = l.Id }).ToList()
            });

            return lojas.ToList();
        }

        public async Task<List<ListaLojaViewModel>> ListarLojasParaSti3Dashboard()
        {
            var lojas = await _lojaRepository.ListarLojasParaSti3Dashboard();

            return lojas.Select(x => new ListaLojaViewModel
            {
                Id = x.Id,
                NomeFantasia = x.Fantasia,
                Cidade = x.Cidade.Nome,
                Uf = x.Cidade.Estado.Sigla,
                DataHoraUltimaSincronizacao = DateTime.UtcNow
            }).ToList();
        }

        public async Task<Loja> ObterInformacoesParaGerarManifesto()
        {
            return await _lojaRepository.FirstOrDefaultAsNoTracking(x => x.Id == _aspNetUserInfo.LojaId.Value, x => new Loja
            {
                CpfCnpj = x.CpfCnpj,
                Cidade = new Cidade
                {
                    Estado = new Estado
                    {
                        Codigo = x.Cidade.Estado.Codigo,
                        //EstadoNotaFiscalAutorizadores = x.Cidade.Estado.EstadoNotaFiscalAutorizadores.Where(a => a.TipoAutorizador == AutorizadorFiscal.NORMAL)
                        //                                                                             .Select(e => new EstadoNotaFiscalAutorizador
                        //                                                                             {
                        //                                                                                 NotaFiscalAutorizador = e.NotaFiscalAutorizador
                        //                                                                             }).ToList()
                    }
                },
                LojaFiscal = new LojaFiscal
                {
                    //SenhaCertificadoDigital = x.LojaFiscal.SenhaCertificadoDigital,
                    //CaminhoCertificadoDigital = x.LojaFiscal.CaminhoCertificadoDigital,
                    TipoAmbienteFiscal = x.LojaFiscal.TipoAmbienteFiscal,
                    NSU = x.LojaFiscal.NSU,
                    TipoCertificado = x.LojaFiscal.TipoCertificado,
                    UltimaConsultaDFe = x.LojaFiscal.UltimaConsultaDFe
                }
            });
        }

        public async Task<List<IdNomeViewModel>> ListarSelect()
        {
            var lojas = await _lojaRepository.ListarSelect();

            if (lojas == null || lojas.Count() == 0)
                return new();

            return lojas.Select(x => new IdNomeViewModel
            {
                Id = x.Id,
                Nome = x.Fantasia
            }).ToList();
        }

        public async Task<List<Loja>> ObterLojasPorUsuarioComPlano(Guid usuarioId)
        {
            return await _lojaRepository.ObterLojasPorUsuarioComPlano(usuarioId);
        }

        public async Task<string> ObterIdentificadorUrlDelivery(Guid lojaId)
        {
            var loja = await _lojaRepository.FirstOrDefaultAsNoTracking(
                l => l.Id == lojaId,
                l => new Loja
                {
                    IdentificacaoDaLojaParaCardapio = l.IdentificacaoDaLojaParaCardapio
                });

            if (loja == null)
                return string.Empty;

            return loja.IdentificacaoDaLojaParaCardapio;
        }

        public async Task<bool> AlterarIdentificadorUrlDelivery(Guid lojaId, string identificadorUrl)
        {
            if (string.IsNullOrEmpty(identificadorUrl))
            {
                NotificarAviso(ResourceValidacao.CampoObrigatorio.Replace("'{PropertyName}'", "domínio personalizado"));
                return false;
            }

            var loja = await _lojaRepository.FindByKey(lojaId);

            if (loja == null)
            {
                NotificarAvisoRegistroNaoEncontrada("loja");
                return false;
            }

            if (await _lojaRepository.Any(l => l.IdentificacaoDaLojaParaCardapio == identificadorUrl && l.Id != lojaId))
            {
                NotificarAviso(ResourceMensagem.LojaService_IdentificadorUrlEmUso);
                return false;
            }

            if (!string.IsNullOrEmpty(loja.IdentificacaoDaLojaParaCardapio) &&
                loja.IdentificacaoDaLojaParaCardapio.Equals(identificadorUrl, StringComparison.OrdinalIgnoreCase))
                return true;

            var dominioAntigo = loja.IdentificacaoDaLojaParaCardapio;

            loja.IdentificacaoDaLojaParaCardapio = identificadorUrl;

            await _lojaRepository.SaveChanges();

            await _logAuditoriaService
                  .Inserir(new LogAuditoriaInserirViewModel(LogAuditoriaTela.LOJA,
                                                            LogAuditoriaOperacao.ALTERAR,
                                                            $"Domínio personalizado: {dominioAntigo} > {identificadorUrl}"));

            return true;
        }

        public async Task<bool> AlterarHorarioFuncionamento(Guid lojaId, IEnumerable<LojaHorarioFuncionamentoViewModel> horariosFuncionamentoViewModel)
        {
            var horariosFuncionamento = horariosFuncionamentoViewModel
                .Select(horario => _mapper.Map<LojaHorarioFuncionamento>(horario))
                .ToList();

            if (horariosFuncionamento.Any() &&
                !ExecutarValidacao(new ListaLojaHorarioFuncionamentoValidation(), horariosFuncionamento))
            {
                return false;
            }

            var loja = await _lojaRepository.ObterParaAlteracao(lojaId);

            if (loja == null)
            {
                NotificarAvisoRegistroNaoEncontrada("loja");
                return false;
            }

            if (horariosFuncionamento.Any())
                loja.HorariosFuncionamento = horariosFuncionamento;
            else
                loja.HorariosFuncionamento.Clear();

            await _lojaRepository.SaveChanges();

            await _logAuditoriaService
                  .Inserir(new LogAuditoriaInserirViewModel(LogAuditoriaTela.LOJA,
                                                            LogAuditoriaOperacao.ALTERAR,
                                                            $"Horário de funcionamento alterado."));

            return true;
        }

        public void Dispose()
        {
            _lojaRepository?.Dispose();
            _tabelaPrecoLojaRepository?.Dispose();
            _contaFinanceiraRepository?.Dispose();
            _localEstoqueRepository?.Dispose();
            _dashboardInicialService?.Dispose();
            _faturamentoRepository?.Dispose();
        }

        private async Task AtualizarAmbienteFiscalIntegracao<TConfiguracaoViewModel>(
            IdentificacaoIntegracao identificacao,
            LojaFiscalViewModel lojaViewModel)
            where TConfiguracaoViewModel : class
        {
            if (!await _integracaoService.VerificarSeExisteIntegracao(identificacao))
                return;

            var listaIntegracoes = await _integracaoService.ListarIntegracaoPorIdentificador(identificacao);

            foreach (var integracao in listaIntegracoes)
            {
                var configuracoes = JsonConvert.DeserializeObject<TConfiguracaoViewModel>(integracao.Configuracoes);

                if (configuracoes == null) continue;

                dynamic config = configuracoes;
                config.NFeTipoAmbiente = (int)lojaViewModel.TipoAmbienteFiscal;

                await _integracaoService.Alterar(integracao, JsonConvert.SerializeObject(config));
            }
        }
    }
}
