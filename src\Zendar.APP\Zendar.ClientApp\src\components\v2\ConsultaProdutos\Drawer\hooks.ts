import { useMediaQuery } from '@chakra-ui/react';
import { useCallback, useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'react-toastify';

import api, { ResponseApi } from 'services/api';

import { Filtros } from 'store/PDV/ConsultaProdutoPdv';

import { useEnterKeyOnFields } from 'hooks/useEnterKeyOnFields';

import { OptionProps } from 'types/optionType';

import ConstanteEnderecoWebservice from 'constants/enderecoWebservice';
import StatusConsultaEnum from 'constants/enum/statusConsulta';
import TipoFiltroProdutoEstoqueEnum from 'constants/enum/tipoFiltroProdutoEstoque';

type OptionsResponseProps = {
  id: string;
  nome: string;
};

type UseDrawerConsultaProdutosParams = {
  filtersSubmit: (filters: Filtros) => void;
  onClose: () => void;
};

const filtrosValoresPadrao: Filtros = {
  nome: '',
  sku: '',
  codigoBarrasEtiqueta: '',
  codigoBarrasFornecedor: '',
  skuIdentificadorReferencia: '',
  tipoEstoque: TipoFiltroProdutoEstoqueEnum.TODOS,
  statusConsulta: StatusConsultaEnum.ATIVOS,
  cores: null,
  tamanhos: null,
  categorias: null,
  marcas: null,
};

export const useDrawerConsultaProdutos = (
  params: UseDrawerConsultaProdutosParams
) => {
  const { filtersSubmit, onClose } = params;

  const [cores, setCores] = useState<OptionProps[]>([]);
  const [tamanhos, setTamanhos] = useState<OptionProps[]>([]);
  const [marcas, setMarcas] = useState<OptionProps[]>([]);

  const [isLargerThan900] = useMediaQuery('(min-width: 900px)');
  const [isLargerThan1200] = useMediaQuery('(min-width: 1200px)');

  const formMethods = useForm<Filtros>({
    defaultValues: filtrosValoresPadrao,
  });

  const { handleSubmit: submit, reset } = formMethods;

  const handleSubmit = submit(() => {
    const filtros = formMethods.watch();
    filtersSubmit(filtros);
    onClose();
  });

  const handleLimparPesquisa = () => {
    reset(filtrosValoresPadrao);
  };

  const preencherOpcoes = useCallback(
    (
      response: ResponseApi<OptionsResponseProps[]>,
      setStateValue: (value: React.SetStateAction<OptionProps[]>) => void
    ) => {
      if (!response) {
        return;
      }

      if (response?.avisos) {
        response.avisos.forEach((aviso) => toast.warning(aviso));
      }

      if (response?.sucesso && response?.dados) {
        const opcoes = response.dados.map((item) => ({
          label: item.nome,
          value: item.id,
        }));

        setStateValue(opcoes);
      }
    },
    []
  );

  const obterCores = useCallback(async () => {
    const response = await api.get<void, ResponseApi<OptionsResponseProps[]>>(
      ConstanteEnderecoWebservice.COR_LISTAR_SELECT,
      {
        params: {
          statusConsulta: StatusConsultaEnum.TODOS,
          listarPadraoSistema: true,
        },
      }
    );

    return response;
  }, []);

  const obterTamanhos = useCallback(async () => {
    const response = await api.get<void, ResponseApi<OptionsResponseProps[]>>(
      ConstanteEnderecoWebservice.TAMANHO_LISTAR_SELECT,
      {
        params: {
          statusConsulta: StatusConsultaEnum.TODOS,
          listarPadraoSistema: true,
        },
      }
    );

    return response;
  }, []);

  const obterMarcas = useCallback(async () => {
    const response = await api.get<void, ResponseApi<OptionsResponseProps[]>>(
      ConstanteEnderecoWebservice.MARCA_LISTAR_SELECT,
      {
        params: {
          statusconsulta: StatusConsultaEnum.ATIVOS,
        },
      }
    );

    return response;
  }, []);

  const buscarOpcoes = useCallback(async () => {
    const promises = [obterCores(), obterTamanhos(), obterMarcas()];
    const [coresResponse, tamanhosResponse, marcasResponse] = await Promise.all(
      promises
    );

    preencherOpcoes(coresResponse, setCores);
    preencherOpcoes(tamanhosResponse, setTamanhos);
    preencherOpcoes(marcasResponse, setMarcas);
  }, [preencherOpcoes, obterCores, obterTamanhos, obterMarcas]);

  useEffect(() => {
    buscarOpcoes();
  }, [buscarOpcoes]);

  useEnterKeyOnFields(() => {
    setTimeout(() => {
      handleSubmit();
    }, 100);
  }, [
    'nome',
    'sku',
    'codigoBarrasEtiqueta',
    'codigoBarrasFornecedor',
    'skuIdentificadorReferencia',
    'tipoEstoque',
    'categorias',
    'marcas',
  ]);

  return {
    cores,
    tamanhos,
    marcas,
    isLargerThan900,
    isLargerThan1200,
    formMethods,
    handleLimparPesquisa,
    preencherOpcoes,
    handleSubmit,
    filtrosValoresPadrao,
  };
};
