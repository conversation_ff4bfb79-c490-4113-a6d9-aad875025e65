﻿using Microsoft.EntityFrameworkCore;
using Multiempresa.Shared.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;
using Zendar.Data.Contexts;
using Zendar.Data.Enums;
using Zendar.Data.Enums.Integracao;
using Zendar.Data.Helpers;
using Zendar.Data.Interfaces.Aplicacao;
using Zendar.Data.Models.Aplicacao;
using Zendar.Data.Models.PesquisaDeProduto;
using Zendar.Data.ViewModels;
using ZendarPackage.NotaFiscal.Classes.Autorizar;

namespace Zendar.Data.Repository.Aplicacao
{
    public class ProdutoCorTamanhoRepository : RepositoryAplicacao<ProdutoCorTamanho>, IProdutoCorTamanhoRepository
    {
        public ProdutoCorTamanhoRepository(AplicacaoContexto context) : base(context)
        {
        }

        public async Task<List<ProdutoCorTamanho>> ObterProdutosCorTamanhoAlteracaoEmMassa(bool selecionados, Guid[] produtosIds, Guid[] marcasIds, Guid[] categoriasIds, StatusConsulta status)
        {
            var pctQuery = DbSet.AsQueryable();

            if (selecionados)
            {
                pctQuery = pctQuery.Where(pct => produtosIds.Contains(pct.ProdutoCor.ProdutoId));
            }
            else
            {
                pctQuery = pctQuery.Where(pct => !produtosIds.Contains(pct.ProdutoCor.ProdutoId));

                pctQuery = pctQuery.Where(pct => marcasIds.Any(m => m == pct.ProdutoCor.Produto.MarcaId)
                                              || categoriasIds.Any(c => c == pct.ProdutoCor.Produto.CategoriaProdutoId));

                pctQuery = status switch
                {
                    StatusConsulta.Ativos => pctQuery.Where(pct => pct.ProdutoCor.Produto.Ativo),
                    StatusConsulta.Inativos => pctQuery.Where(pct => !pct.ProdutoCor.Produto.Ativo),
                    _ => pctQuery
                };
            }

            return await pctQuery.ToListAsync();
        }

        public async Task<List<ProdutoCorTamanho>> ObterProdutosCorTamanhoAlteracaoEmMassa(bool selecionados, Guid[] produtosIds, decimal estoqueMinimoAtual)
        {

            var pctQuery = DbSet.Where(pct => pct.EstoqueMinimo == estoqueMinimoAtual);

            if (selecionados)
            {
                pctQuery = pctQuery.Where(pct => produtosIds.Contains(pct.ProdutoCor.ProdutoId));
            }
            else
            {
                pctQuery = pctQuery.Where(pct => !produtosIds.Contains(pct.ProdutoCor.ProdutoId));
            }

            return await pctQuery.ToListAsync();
        }

        public IQueryable<ProdutoCorTamanho> Where(Expression<Func<ProdutoCorTamanho, bool>> predicate)
            => DbSet.Where(predicate);

        public async Task<bool> AlterarCodigoGtinEan(Guid produtoCorTamanhoId, string codigo)
        {
            return await Db.Database.ExecuteSqlInterpolatedAsync(@$"
                UPDATE
                    [ProdutoCorTamanho]
                SET
                    [CodigoGtinEan] = {codigo}
                WHERE
                    [Id] = {produtoCorTamanhoId}
            ") > 0;
        }

        public async Task<bool> AlterarCodigoExterno(Guid produtoCorTamanhoId, string codigo)
        {
            return await Db.Database.ExecuteSqlInterpolatedAsync(@$"
                UPDATE
                    [ProdutoCorTamanho]
                SET
                    [CodigoExterno] = {codigo}
                WHERE
                    [Id] = {produtoCorTamanhoId}
            ") > 0;
        }

        public Task<ProdutoCorTamanho> ObterInformacoesParaNotaFiscal(Guid id, Guid lojaId)
        {
            return DbSet.Where(p => p.Id == id)
                        .Select(t => new ProdutoCorTamanho
                        {
                            Id = t.Id,
                            CodigoGTINEAN = t.CodigoGTINEAN,
                            SKU = t.SKU,
                            PesoLiquido = t.PesoLiquido,
                            PesoBruto = t.PesoBruto,
                            PesoEmbalagem = t.PesoEmbalagem,
                            ProdutoCor = new ProdutoCor
                            {
                                Produto = new Produto
                                {
                                    Nome = t.ProdutoCor.Produto.Nome,
                                    ProdutoPrecoLojas = t.ProdutoCor.Produto.ProdutoPrecoLojas.Where(pcl => pcl.LojaId.Equals(lojaId)).Select(pcl => new ProdutoPrecoLoja { PrecoVenda = pcl.PrecoVenda }).ToList(),
                                    IcmsStRetidoBaseCalculo = t.ProdutoCor.Produto.IcmsStRetidoBaseCalculo,
                                    IcmsStRetidoValor = t.ProdutoCor.Produto.IcmsStRetidoValor,
                                    FcpStRetidoBaseCalculo = t.ProdutoCor.Produto.FcpStRetidoBaseCalculo,
                                    FcpStRetidoValor = t.ProdutoCor.Produto.FcpStRetidoValor,
                                    IcmsAliquota = t.ProdutoCor.Produto.IcmsAliquota,
                                    PisAliquota = t.ProdutoCor.Produto.PisAliquota,
                                    CofinsAliquota = t.ProdutoCor.Produto.CofinsAliquota,
                                    FcpAliquota = t.ProdutoCor.Produto.FcpAliquota,
                                    IcmsReducaoBaseCalculo = t.ProdutoCor.Produto.IcmsReducaoBaseCalculo,
                                    CodigoCest = t.ProdutoCor.Produto.CodigoCest,
                                    AliquotaAdREmICMSRetido = t.ProdutoCor.Produto.AliquotaAdREmICMSRetido,
                                    QuantidadeBCMonoRetido = t.ProdutoCor.Produto.QuantidadeBCMonoRetido,
                                    UnidadeMedida = new UnidadeMedida { Sigla = t.ProdutoCor.Produto.UnidadeMedida.Sigla },
                                    UnidadeTributavel = new UnidadeMedida
                                    {
                                        Sigla = t.ProdutoCor.Produto.UnidadeTributavel.Sigla,
                                        UnidadeTributavel = t.ProdutoCor.Produto.UnidadeTributavel.UnidadeTributavel
                                    }
                                }
                            }
                        })
                        .FirstOrDefaultAsync();
        }

        public Task<ProdutoCorTamanho> ConsultarDescricaoProduto(Guid produtoCorTamanhoId)
        {
            return DbSet.Where(p => p.Id == produtoCorTamanhoId)
                        .OrderBy(x => x.Tamanho.SequenciaOrdenacao)
                        .Select(p => new ProdutoCorTamanho
                        {
                            Tamanho = new Tamanho
                            {
                                PadraoSistema = p.Tamanho.PadraoSistema,
                                Descricao = p.Tamanho.Descricao
                            },
                            ProdutoCor = new ProdutoCor
                            {
                                Cor = new Cor
                                {
                                    PadraoSistema = p.ProdutoCor.Cor.PadraoSistema,
                                    Descricao = p.ProdutoCor.Cor.Descricao
                                },
                                Produto = new Produto
                                {
                                    Nome = p.ProdutoCor.Produto.Nome
                                }
                            }
                        })
                        .FirstOrDefaultAsync();
        }

        public Task<List<ProdutoCorTamanhoSelectKitsViewModel>> ListarSelectTamanhoKits(string nomeSkuCodigoExternoBarrasGtinEan, Guid produtoCorId, Guid lojaId)
        {
            var query = FiltrarProdutoCorTamanho(nomeSkuCodigoExternoBarrasGtinEan);

            var produtoPreco = query.Where(p => p.ProdutoCor.Id.Equals(produtoCorId)).Select(x => x.ProdutoCor.Produto.ProdutoPrecoLojas.Where(pcl => pcl.LojaId.Equals(lojaId)).FirstOrDefault()).FirstOrDefault();

            return query.Where(p => p.ProdutoCor.Id.Equals(produtoCorId) && p.ProdutoCor.Produto.Ativo)
                .OrderBy(p => p.Tamanho.SequenciaOrdenacao)
                .Select(p => new ProdutoCorTamanhoSelectKitsViewModel
                {
                    Id = p.Id,
                    Descricao = p.Tamanho.Descricao,
                    PrecoCusto = produtoPreco != null ? produtoPreco.PrecoCusto : 0m,
                    PrecoVenda = produtoPreco != null ? produtoPreco.PrecoVenda : 0m,
                    PadraoSistema = p.Tamanho.PadraoSistema
                })
                .Take(20)
                .ToListAsync();
        }

        private IQueryable<ProdutoCorTamanho> FiltrarProdutoCorTamanho(string nomeReferenciaCodigoBarras, bool filtrarAtivos = true)
        {
            var produtos = DbSet.Where(pct => pct.Ativo);

            if (filtrarAtivos)
                produtos = produtos.Where(p => p.ProdutoCor.Produto.Ativo);

            if (!string.IsNullOrEmpty(nomeReferenciaCodigoBarras))
            {
                (var codigoBarrasHexadecimal, var decimalValue) = CodigoBarrasReduzidoHelper.ValidarCodigoBarras(nomeReferenciaCodigoBarras);

                if (codigoBarrasHexadecimal)
                {
                    produtos = produtos.Where(pct => pct.SequenciaCodigoBarras == decimalValue);
                }
                else
                {
                    bool pesquisarGtinEan = false;
                    if (long.TryParse(nomeReferenciaCodigoBarras, out long codigoGtinEan))
                    {
                        pesquisarGtinEan = nomeReferenciaCodigoBarras.Length is 8 or 12 or 13 or 14;
                    }

                    bool pesquisarCodigoBarasInterno = false;
                    if (long.TryParse(nomeReferenciaCodigoBarras, out long codigoBarrasInterno))
                    {
                        pesquisarCodigoBarasInterno = nomeReferenciaCodigoBarras.Length is 13;
                    }
                    produtos = produtos.Where(p => EF.Functions.Collate(p.ProdutoCor.Produto.Nome, Collates.RemoverCaracteresEspeciaisAcentos).Contains(nomeReferenciaCodigoBarras) ||
                                             EF.Functions.Collate(p.ProdutoCor.Produto.Referencia, Collates.RemoverCaracteresEspeciaisAcentos).Equals(nomeReferenciaCodigoBarras) ||
                                             p.SKU == nomeReferenciaCodigoBarras ||
                                             p.CodigoExterno == nomeReferenciaCodigoBarras ||
                                             (pesquisarGtinEan && p.CodigoGTINEAN == nomeReferenciaCodigoBarras) ||
                                             (pesquisarCodigoBarasInterno && p.CodigoBarrasInterno == nomeReferenciaCodigoBarras));
                }
            }

            return produtos;
        }

        public async Task<decimal> ObterPrecoVendaProduto(Guid produtoCorTamanhoId, Guid lojaId)
        {
            var produtoPrecoLoja = await DbSet.Where(p => p.Id == produtoCorTamanhoId)
                                        .Select(p => p.ProdutoCor.Produto.ProdutoPrecoLojas.FirstOrDefault(pcl => pcl.LojaId.Equals(lojaId)))
                                        .FirstOrDefaultAsync();

            if (produtoPrecoLoja != null)
                return produtoPrecoLoja.PrecoVenda;
            else
                return 0;
        }

        public async Task<decimal> ObterPrecoCustoProduto(Guid produtoCorTamanhoId, Guid lojaId)
        {
            var produtoPrecoLoja = await DbSet.Where(p => p.Id == produtoCorTamanhoId)
                                        .Select(p => p.ProdutoCor.Produto.ProdutoPrecoLojas.FirstOrDefault(pcl => pcl.LojaId.Equals(lojaId)))
                                        .FirstOrDefaultAsync();

            if (produtoPrecoLoja != null)
                return produtoPrecoLoja.PrecoCusto;
            else
                return 0;
        }

        public List<IGrouping<Guid, ProdutoCorTamanho>> ListaSelectConferenciaEstoque(ProdutoCorTamanhoConferenciaEstoqueListaSelectParameros produtoCorTamanhoConferenciaEstoqueListaSelectParameros)
        {

            var query = FiltrarProdutoCorTamanho(produtoCorTamanhoConferenciaEstoqueListaSelectParameros.NomeSkuCodigoExternoBarrasGtinEan);

            if (produtoCorTamanhoConferenciaEstoqueListaSelectParameros.ListaMarcas != null && produtoCorTamanhoConferenciaEstoqueListaSelectParameros.ListaMarcas.Count > 0)
            {
                query = query.Where(x => produtoCorTamanhoConferenciaEstoqueListaSelectParameros.ListaMarcas.Contains(x.ProdutoCor.Produto.MarcaId));
            }

            if (produtoCorTamanhoConferenciaEstoqueListaSelectParameros.ListaCategorias != null && produtoCorTamanhoConferenciaEstoqueListaSelectParameros.ListaCategorias.Count > 0)
            {
                query = query.Where(x => produtoCorTamanhoConferenciaEstoqueListaSelectParameros.ListaCategorias.Contains(x.ProdutoCor.Produto.CategoriaProdutoId));
            }

            return query.Where(x => x.ProdutoCor.Produto.TipoProduto != TipoProduto.PRODUTO_KIT)
                        .OrderBy(x => x.Tamanho.SequenciaOrdenacao)
                        .Select(p => new ProdutoCorTamanho
                        {
                            Id = p.Id,
                            ProdutoCorId = p.ProdutoCorId,
                            SKU = p.SKU,
                            CodigoBarrasInterno = p.CodigoBarrasInterno,
                            CodigoGTINEAN = p.CodigoGTINEAN,
                            CodigoExterno = p.CodigoExterno,
                            ProdutoCor = new ProdutoCor
                            {
                                Produto = new Produto
                                {
                                    Nome = p.ProdutoCor.Produto.Nome,
                                    TipoProduto = p.ProdutoCor.Produto.TipoProduto,
                                    UnidadeMedida = new UnidadeMedida
                                    {
                                        VolumeUnitario = p.ProdutoCor.Produto.UnidadeMedida.VolumeUnitario
                                    }
                                },
                                Cor = new Cor
                                {
                                    Descricao = p.ProdutoCor.Cor.Descricao,
                                    PadraoSistema = p.ProdutoCor.Cor.PadraoSistema
                                }
                            },
                            Tamanho = new Tamanho
                            {
                                Descricao = p.Tamanho.Descricao,
                                PadraoSistema = p.Tamanho.PadraoSistema,
                            }
                        })
                .AsEnumerable()
                .GroupBy(p => p.ProdutoCorId)
                .Take(20)
                .ToList();
        }

        public List<IGrouping<Guid, ProdutoCorTamanho>> ListarSelectMovimentacaoTransferenciaEstoque(string nomeSkuCodigoExternoBarrasGtinEan)
        {
            var query = FiltrarProdutoCorTamanho(nomeSkuCodigoExternoBarrasGtinEan);

            return query
                .Where(pct => pct.ProdutoCor.Produto.TipoProduto != TipoProduto.PRODUTO_KIT)
                .OrderBy(x => x.Tamanho.SequenciaOrdenacao)
                .Select(p => new ProdutoCorTamanho
                {
                    Id = p.Id,
                    ProdutoCorId = p.ProdutoCorId,
                    ProdutoCor = new ProdutoCor
                    {
                        Produto = new Produto
                        {
                            Nome = p.ProdutoCor.Produto.Nome,
                            TipoProduto = p.ProdutoCor.Produto.TipoProduto,
                            UnidadeMedida = new UnidadeMedida
                            {
                                VolumeUnitario = p.ProdutoCor.Produto.UnidadeMedida.VolumeUnitario
                            }
                        },
                        Cor = new Cor
                        {
                            Descricao = p.ProdutoCor.Cor.Descricao,
                            PadraoSistema = p.ProdutoCor.Cor.PadraoSistema
                        },
                    },
                    Tamanho = new Tamanho
                    {
                        Descricao = p.Tamanho.Descricao,
                        PadraoSistema = p.Tamanho.PadraoSistema,
                    }
                })
                .AsEnumerable()
                .GroupBy(p => p.ProdutoCorId)
                .Take(20)
                .ToList();
        }

        public async Task<List<ProdutoExportarConferenciaViewModel>> ObterProdutosExportarConferencia(Guid localEstoqueId, TipoConferencia tipoConferencia, List<Guid> categorias, List<Guid> marcas)
        {
            var query = DbSet.Where(x => x.Ativo && (x.ProdutoCor.Produto.TipoProduto != TipoProduto.PRODUTO_VARIACAO || (x.ProdutoCor.Produto.TipoProduto == TipoProduto.PRODUTO_VARIACAO &&
                                                                                                                         (x.ProdutoCor.Produto.ProdutoCores.Count == 1 || !x.ProdutoCor.Cor.PadraoSistema) &&
                                                                                                                         (x.ProdutoCor.ProdutoCorTamanhos.Count == 1 ||
                                                                                                                         !x.Tamanho.PadraoSistema)))
                                  );

            if (tipoConferencia == TipoConferencia.COM_FILTROS)
            {
                query = query
                    .Where(p => (categorias.Count == 0 || categorias.Any(x => x == p.ProdutoCor.Produto.CategoriaProdutoId)) && (marcas.Count == 0 || marcas.Any(x => x == p.ProdutoCor.Produto.MarcaId)));
            }

            return await query.Where(x => !x.ProdutoCor.Produto.TipoProduto.Equals(TipoProduto.PRODUTO_KIT))
                .Select(x => new ProdutoExportarConferenciaViewModel
                {
                    CodigoBarrasInterno = x.CodigoBarrasInterno,
                    CodigoEan = string.IsNullOrEmpty(x.CodigoGTINEAN) ? "" : "C" + x.CodigoGTINEAN,
                    CodigoExterno = string.IsNullOrEmpty(x.CodigoExterno) ? "" : "C" + x.CodigoExterno,
                    ProdutoNome = x.ProdutoCor.Produto.Nome,
                    CorDescricao = x.ProdutoCor.Cor.PadraoSistema ? "" : x.ProdutoCor.Cor.Descricao,
                    TamanhoDescricao = x.Tamanho.PadraoSistema ? "" : x.Tamanho.Descricao,
                    QuantidadeAtual = x.ProdutoCorTamanhoEstoques.Where(y => y.LocalEstoqueId == localEstoqueId).Select(x => x.EstoqueAtual).FirstOrDefault(),
                })
                .ToListAsync();
        }

        public async Task<bool> VerificarCodExterno(Guid id, string codigoExterno)
        {
            return await DbSet.AnyAsync(x => (x.Id != id || id == Guid.Empty) && x.CodigoExterno == codigoExterno && !string.IsNullOrEmpty(codigoExterno));
        }

        public async Task<bool> VerificarCodGtinEan(Guid id, string gTIN)
        {
            return await DbSet.AnyAsync(x => (x.Id != id || id == Guid.Empty) && x.CodigoGTINEAN == gTIN && !string.IsNullOrEmpty(gTIN));
        }

        public async Task<List<OperacaoItem>> ObterParaRelatorioLucroPorProduto(RelatorioProdutoFiltrosViewModel produtoFiltrosViewModel, Guid lojaId)
        {
            var produtos = FiltrarCamposListagemProduto(produtoFiltrosViewModel, lojaId);

            var operacaoItens = FiltrarProdutosVendidosPorData(produtos, produtoFiltrosViewModel, lojaId);

            return await operacaoItens.Select(o => new OperacaoItem
            {
                ProdutoCorTamanhoId = o.ProdutoCorTamanhoId,
                PrecoCusto = o.PrecoCusto,
                ValorUnitario = o.ValorUnitario,
                ValorDescontoItem = o.ValorDescontoItem,
                DescontoDistribuido = o.DescontoDistribuido,
                AcrescimoDistribuido = o.AcrescimoDistribuido,
                FreteDistribuido = o.FreteDistribuido,
                OutrasDespesasDistribuido = o.OutrasDespesasDistribuido,
                Quantidade = o.Quantidade,
                Operacao = new Operacao
                {
                    NumeroOperacao = o.Operacao.NumeroOperacao,
                    TipoOperacao = new TipoOperacao
                    {
                        IdentificacaoTipoOperacao = o.Operacao.TipoOperacao.IdentificacaoTipoOperacao
                    }
                }
            }).ToListAsync();
        }
        public async Task<List<OperacaoItem>> ObterRelatorioAgrupadoPorCliente(RelatorioProdutoFiltrosViewModel produtoFiltrosViewModel, Guid lojaId)
        {
            var produtos = FiltrarCamposListagemProduto(produtoFiltrosViewModel, lojaId);

            var operacaoItens = FiltrarProdutosVendidosPorData(produtos, produtoFiltrosViewModel, lojaId);

            return await operacaoItens.Select(o => new OperacaoItem
            {
                ValorUnitario = o.ValorUnitario,
                ValorItemSemDesconto = o.ValorItemSemDesconto,
                Quantidade = o.Quantidade,
                ProdutoCorTamanho = new ProdutoCorTamanho
                {
                    Tamanho = new Tamanho
                    {
                        Descricao = o.ProdutoCorTamanho.Tamanho.Descricao,
                        PadraoSistema = o.ProdutoCorTamanho.Tamanho.PadraoSistema,
                    },
                    ProdutoCor = new ProdutoCor
                    {
                        Cor = new Cor
                        {
                            Descricao = o.ProdutoCorTamanho.ProdutoCor.Cor.Descricao,
                            PadraoSistema = o.ProdutoCorTamanho.ProdutoCor.Cor.PadraoSistema,
                        },
                        Produto = new Produto
                        {
                            Nome = o.ProdutoCorTamanho.ProdutoCor.Produto.Nome,
                            Referencia = o.ProdutoCorTamanho.ProdutoCor.Produto.Referencia,
                        }
                    }
                },
                DescontoDistribuido = o.DescontoDistribuido,
                ValorDescontoItem = o.ValorDescontoItem,
                AcrescimoDistribuido = o.AcrescimoDistribuido,
                FreteDistribuido = o.FreteDistribuido,
                OutrasDespesasDistribuido = o.OutrasDespesasDistribuido,
                Operacao = new Operacao
                {
                    TipoOperacao = new TipoOperacao
                    {
                        IdentificacaoTipoOperacao = o.Operacao.TipoOperacao.IdentificacaoTipoOperacao
                    },
                    NumeroOperacao = o.Operacao.NumeroOperacao,
                    DataEmissao = o.Operacao.DataEmissao,
                    ClienteFornecedor = new ClienteFornecedor
                    {
                        Codigo = o.Operacao.ClienteFornecedor.Codigo,
                        Nome = o.Operacao.ClienteFornecedor.Nome,
                        Telefone = o.Operacao.ClienteFornecedor.Telefone,
                        Celular = o.Operacao.ClienteFornecedor.Celular,
                    }
                }
            }).ToListAsync();
        }

        public async Task<List<OperacaoItem>> ObterRelatoriosProdutoAgrupadoPorDia(RelatorioProdutoFiltrosViewModel filtrosViewModel, Guid lojaId)
        {
            var produtos = FiltrarCamposListagemProduto(filtrosViewModel, lojaId);

            var operacaoItens = FiltrarProdutosVendidosPorData(produtos, filtrosViewModel, lojaId);
            return await operacaoItens.Select(i => new OperacaoItem
            {
                Quantidade = i.Quantidade,
                ValorUnitario = i.ValorUnitario,
                //Totalizadores
                AcrescimoDistribuido = i.AcrescimoDistribuido,
                FreteDistribuido = i.FreteDistribuido,
                OutrasDespesasDistribuido = i.OutrasDespesasDistribuido,
                ValorDescontoItem = i.ValorDescontoItem,
                DescontoDistribuido = i.DescontoDistribuido,

                ProdutoCorTamanho = new ProdutoCorTamanho
                {
                    ProdutoCor = new ProdutoCor
                    {
                        Produto = new Produto
                        {
                            SkuIdentificador = i.ProdutoCorTamanho.ProdutoCor.Produto.SkuIdentificador,
                            Nome = i.ProdutoCorTamanho.ProdutoCor.Produto.Nome,
                            CategoriaProduto = new CategoriaProduto
                            {
                                Nome = i.ProdutoCorTamanho.ProdutoCor.Produto.CategoriaProduto.Nome
                            }
                        }
                    }
                },
                Operacao = new Operacao
                {
                    Id = i.Operacao.Id,
                    DataEmissao = i.Operacao.DataEmissao,
                    TipoOperacao = new TipoOperacao
                    {
                        IdentificacaoTipoOperacao = i.Operacao.TipoOperacao.IdentificacaoTipoOperacao
                    }
                }
            }).ToListAsync();
        }

        public async Task<List<OperacaoItem>> ObterRelatorioProdutoPorCompras(RelatorioProdutoFiltrosViewModel produtoFiltrosViewModel, Guid lojaId)
        {
            var produtos = FiltrarCamposListagemProduto(produtoFiltrosViewModel, lojaId);

            var operacaoItens = FiltrarProdutosCompradosPorData(produtos, produtoFiltrosViewModel, lojaId);

            return await operacaoItens.Select(o => new OperacaoItem
            {
                ValorUnitario = o.ValorUnitario,
                ValorItemSemDesconto = o.ValorItemSemDesconto,
                Quantidade = o.Quantidade,

                ProdutoCorTamanho = new ProdutoCorTamanho
                {
                    Tamanho = new Tamanho
                    {
                        Descricao = o.ProdutoCorTamanho.Tamanho.Descricao,
                    },
                    ProdutoCor = new ProdutoCor
                    {
                        Cor = new Cor
                        {
                            Descricao = o.ProdutoCorTamanho.ProdutoCor.Cor.Descricao,
                        },
                        Produto = new Produto
                        {
                            Nome = o.ProdutoCorTamanho.ProdutoCor.Produto.Nome,
                        }
                    }
                },
                DescontoDistribuido = o.DescontoDistribuido,
                ValorDescontoItem = o.ValorDescontoItem,
                ValorItemComDesconto = o.ValorItemComDesconto,
                AcrescimoDistribuido = o.AcrescimoDistribuido,
                FreteDistribuido = o.FreteDistribuido,
                OutrasDespesasDistribuido = o.OutrasDespesasDistribuido,
                Operacao = new Operacao
                {
                    NumeroOperacao = o.Operacao.NumeroOperacao,
                    DataEmissao = o.Operacao.DataEmissao,
                    ClienteFornecedor = new ClienteFornecedor
                    {
                        Nome = o.Operacao.ClienteFornecedor.Nome,
                    }
                }
            }).ToListAsync();
        }

        public async Task<List<OperacaoItem>> ObterRelatorioCompraPorGrade(RelatorioProdutoFiltrosViewModel produtoFiltrosViewModel, Guid lojaId)
        {
            var produtos = FiltrarCamposListagemProduto(produtoFiltrosViewModel, lojaId);

            var operacaoItens = FiltrarProdutosCompradosPorData(produtos, produtoFiltrosViewModel, lojaId);

            return await operacaoItens.Select(o => new OperacaoItem
            {
                ValorUnitario = o.ValorUnitario,
                ValorItemSemDesconto = o.ValorItemSemDesconto,
                Quantidade = o.Quantidade,

                ProdutoCorTamanho = new ProdutoCorTamanho
                {
                    Tamanho = new Tamanho
                    {
                        Descricao = o.ProdutoCorTamanho.Tamanho.Descricao,
                    },
                    ProdutoCor = new ProdutoCor
                    {
                        Cor = new Cor
                        {
                            Descricao = o.ProdutoCorTamanho.ProdutoCor.Cor.Descricao,
                            PadraoSistema = o.ProdutoCorTamanho.ProdutoCor.Cor.PadraoSistema
                        },
                        Produto = new Produto
                        {
                            Nome = o.ProdutoCorTamanho.ProdutoCor.Produto.Nome,
                        }
                    }
                },
                DescontoDistribuido = o.DescontoDistribuido,
                ValorDescontoItem = o.ValorDescontoItem,
                ValorItemComDesconto = o.ValorItemComDesconto,
                AcrescimoDistribuido = o.AcrescimoDistribuido,
                FreteDistribuido = o.FreteDistribuido,
                OutrasDespesasDistribuido = o.OutrasDespesasDistribuido,
                Operacao = new Operacao
                {
                    NumeroOperacao = o.Operacao.NumeroOperacao,
                    DataEmissao = o.Operacao.DataEmissao,
                    ClienteFornecedor = new ClienteFornecedor
                    {
                        Nome = o.Operacao.ClienteFornecedor.Nome,
                    }
                }
            }).ToListAsync();
        }
        public async Task<List<OperacaoItem>> ObterRelatorioProdutoPorComprasDetalhado(RelatorioProdutoFiltrosViewModel produtoFiltrosViewModel, Guid lojaId)
        {
            var produtos = FiltrarCamposListagemProduto(produtoFiltrosViewModel, lojaId);

            var operacaoItens = FiltrarProdutosCompradosPorData(produtos, produtoFiltrosViewModel, lojaId);

            return await operacaoItens.Select(o => new OperacaoItem
            {
                ValorUnitario = o.ValorUnitario,
                ValorItemSemDesconto = o.ValorItemSemDesconto,
                Quantidade = o.Quantidade,

                EntradaMercadoriaItem = new EntradaMercadoriaItem()
                {
                    EntradaMercadoria = new EntradaMercadoria()
                    {
                        NumeroNFe = o.EntradaMercadoriaItem.EntradaMercadoria.NumeroNFe,

                    },
                    MarkupNovo = o.EntradaMercadoriaItem.MarkupNovo,
                    PrecoVendaNovo = o.EntradaMercadoriaItem.PrecoVendaNovo,
                },
                ProdutoCorTamanho = new ProdutoCorTamanho
                {
                    Tamanho = new Tamanho
                    {
                        Descricao = o.ProdutoCorTamanho.Tamanho.Descricao,
                    },
                    ProdutoCor = new ProdutoCor
                    {
                        Cor = new Cor
                        {
                            Descricao = o.ProdutoCorTamanho.ProdutoCor.Cor.Descricao,
                        },
                        Produto = new Produto
                        {
                            Nome = o.ProdutoCorTamanho.ProdutoCor.Produto.Nome,
                            SkuIdentificador = o.ProdutoCorTamanho.ProdutoCor.Produto.SkuIdentificador,
                        }
                    }
                },
                DescontoDistribuido = o.DescontoDistribuido,
                ValorDescontoItem = o.ValorDescontoItem,
                ValorItemComDesconto = o.ValorItemComDesconto,
                AcrescimoDistribuido = o.AcrescimoDistribuido,
                FreteDistribuido = o.FreteDistribuido,
                OutrasDespesasDistribuido = o.OutrasDespesasDistribuido,
                Operacao = new Operacao
                {
                    TipoOperacao = new TipoOperacao
                    {
                        IdentificacaoTipoOperacao = o.Operacao.TipoOperacao.IdentificacaoTipoOperacao
                    },
                    NumeroOperacao = o.Operacao.NumeroOperacao,
                    DataEmissao = o.Operacao.DataEmissao,
                    ClienteFornecedor = new ClienteFornecedor
                    {
                        Nome = o.Operacao.ClienteFornecedor.Nome,
                    }
                }
            }).ToListAsync();
        }
        public async Task<List<OperacaoItem>> ObterParaRelatorioProdutoPorVenda(RelatorioProdutoFiltrosViewModel produtoFiltrosViewModel, Guid lojaId)
        {
            var produtos = FiltrarCamposListagemProduto(produtoFiltrosViewModel, lojaId);

            var operacaoItens = FiltrarProdutosVendidosPorData(produtos, produtoFiltrosViewModel, lojaId);

            return await operacaoItens.Select(o => new OperacaoItem
            {
                ProdutoCorTamanhoId = o.ProdutoCorTamanhoId,
                ValorUnitario = o.ValorUnitario,
                ValorItemSemDesconto = o.ValorItemSemDesconto,
                Quantidade = o.Quantidade,
                ProdutoCorTamanho = new() { ProdutoCor = new() { Produto = new Produto { Referencia = o.ProdutoCorTamanho.ProdutoCor.Produto.Referencia } } },
                DescontoDistribuido = o.DescontoDistribuido,
                ValorDescontoItem = o.ValorDescontoItem,
                AcrescimoDistribuido = o.AcrescimoDistribuido,
                FreteDistribuido = o.FreteDistribuido,
                OutrasDespesasDistribuido = o.OutrasDespesasDistribuido,
                Operacao = new Operacao
                {
                    TipoOperacao = new TipoOperacao
                    {
                        IdentificacaoTipoOperacao = o.Operacao.TipoOperacao.IdentificacaoTipoOperacao
                    },
                    NumeroOperacao = o.Operacao.NumeroOperacao,
                    DataEmissao = o.Operacao.DataEmissao,
                    DocumentoFiscal = o.Operacao.DocumentoFiscal.Select(df => new DocumentoFiscal
                    {
                        Numero = df.Numero,
                        ModeloFiscal = df.ModeloFiscal
                    })
                    .ToList(),
                    Vendedor = new Vendedor { Nome = o.Operacao.Vendedor.Nome },
                }
            }).ToListAsync();
        }

        public async Task<List<OperacaoItem>> ObterParaRelatorioProdutoPorGrupo(RelatorioProdutoFiltrosViewModel produtoFiltrosViewModel, Guid lojaId)
        {
            var produtos = FiltrarCamposListagemProduto(produtoFiltrosViewModel, lojaId);

            var operacaoItens = FiltrarProdutosVendidosPorData(produtos, produtoFiltrosViewModel, lojaId);

            operacaoItens = operacaoItens.Where(o => o.Operacao.TipoOperacao.IdentificacaoTipoOperacao == IdentificacaoTipoOperacao.VENDA);

            return await operacaoItens.Select(o => new OperacaoItem
            {
                ValorUnitario = o.ValorUnitario,
                Quantidade = o.Quantidade,
                OperacaoId = o.OperacaoId,
                ProdutoCorTamanho = new()
                {
                    ProdutoCor = new()
                    {
                        Produto = new Produto
                        {
                            SkuIdentificador = o.ProdutoCorTamanho.ProdutoCor.Produto.SkuIdentificador,
                            Nome = o.ProdutoCorTamanho.ProdutoCor.Produto.Nome,
                            CategoriaProduto = o.ProdutoCorTamanho.ProdutoCor.Produto.CategoriaProduto,
                            CategoriaProdutoId = o.ProdutoCorTamanho.ProdutoCor.Produto.CategoriaProdutoId,
                        },
                    },
                },

                DescontoDistribuido = o.DescontoDistribuido,
                ValorDescontoItem = o.ValorDescontoItem,
                AcrescimoDistribuido = o.AcrescimoDistribuido,
                FreteDistribuido = o.FreteDistribuido,
                OutrasDespesasDistribuido = o.OutrasDespesasDistribuido,
            }).ToListAsync();
        }

        public async Task<List<OperacaoItem>> ObterParaRelatorioProdutoPorNumeroConta(RelatorioProdutoFiltrosViewModel produtoFiltrosViewModel, Guid lojaId)
        {
            var produtos = FiltrarCamposListagemProduto(produtoFiltrosViewModel, lojaId);

            var operacaoItens = FiltrarProdutosVendidosPorData(produtos, produtoFiltrosViewModel, lojaId);

            operacaoItens = operacaoItens.Where(w => w.Operacao.Dispositivo.ReferenciaServico == Data.Enums.Stargate.ReferenciaServicoStargate.DISPOSITIVO_FRENTE_CAIXA);

            return await operacaoItens.Select(o => new OperacaoItem
            {
                ValorItemSemDesconto = o.ValorItemSemDesconto,
                Quantidade = o.Quantidade,
                NumeroConta = o.NumeroConta,
                ValorUnitario = o.ValorUnitario,
                OperacaoId = o.OperacaoId,
                ProdutoCorTamanho = new()
                {
                    ProdutoCor = new()
                    {
                        Produto = new Produto
                        {
                            Nome = o.ProdutoCorTamanho.ProdutoCor.Produto.Nome,
                        },
                    },
                },
                ValorItemComDesconto = o.ValorItemComDesconto,
                DescontoDistribuido = o.DescontoDistribuido,
                ValorDescontoItem = o.ValorDescontoItem,
                AcrescimoDistribuido = o.AcrescimoDistribuido,
                FreteDistribuido = o.FreteDistribuido,
                OutrasDespesasDistribuido = o.OutrasDespesasDistribuido,
            }).ToListAsync();
        }
        public async Task<List<OperacaoItem>> ObterParaRelatorioItensMaisVendidos(RelatorioProdutoFiltrosViewModel produtoFiltrosViewModel, Guid lojaId)
        {
            var produtos = FiltrarCamposListagemProduto(produtoFiltrosViewModel, lojaId);

            var operacaoItens = FiltrarProdutosVendidosPorData(produtos, produtoFiltrosViewModel, lojaId);

            return await operacaoItens.Select(o => new OperacaoItem
            {
                Operacao = new Operacao
                {
                    TipoOperacao = o.Operacao.TipoOperacao
                },
                ValorUnitario = o.ValorUnitario,
                Quantidade = o.Quantidade,
                ProdutoCorTamanho = new()
                {
                    Tamanho = o.ProdutoCorTamanho.Tamanho,
                    ProdutoCor = new()
                    {
                        Cor = o.ProdutoCorTamanho.ProdutoCor.Cor,
                        Produto = new Produto
                        {
                            SkuIdentificador = o.ProdutoCorTamanho.ProdutoCor.Produto.SkuIdentificador,
                            Nome = o.ProdutoCorTamanho.ProdutoCor.Produto.Nome,
                        },
                    },
                },

                DescontoDistribuido = o.DescontoDistribuido,
                ValorDescontoItem = o.ValorDescontoItem,
                AcrescimoDistribuido = o.AcrescimoDistribuido,
                FreteDistribuido = o.FreteDistribuido,
                OutrasDespesasDistribuido = o.OutrasDespesasDistribuido,
            }).ToListAsync();
        }

        public async Task<List<ProdutoCorTamanho>> ObterParaRelatorioProdutoComPreco(RelatorioProdutoFiltrosViewModel produtoFiltrosViewModel, Guid lojaId)
        {
            var produtos = FiltrarCamposListagemProduto(produtoFiltrosViewModel, lojaId)
                                .Where(p => p.ProdutoCor.Produto.TipoProduto != TipoProduto.PRODUTO_KIT);

            return await produtos
                .Select(x => new ProdutoCorTamanho
                {
                    Id = x.Id,
                    Tamanho = new Tamanho
                    {
                        PadraoSistema = x.Tamanho.PadraoSistema,
                        Descricao = x.Tamanho.Descricao,
                        SequenciaOrdenacao = x.Tamanho.SequenciaOrdenacao
                    },
                    ProdutoCor = new ProdutoCor
                    {
                        Cor = new Cor
                        {
                            PadraoSistema = x.ProdutoCor.Cor.PadraoSistema,
                            Descricao = x.ProdutoCor.Cor.Descricao
                        },
                        Produto = new Produto
                        {
                            Id = x.ProdutoCor.Produto.Id,
                            Nome = x.ProdutoCor.Produto.Nome,
                            ProdutoPrecoLojas = x.ProdutoCor.Produto.ProdutoPrecoLojas
                            .Where(pcl => pcl.LojaId.Equals(lojaId))
                            .Select(pcl => new ProdutoPrecoLoja
                            {
                                LojaId = pcl.LojaId,
                                PrecoVenda = pcl.PrecoVenda,
                                PrecoCusto = pcl.PrecoCusto
                            })
                            .ToList(),
                        }
                    }
                })
                .ToListAsync();
        }

        public async Task<List<ProdutoCorTamanho>> ObterParaRelatorioPersonalizadoProduto(RelatorioProdutoFiltrosViewModel produtoFiltrosViewModel, Guid lojaId)
        {
            var produtos = FiltrarCamposListagemProduto(produtoFiltrosViewModel, lojaId);

            return await produtos.Select(p => new ProdutoCorTamanho
            {
                Id = p.Id,
                CodigoGTINEAN = p.CodigoGTINEAN,
                EstoqueMinimo = p.EstoqueMinimo,
                SKU = p.SKU,
                CodigoBarrasInterno = p.CodigoBarrasInterno,
                Tamanho = new Tamanho
                {
                    Descricao = p.Tamanho.Descricao,
                    PadraoSistema = p.Tamanho.PadraoSistema
                },
                ProdutoCor = new ProdutoCor
                {
                    Cor = new Cor
                    {
                        Descricao = p.ProdutoCor.Cor.Descricao,
                        PadraoSistema = p.ProdutoCor.Cor.PadraoSistema

                    },
                    Produto = new Produto
                    {
                        Nome = p.ProdutoCor.Produto.Nome,
                        Foto = p.ProdutoCor.Produto.Foto,
                        NomeAbreviado = p.ProdutoCor.Produto.NomeAbreviado,
                        Referencia = p.ProdutoCor.Produto.Referencia,
                        TipoProduto = p.ProdutoCor.Produto.TipoProduto,
                        CategoriaProduto = new CategoriaProduto { Nome = p.ProdutoCor.Produto.CategoriaProduto.Nome },
                        Marca = new Marca { Nome = p.ProdutoCor.Produto.Marca.Nome },
                        ProdutoPrecoLojas = p.ProdutoCor.Produto.ProdutoPrecoLojas.Where(pcl => pcl.LojaId.Equals(lojaId)).Select(pcl => new ProdutoPrecoLoja
                        {
                            PrecoCompra = pcl.PrecoCompra,
                            PrecoCusto = pcl.PrecoCusto,
                            Markup = pcl.Markup,
                            PrecoVenda = pcl.PrecoVenda,
                        }).ToList(),
                        CodigoNcm = p.ProdutoCor.Produto.CodigoNcm,
                        CodigoCest = p.ProdutoCor.Produto.CodigoCest,
                        UnidadeMedida = new UnidadeMedida
                        {
                            Descricao = p.ProdutoCor.Produto.UnidadeMedida.Descricao
                        },
                        TagProdutos = p.ProdutoCor.Produto.TagProdutos.Select(x => new TagProduto
                        {
                            Tag = new Tag
                            {
                                Nome = x.Tag.Nome
                            }
                        }).OrderBy(x => x.Tag.Nome).ToList(),
                        RegraFiscal = new RegraFiscal { Nome = p.ProdutoCor.Produto.RegraFiscal.Nome },
                    }
                },
                ProdutoCorTamanhoEstoques = p.ProdutoCorTamanhoEstoques.Where(x => x.LocalEstoque.LojaId.Equals(lojaId))
                                                                       .Select(x => new ProdutoCorTamanhoEstoque { EstoqueAtual = x.EstoqueAtual })
                                                                       .ToList()
            }).ToListAsync();
        }

        private IQueryable<OperacaoItem> FiltrarProdutosVendidosPorData(IQueryable<ProdutoCorTamanho> produto, RelatorioProdutoFiltrosViewModel produtoFiltrosViewModel, Guid lojaId)
        {
            // Filtrar os produtos com operacoes de venda dentro da data escolhida
            var query = produto.SelectMany(p => p.OperacaoItens);

            if (query.Any(oi => oi.Operacao.Dispositivo.ReferenciaServico == Enums.Stargate.ReferenciaServicoStargate.DISPOSITIVO_FRENTE_CAIXA))
            {
                if (produtoFiltrosViewModel.NumeracaoComandaInicial.HasValue)
                    query = query.Where(oi => oi.NumeroConta >= produtoFiltrosViewModel.NumeracaoComandaInicial);

                if (produtoFiltrosViewModel.NumeracaoComandaFinal.HasValue)
                    query = query.Where(oi => oi.NumeroConta <= produtoFiltrosViewModel.NumeracaoComandaFinal);
            }

            if (produtoFiltrosViewModel.ContasFinanceirasId?.Length > 0)
            {
                query = query.Where(oi => oi.Operacao.CaixaMovimentacaoId.HasValue &&
                                          produtoFiltrosViewModel.ContasFinanceirasId.Contains(oi.Operacao.CaixaMovimentacao.ContaFinanceiraId));
            }

            if (produtoFiltrosViewModel.LocalEstoqueIds.Any())
                query = query.Where(oi => produtoFiltrosViewModel.LocalEstoqueIds.Contains(oi.Operacao.LocalEstoqueId));

            if (produtoFiltrosViewModel.Origem.HasValue && produtoFiltrosViewModel.Origem.Value != IdentificacaoIntegracao.TODAS)
            {
                if (produtoFiltrosViewModel.Origem.Value != IdentificacaoIntegracao.CAIXA_MOVEL)
                {
                    query = query.Where(oi => oi.Operacao.IntegracaoPedidos.Any(p => p.Integracao.IdentificacaoIntegracao == produtoFiltrosViewModel.Origem.Value));
                }
                else
                {
                    query = query.Where(oi => oi.Operacao.IntegracaoPedidos.Any(p => p.Integracao.IdentificacaoIntegracao == produtoFiltrosViewModel.Origem.Value) ||
                                             (oi.Operacao.DispositivoId.HasValue && !oi.Operacao.IntegracaoPedidos.Any())); 
                }
            }

            if (produtoFiltrosViewModel.StatusVenda.HasValue && produtoFiltrosViewModel.StatusVenda.Value != StatusVenda.TODAS)
            {
                query = query.Where(oi => produtoFiltrosViewModel.StatusVenda.Value == StatusVenda.CONCLUIDAS ?
                    oi.Operacao.Status == StatusOperacao.EFETUADA : oi.Operacao.Status == StatusOperacao.CANCELADA);
            }

            if (produtoFiltrosViewModel.TipoFiscal.Any() &&
                    !produtoFiltrosViewModel.TipoFiscal.Contains(TipoFiscal.TODOS))
            {
                if (produtoFiltrosViewModel.TipoFiscal.Contains(TipoFiscal.SEM_FISCAL))
                {
                    query = query.Where(oi => !oi.Operacao.DocumentoFiscal.Any());
                }
                else
                {
                    query = query.Where(oi =>
                        oi.Operacao.DocumentoFiscal.Any(x =>
                            (produtoFiltrosViewModel.TipoFiscal.Contains(TipoFiscal.NFCE) &&
                                (x.ModeloFiscal == ZendarPackage.NotaFiscal.Enums.ModeloFiscal.NFCe ||
                                 x.ModeloFiscal == ZendarPackage.NotaFiscal.Enums.ModeloFiscal.SAT)) ||
                            (produtoFiltrosViewModel.TipoFiscal.Contains(TipoFiscal.NFE) &&
                                x.ModeloFiscal == ZendarPackage.NotaFiscal.Enums.ModeloFiscal.NFe)));
                }
            }

            return query.Where(oi => oi.Operacao.DataEmissao >= produtoFiltrosViewModel.DataEmissaoInicio.Value
                                  && oi.Operacao.DataEmissao <= produtoFiltrosViewModel.DataEmissaoFim.Value
                                  && oi.Operacao.LojaId == lojaId
                                  && (oi.Operacao.TipoOperacao.IdentificacaoTipoOperacao == IdentificacaoTipoOperacao.VENDA
                                     || oi.Operacao.TipoOperacao.IdentificacaoTipoOperacao == IdentificacaoTipoOperacao.DEVOLUCAO)
                                  && (!produtoFiltrosViewModel.VendedorId.HasValue || oi.Operacao.VendedorId.Equals(produtoFiltrosViewModel.VendedorId.Value))
                                  && (!produtoFiltrosViewModel.ClienteFornecedorId.HasValue || oi.Operacao.ClienteFornecedorId.Equals(produtoFiltrosViewModel.ClienteFornecedorId.Value))
                                  && !oi.ItemKit
                                  && ((oi.Operacao.Status == StatusOperacao.EFETUADA && oi.Status == StatusOperacaoItem.EFETUADO) ||
                                      (oi.Operacao.Status == StatusOperacao.CANCELADA && oi.Status == StatusOperacaoItem.CANCELADO))
                                  && oi.OperacaoItemKitId == null);
        }
        private IQueryable<OperacaoItem> FiltrarProdutosCompradosPorData(IQueryable<ProdutoCorTamanho> produto, RelatorioProdutoFiltrosViewModel produtoFiltrosViewModel, Guid lojaId)
        {
            // Filtrar os produtos com operacoes de venda dentro da data escolhida
            return produto.SelectMany(p => p.OperacaoItens).Where(oi => oi.Operacao.DataEmissao >= produtoFiltrosViewModel.DataEmissaoInicio.Value
                                                 && oi.Operacao.DataEmissao <= produtoFiltrosViewModel.DataEmissaoFim.Value
                                                 && oi.Operacao.LojaId == lojaId
                                                 && (oi.Operacao.TipoOperacao.IdentificacaoTipoOperacao == IdentificacaoTipoOperacao.COMPRA_MERCADORIA || oi.Operacao.TipoOperacao.IdentificacaoTipoOperacao == IdentificacaoTipoOperacao.ENTRADA_MERCADORIA)
                                                 && oi.Operacao.Status == StatusOperacao.EFETUADA
                                                 && (!produtoFiltrosViewModel.ClienteFornecedorId.HasValue || oi.Operacao.ClienteFornecedorId.Equals(produtoFiltrosViewModel.ClienteFornecedorId.Value))
                                                 && !oi.ItemKit
                                                 && oi.Status == StatusOperacaoItem.EFETUADO
                                                 && oi.OperacaoItemKitId == null);
        }

        private IQueryable<ProdutoCorTamanho> ListarProdutosCorTamanhoPorProdutoId(RelatorioProdutoFiltrosViewModel produtoFiltrosViewModel)
        {
            var produtosCorTamanho = DbSet.Where(pct => pct.ProdutoCor.Produto.TipoProduto != TipoProduto.PRODUTO_VARIACAO ||
                (
                    pct.ProdutoCor.Produto.TipoProduto == TipoProduto.PRODUTO_VARIACAO
                    // é registro unico OU não é padrão
                    && (!pct.ProdutoCor.Produto.ProdutoCores.Skip(1).Any() || !pct.ProdutoCor.Cor.PadraoSistema)
                    && (!pct.ProdutoCor.ProdutoCorTamanhos.Skip(1).Any() || !pct.Tamanho.PadraoSistema)
                ));

            produtosCorTamanho = produtoFiltrosViewModel.StatusConsulta switch
            {
                StatusConsulta.Ativos => produtosCorTamanho.Where(pct => pct.Ativo && pct.ProdutoCor.Produto.Ativo),
                StatusConsulta.Inativos => produtosCorTamanho.Where(pct => !pct.Ativo || !pct.ProdutoCor.Produto.Ativo),
                _ => produtosCorTamanho
            };

            if (produtoFiltrosViewModel.ProdutoId.HasValue)
            {
                produtosCorTamanho = produtosCorTamanho.Where(x => x.ProdutoCor.ProdutoId == produtoFiltrosViewModel.ProdutoId);
            }

            return produtosCorTamanho;
        }
        private IQueryable<ProdutoCorTamanho> FiltrarCamposListagemProduto(RelatorioProdutoFiltrosViewModel produtoFiltrosViewModel, Guid lojaId)
        {
            var produtos = ListarProdutosCorTamanhoPorProdutoId(produtoFiltrosViewModel);            

            if (produtoFiltrosViewModel.TipoEstoque != TipoFiltroProdutoEstoque.TODOS)
            {
                if (produtoFiltrosViewModel.TipoEstoque == TipoFiltroProdutoEstoque.POSITIVO)
                {
                    produtos = produtos.Where(x => x.ProdutoCorTamanhoEstoques.Where(x => x.LocalEstoque.LojaId == lojaId)
                            .Select(pcte => pcte.EstoqueAtual).Sum() > 0);
                }
                else if (produtoFiltrosViewModel.TipoEstoque == TipoFiltroProdutoEstoque.NEGATIVO)
                {
                    produtos = produtos.Where(x => x.ProdutoCorTamanhoEstoques.Where(x => x.LocalEstoque.LojaId == lojaId)
                            .Select(pcte => pcte.EstoqueAtual).Sum() < 0);
                }
                else if (produtoFiltrosViewModel.TipoEstoque == TipoFiltroProdutoEstoque.ZERADO)
                {
                    produtos = produtos.Where(x => x.ProdutoCorTamanhoEstoques.Where(x => x.LocalEstoque.LojaId == lojaId)
                            .Select(pcte => pcte.EstoqueAtual).Sum() == 0);
                }
                else if (produtoFiltrosViewModel.TipoEstoque == TipoFiltroProdutoEstoque.ABAIXO_MINIMO)
                {
                    produtos = produtos.Where(x => x.ProdutoCorTamanhoEstoques.Where(x => x.LocalEstoque.LojaId == lojaId)
                            .Select(pcte => pcte.EstoqueAtual).Sum() < x.EstoqueMinimo);
                }
            }

            if (produtoFiltrosViewModel.Cores != null && produtoFiltrosViewModel.Cores.Count > 0)
            {
                produtos = produtos.Where(y => produtoFiltrosViewModel.Cores.Contains(y.ProdutoCor.CorId));
            }

            if (produtoFiltrosViewModel.Tamanhos != null && produtoFiltrosViewModel.Tamanhos.Count > 0)
            {
                produtos = produtos.Where(x => produtoFiltrosViewModel.Tamanhos.Contains(x.TamanhoId));
            }

            if (produtoFiltrosViewModel.CategoriasProduto != null && produtoFiltrosViewModel.CategoriasProduto.Count > 0)
            {
                produtos = produtos.Where(x => produtoFiltrosViewModel.CategoriasProduto.Contains(x.ProdutoCor.Produto.CategoriaProdutoId));
            }

            if (produtoFiltrosViewModel.Marcas != null && produtoFiltrosViewModel.Marcas.Count > 0)
            {
                produtos = produtos.Where(x => produtoFiltrosViewModel.Marcas.Contains(x.ProdutoCor.Produto.MarcaId));
            }

            if (produtoFiltrosViewModel.Tags != null && produtoFiltrosViewModel.Tags.Count > 0)
            {
                produtos = produtos.Where(x => x.ProdutoCor.Produto.TagProdutos.Any(y => produtoFiltrosViewModel.Tags.Contains(y.TagId)));
            }

            if (produtoFiltrosViewModel.LocalEstoqueIds.Any())
            {
                produtos = produtos.Where(o => o.ProdutoCorTamanhoEstoques.Any(mf =>
                            produtoFiltrosViewModel.LocalEstoqueIds.Contains(mf.LocalEstoqueId)));
            }

            if (produtoFiltrosViewModel.CamposPersonalizados != null && produtoFiltrosViewModel.CamposPersonalizados.Length > 0)
            {
                foreach (var camposPersonalizado in produtoFiltrosViewModel.CamposPersonalizados)
                {
                    produtos = produtos
                        .Where(x => x.ProdutoCor.Produto.CampoPersonalizadoProduto
                            .Any(y =>
                                y.CampoPersonalizadoId.Equals(camposPersonalizado.CampoPersonalizadoId) &&
                                y.Valor.Contains(camposPersonalizado.Valor)
                            )
                        );
                }
            }

            return produtos;
        }

        public async Task<List<ProdutoCorTamanho>> ObterParaImprimirEtiqueta(FiltroGerarEtiquetaViewModel filtroGerarEtiqueta, Guid lojaId)
        {
            // Filtro os produtos a partir de uma operação, caso tenha sido informada
            var query = DbSet.AsQueryable()
                            .Where(x => filtroGerarEtiqueta.OperacaoId.HasValue
                                        ? x.OperacaoItens.Any(y => y.OperacaoId.Equals(filtroGerarEtiqueta.OperacaoId.Value))
                                        : true);

            // Filtro os produtos pelo id, caso tenham sido informados
            if (filtroGerarEtiqueta.ProdutoCorTamanho != null && filtroGerarEtiqueta.ProdutoCorTamanho.Any() && filtroGerarEtiqueta.ProdutoCorTamanho[0] != null)
            {
                var produtoCorTamanhoId = filtroGerarEtiqueta.ProdutoCorTamanho.Select(p => p.ProdutoCorTamanhoId);
                query = query.Where(x => produtoCorTamanhoId.Contains(x.Id));
            }

            return await query
                            .AsNoTracking()
                            .Select(pct => new ProdutoCorTamanho
                            {
                                Id = pct.Id,
                                CodigoGTINEAN = pct.CodigoGTINEAN,
                                CodigoBarrasInterno = pct.CodigoBarrasInterno,
                                SequenciaCodigoBarras = pct.SequenciaCodigoBarras,
                                SKU = pct.SKU,
                                Tamanho = new Tamanho
                                {
                                    Descricao = pct.Tamanho.Descricao,
                                    PadraoSistema = pct.Tamanho.PadraoSistema
                                },
                                ProdutoCor = new ProdutoCor
                                {
                                    Cor = new Cor
                                    {
                                        Descricao = pct.ProdutoCor.Cor.Descricao,
                                        PadraoSistema = pct.ProdutoCor.Cor.PadraoSistema
                                    },
                                    Produto = new Produto
                                    {
                                        Id = pct.ProdutoCor.Produto.Id,
                                        Nome = pct.ProdutoCor.Produto.Nome,
                                        NomeAbreviado = pct.ProdutoCor.Produto.NomeAbreviado,
                                        Referencia = pct.ProdutoCor.Produto.Referencia,
                                        ProdutoPrecoLojas = pct.ProdutoCor.Produto.ProdutoPrecoLojas
                                            .Where(pcl => pcl.LojaId.Equals(lojaId))
                                            .Select(pcl => new ProdutoPrecoLoja
                                            {
                                                LojaId = pcl.LojaId,
                                                PrecoVenda = pcl.PrecoVenda,
                                                PrecoCusto = pcl.PrecoCusto
                                            })
                                            .ToList(),
                                        UnidadeMedida = new UnidadeMedida
                                        {
                                            Sigla = pct.ProdutoCor.Produto.UnidadeMedida.Sigla
                                        },
                                        Marca = new Marca
                                        {
                                            Nome = pct.ProdutoCor.Produto.Marca.Nome
                                        },
                                        CategoriaProduto = new CategoriaProduto
                                        {
                                            Nome = pct.ProdutoCor.Produto.CategoriaProduto.Nome,
                                        }
                                    }
                                },
                                // Recupero a operação item, da operacao informada, para pegar o valor de venda do item
                                OperacaoItens = pct.OperacaoItens.Where(oi => filtroGerarEtiqueta.OperacaoId.HasValue
                                                                                ? oi.OperacaoId.Equals(filtroGerarEtiqueta.OperacaoId.Value)
                                                                                : false)
                                                .Select(oi => new OperacaoItem
                                                {
                                                    Quantidade = oi.Quantidade
                                                }).ToList()
                            })
                            .ToListAsync();
        }

        public async Task<List<ProdutoCorTamanho>> ListarSelectIdNome(string nomeSkuCodigoExternoBarrasGtinEan)
        {
            var query = FiltrarProdutoCorTamanho(nomeSkuCodigoExternoBarrasGtinEan);

            return await query.Select(p => new ProdutoCorTamanho
            {
                Id = p.Id,
            }).Take(20).ToListAsync();
        }

        public async Task<bool> ProdutoFoiVendido(List<Guid> produtoCorTamanhoId)
        {
            return await DbSet.AnyAsync(pct => produtoCorTamanhoId.Contains(pct.Id) && pct.OperacaoItens.Any());
        }

        public async Task<ProdutoCorTamanho> ObterParaAtualizarCusto(Guid produtoCorTamanhoId, Guid lojaId)
        {
            return await DbSet.Where(pct => pct.Id.Equals(produtoCorTamanhoId))
                            .Include(pct => pct.ProdutoCorTamanhoEstoques.Where(pce => pce.LocalEstoque.LojaId.Equals(lojaId)))
                            .Include(pct => pct.ProdutoCor)
                                .ThenInclude(pc => pc.Produto)
                                    .ThenInclude(p => p.ProdutoPrecoLojas.Where(pcl => pcl.LojaId.Equals(lojaId)))
                            .FirstOrDefaultAsync();
        }

        public GridPaginadaRetorno<ProdutoCorTamanho> ObterItensConferirEstoqueCompleto(GridPaginadaConsulta gridConsulta, Guid lojaEstoqueId, Guid conferenciaId, ICollection<ConferenciaEstoqueFiltros> filtros = null, bool completo = false)
        {
            var gridPaginadaRetorno = new GridPaginadaRetorno<ProdutoCorTamanho>();

            var query = DbSet.Where(x => x.ConferenciaEstoqueItens.Any(c => c.ConferenciaEstoqueId == conferenciaId) ||
                                        (x.ProdutoCorTamanhoEstoques.Any(y => y.LocalEstoqueId == lojaEstoqueId) &&
                                         !x.ConferenciaEstoqueItens.Any(y => y.ConferenciaEstoqueId == conferenciaId)));

            if (filtros != null)
                query = FiltrarProdutosConferencia(query, filtros);

            var retorno = query.Select(x => new ProdutoCorTamanho
            {
                CodigoBarrasInterno = x.CodigoBarrasInterno,
                PesquisaProduto = new PesquisaProduto { Nome = x.PesquisaProduto.Nome },
                ConferenciaEstoqueItens = x.ConferenciaEstoqueItens.Where(c => c.ConferenciaEstoqueId == conferenciaId).ToList(),
                ProdutoCorTamanhoEstoques = x.ProdutoCorTamanhoEstoques.Where(e => e.LocalEstoqueId.Equals(lojaEstoqueId)).ToList(),
                ProdutoCor = new ProdutoCor
                {
                    Cor = new Cor
                    {
                        PadraoSistema = x.ProdutoCor.Cor.PadraoSistema,
                        Descricao = x.ProdutoCor.Cor.Descricao,
                    }
                },
                Tamanho = new Tamanho
                {
                    PadraoSistema = x.Tamanho.PadraoSistema,
                    Descricao = x.Tamanho.Descricao
                }
            }).Where(x => x.ProdutoCorTamanhoEstoques.Where(e => e.LocalEstoqueId.Equals(lojaEstoqueId)).Sum(p => p.EstoqueAtual)
                          != x.ConferenciaEstoqueItens.Where(c => c.ConferenciaEstoqueId == conferenciaId).Sum(c => c.Quantidade) ||
                          (!x.ConferenciaEstoqueItens.Any() && x.ProdutoCorTamanhoEstoques.Any(p => p.EstoqueAtual != 0)) || completo);

            gridPaginadaRetorno.CarregarPaginacao(retorno, gridConsulta);

            return gridPaginadaRetorno;
        }

        private IQueryable<ProdutoCorTamanho> FiltrarProdutosConferencia(IQueryable<ProdutoCorTamanho> query, ICollection<ConferenciaEstoqueFiltros> filtros)
        {
            var marcaId = filtros.Where(f => f.MarcaId != null).Select(f => f.MarcaId);
            var categoriaId = filtros.Where(f => f.CategoriaProdutoId != null).Select(f => f.CategoriaProdutoId);

            if (marcaId != null && marcaId.Count() > 0)
                query = query.Where(x => marcaId.Contains(x.ProdutoCor.Produto.MarcaId));

            if (categoriaId != null && categoriaId.Count() > 0)
                query = query.Where(x => categoriaId.Contains(x.ProdutoCor.Produto.CategoriaProdutoId));

            return query.Where(x => !x.ProdutoCor.Produto.TipoProduto.Equals(TipoProduto.PRODUTO_KIT));
        }

        public async Task<List<Guid>> ObterIdItensConferencia(Guid lojaEstoqueId, Guid conferenciaId, ICollection<ConferenciaEstoqueFiltros> filtros = null)
        {
            var query = DbSet.Where(x => x.ConferenciaEstoqueItens.Any(y => y.ConferenciaEstoqueId == conferenciaId) ||
                                         (x.ProdutoCorTamanhoEstoques.Any(y => y.LocalEstoqueId == lojaEstoqueId) && !x.ConferenciaEstoqueItens.Any(y => y.ConferenciaEstoqueId == conferenciaId)));

            if (filtros != null)
                query = FiltrarProdutosConferencia(query, filtros);

            return await query.Select(x => x.Id).ToListAsync();
        }

        public async Task<List<ProdutoCorTamanho>> ObterRelatoriosEstoque(RelatorioProdutoFiltrosViewModel filtrosEstoqueViewModel, Guid lojaId)
        {
            return await FiltrarCamposListagemProduto(filtrosEstoqueViewModel, lojaId)
                         .Select(pct => new ProdutoCorTamanho
                         {
                             Id = pct.Id,
                             ProdutoCorTamanhoEstoques = pct.ProdutoCorTamanhoEstoques.Where(pcte => pcte.LocalEstoque.LojaId == lojaId &&
                                (!filtrosEstoqueViewModel.LocalEstoqueIds.Any() || filtrosEstoqueViewModel.LocalEstoqueIds.Contains(pcte.LocalEstoque.Id)))
                                                                                 .Select(e => new ProdutoCorTamanhoEstoque
                                                                                 {
                                                                                     EstoqueAtual = e.EstoqueAtual
                                                                                 }).ToList(),

                             ProdutoCor = new ProdutoCor
                             {
                                 Produto = new Produto
                                 {
                                     Referencia = pct.ProdutoCor.Produto.Referencia,
                                     ProdutoPrecoLojas = pct.ProdutoCor.Produto.ProdutoPrecoLojas.Where(l => l.LojaId == lojaId)
                                     .Select(l => new ProdutoPrecoLoja
                                     {
                                         PrecoVenda = l.PrecoVenda,
                                         PrecoCusto = l.PrecoCusto

                                     }).ToList()
                                 }
                             }

                         }).ToListAsync();
        }

        public Task<List<ProdutoCorTamanho>> ObterVariacoesParaExportarNaTabelaPreco(Guid lojaId)
        {
            return DbSet
                .Where(x => x.ProdutoCor.Produto.TipoProduto == TipoProduto.PRODUTO_VARIACAO &&
                            (x.ProdutoCor.Produto.ProdutoCores.Count == 1 || !x.ProdutoCor.Cor.PadraoSistema) &&
                            (x.ProdutoCor.ProdutoCorTamanhos.Count == 1 || !x.Tamanho.PadraoSistema))
                .Select(x => new ProdutoCorTamanho
                {
                    CodigoBarrasInterno = x.CodigoBarrasInterno,
                    Tamanho = new Tamanho
                    {
                        Descricao = x.Tamanho.Descricao,
                        PadraoSistema = x.Tamanho.PadraoSistema
                    },
                    ProdutoCor = new ProdutoCor
                    {
                        Cor = new Cor
                        {
                            Descricao = x.ProdutoCor.Cor.Descricao,
                            PadraoSistema = x.ProdutoCor.Cor.PadraoSistema
                        },
                        Produto = new Produto
                        {
                            Nome = x.ProdutoCor.Produto.Nome,
                            ProdutoPrecoLojas = x.ProdutoCor.Produto.ProdutoPrecoLojas.Where(l => l.LojaId == lojaId)
                                                                                      .Select(p => new ProdutoPrecoLoja
                                                                                      {
                                                                                          PrecoVenda = p.PrecoVenda
                                                                                      }).ToList()
                        }
                    }
                })
                .ToListAsync();
        }

        public async Task<Dictionary<string, Guid>> ObterListaIdPorCodigoBarrasInterno(List<string> codigoBarrasInterno)
        {
            return await DbSet
                            .Where(x => x.ProdutoCor.Produto.TipoProduto == TipoProduto.PRODUTO_VARIACAO &&
                                        codigoBarrasInterno.Contains(x.CodigoBarrasInterno))
                            .Select(x => new ProdutoCorTamanho
                            {
                                Id = x.Id,
                                CodigoBarrasInterno = x.CodigoBarrasInterno,
                            })
                            .ToDictionaryAsync(x => x.CodigoBarrasInterno, x => x.Id);
        }

        public IQueryable<ProdutoCorTamanho> ObterPorData(DateTime dataInicio, DateTime dataFim, TipoData tipoData, Guid lojaId)
        {
            var query = DbSet.Where(
                x => x.ProdutoCor.Produto.TipoProduto != TipoProduto.PRODUTO_VARIACAO ||
                     (x.ProdutoCor.Produto.TipoProduto == TipoProduto.PRODUTO_VARIACAO &&
                     (x.ProdutoCor.Produto.ProdutoCores.Count() == 1 || !x.ProdutoCor.Cor.PadraoSistema) &&
                     (x.ProdutoCor.ProdutoCorTamanhos.Count() == 1 ||
                     !x.Tamanho.PadraoSistema)));

            switch (tipoData)
            {
                case TipoData.DataUltimaAlteracao:
                    query = query.Where(x => x.DataHoraUltimaAlteracao >= dataInicio && x.DataHoraUltimaAlteracao <= dataFim);
                    break;
                case TipoData.DataCadastro:
                    query = query.Where(x => x.DataHoraCadastro >= dataInicio && x.DataHoraCadastro <= dataFim);
                    break;
            }

            return query.Select(pct => new ProdutoCorTamanho
            {
                Id = pct.Id,
                ProdutoCor = new ProdutoCor
                {
                    Cor = new Cor
                    {
                        Descricao = pct.ProdutoCor.Cor.Descricao,
                        PadraoSistema = pct.ProdutoCor.Cor.PadraoSistema
                    },
                    Produto = new Produto
                    {
                        Nome = pct.ProdutoCor.Produto.Nome
                    }
                },
                Tamanho = new Tamanho
                {
                    Id = pct.Tamanho.Id,
                    Descricao = pct.Tamanho.Descricao,
                    PadraoSistema = pct.Tamanho.PadraoSistema,
                },
                ProdutoCorTamanhoEstoques = pct.ProdutoCorTamanhoEstoques.Where(e => e.LocalEstoque.LojaId == lojaId)
                                                 .Select(e => new ProdutoCorTamanhoEstoque
                                                 {
                                                     EstoqueAtual = e.EstoqueAtual
                                                 }).ToList()
            });
        }

        public async Task<List<ProdutoCorTamanho>> ObterProdutoCorTamanhoPorProdutoId(Guid produtoId, Guid lojaId)
        {
            return await DbSet.Where(p => p.ProdutoCor.ProdutoId == produtoId &&
                                     (p.ProdutoCor.Produto.ProdutoCores.Count() == 1 || !p.ProdutoCor.Cor.PadraoSistema) &&
                                     (p.ProdutoCor.ProdutoCorTamanhos.Count() == 1 || !p.Tamanho.PadraoSistema))
                .OrderBy(x => x.Tamanho.SequenciaOrdenacao)
                .Select(x => new ProdutoCorTamanho
                {
                    Id = x.Id,
                    ProdutoCor = new ProdutoCor
                    {
                        Produto = new Produto
                        {
                            Nome = x.ProdutoCor.Produto.Nome,
                            ProdutoPrecoLojas = x.ProdutoCor.Produto.ProdutoPrecoLojas.Where(l => l.LojaId == lojaId)
                                                                                      .Select(p => new ProdutoPrecoLoja
                                                                                      {
                                                                                          PrecoVenda = p.PrecoVenda,
                                                                                          PrecoCusto = p.PrecoCusto
                                                                                      }).ToList()
                        },
                        Cor = new Cor
                        {
                            Descricao = x.ProdutoCor.Cor.Descricao,
                            PadraoSistema = x.ProdutoCor.Cor.PadraoSistema
                        },
                    },
                    Tamanho = new Tamanho
                    {
                        Descricao = x.Tamanho.Descricao,
                        PadraoSistema = x.Tamanho.PadraoSistema
                    }
                })
                .ToListAsync();
        }

        public async Task<List<PesquisaProduto>> ObterParaPopularTabelaPesquisa(Guid? produtoId = null)
        {
            var produtos = DbSet.AsQueryable();

            if (produtoId.HasValue)
                produtos = produtos.Where(x => x.ProdutoCor.ProdutoId == produtoId.Value);

            return await produtos
                            .Select(x => new PesquisaProduto
                            {
                                ProdutoCorTamanhoId = x.Id,
                                ProdutoCorId = x.ProdutoCorId,
                                ProdutoId = x.ProdutoCor.Produto.Id,
                                Nome = x.ProdutoCor.Produto.Nome,
                                Referencia = x.ProdutoCor.Produto.Referencia,
                                SKU = x.SKU,
                                SKUIdentificador = x.ProdutoCor.Produto.SkuIdentificador,
                                CodigoIntegracao = x.ProdutoCor.Produto.CodigoIntegracao,
                                CodigoGTINEAN = x.CodigoGTINEAN,
                                CodigoExterno = x.CodigoExterno,
                                CodigoBarrasInterno = long.Parse(x.CodigoBarrasInterno),
                                SequenciaCodigoBarras = x.SequenciaCodigoBarras
                            })
                            .ToListAsync();
        }

        public async Task<List<PesquisaProduto>> ObterParaPopularTabelaPesquisa(IEnumerable<Guid> produtosId)
        {
            var produtos = DbSet.AsQueryable();

            if (produtosId.Any())
            {
                produtosId = produtosId.Distinct();

                produtos = produtos.Where(x => produtosId.Contains(x.ProdutoCor.Produto.Id));
            }

            return await produtos
                            .Select(x => new PesquisaProduto
                            {
                                ProdutoCorTamanhoId = x.Id,
                                TamanhoId = x.Tamanho.Id,
                                Tamanho = x.Tamanho.PadraoSistema ? string.Empty : x.Tamanho.Descricao,
                                ProdutoCorId = x.ProdutoCorId,
                                CorId = x.ProdutoCor.Cor.Id,
                                Cor = x.ProdutoCor.Cor.PadraoSistema ? string.Empty : x.ProdutoCor.Cor.Descricao,
                                MarcaId = x.ProdutoCor.Produto.Marca.Id,
                                Marca = x.ProdutoCor.Produto.Marca.Nome,
                                ProdutoId = x.ProdutoCor.Produto.Id,
                                Nome = x.ProdutoCor.Produto.Nome,
                                Ativo = x.ProdutoCor.Produto.Ativo,
                                Referencia = x.ProdutoCor.Produto.Referencia,
                                SKU = x.SKU,
                                SKUIdentificador = x.ProdutoCor.Produto.SkuIdentificador,
                                CodigoIntegracao = x.ProdutoCor.Produto.CodigoIntegracao,
                                CodigoGTINEAN = x.CodigoGTINEAN,
                                CodigoExterno = x.CodigoExterno,
                                CodigoBarrasInterno = long.Parse(x.CodigoBarrasInterno),
                                SequenciaCodigoBarras = x.SequenciaCodigoBarras
                            })
                            .ToListAsync();
        }


        public async Task<List<ProdutoCorTamanho>> ObterParaRelatorioCatalogoGrade(RelatorioCatalogoProdutosFiltrosViewModel produtoFiltrosViewModel, Guid lojaId)
        {
            var produtos = FiltrarCamposCatalogoProduto(produtoFiltrosViewModel, lojaId);

            return await produtos.Select(o => new ProdutoCorTamanho
            {
                Id = o.Id,
                Tamanho = new Tamanho { Descricao = o.Tamanho.Descricao, PadraoSistema = o.Tamanho.PadraoSistema },
                ProdutoCorId = o.ProdutoCorId,

                ProdutoCor = new ProdutoCor
                {
                    Id = o.ProdutoCor.Id,
                    Cor = new Cor { Descricao = o.ProdutoCor.Cor.Descricao, PadraoSistema = o.ProdutoCor.Cor.PadraoSistema },
                    ProdutoId = o.ProdutoCor.ProdutoId,
                    Produto = new Produto
                    {
                        Id = o.ProdutoCor.Produto.Id,
                        Nome = o.ProdutoCor.Produto.Nome,
                        Referencia = o.ProdutoCor.Produto.Referencia,
                        Foto = o.ProdutoCor.Produto.Foto,
                        ProdutoPrecoLojas = o.ProdutoCor.Produto.ProdutoPrecoLojas
                        .Where(x => x.LojaId == lojaId)
                        .Select(l => new ProdutoPrecoLoja
                        {
                            PrecoCusto = l.PrecoCusto,
                            PrecoVenda = l.PrecoVenda
                        }).ToList(),
                    },
                },
                ProdutoCorTamanhoEstoques = o.ProdutoCorTamanhoEstoques
                .Where(pcte => o.Id == pcte.ProdutoCorTamanhoId &&
                               pcte.LocalEstoque.LojaId == lojaId &&
                               pcte.LocalEstoque.Ativo)
                .Select(l => new ProdutoCorTamanhoEstoque
                {
                    EstoqueAtual = l.EstoqueAtual,
                    ProdutoCorTamanhoId = l.ProdutoCorTamanhoId,
                }).ToList()

            }).ToListAsync();
        }

        public async Task<List<ProdutoCorTamanhoEstoque>> ObterParaRelatorioCatalogo(RelatorioCatalogoProdutosFiltrosViewModel produtoFiltrosViewModel, Guid lojaId)
        {
            var produtos = FiltrarCamposCatalogoProduto(produtoFiltrosViewModel, lojaId);

            return await produtos.Select(o => new ProdutoCorTamanhoEstoque
            {
                ProdutoCorTamanho = new ProdutoCorTamanho
                {
                    Id = o.Id,
                    ProdutoCor = new ProdutoCor
                    {
                        Id = o.ProdutoCor.Id,
                        ProdutoId = o.ProdutoCor.ProdutoId,

                        Produto = new Produto
                        {
                            Id = o.ProdutoCor.Produto.Id,
                            Nome = o.ProdutoCor.Produto.Nome,
                            Referencia = o.ProdutoCor.Produto.Referencia,
                            Foto = o.ProdutoCor.Produto.Foto,
                            ProdutoPrecoLojas = o.ProdutoCor.Produto.ProdutoPrecoLojas.Where(x => x.LojaId == lojaId).Select(l => new ProdutoPrecoLoja
                            {

                                PrecoCusto = l.PrecoCusto,
                                PrecoVenda = l.PrecoVenda
                            }).ToList(),
                        },
                    },
                }

            }).ToListAsync();
        }

        private IQueryable<ProdutoCorTamanho> FiltrarCamposCatalogoProduto(RelatorioCatalogoProdutosFiltrosViewModel produtoFiltrosViewModel, Guid lojaId)
        {
            var query = FiltrarProdutoCorTamanho(produtoFiltrosViewModel.NomeSkuCodigoExternoBarrasGtinEan, false);

            query = query.Where(p => (produtoFiltrosViewModel.StatusConsulta == StatusConsulta.Todos || p.ProdutoCor.Produto.Ativo.Equals(StatusConsulta.Ativos.Equals(produtoFiltrosViewModel.StatusConsulta))));

            if (produtoFiltrosViewModel.TipoEstoque != TipoFiltroProdutoEstoque.TODOS)
            {
                if (produtoFiltrosViewModel.TipoEstoque == TipoFiltroProdutoEstoque.POSITIVO)
                {
                    query = query.Where(pct => pct.ProdutoCorTamanhoEstoques
                                        .Where(pcte => pct.Id == pcte.ProdutoCorTamanhoId && pcte.LocalEstoque.LojaId == lojaId && pcte.LocalEstoque.Ativo)
                                        .Sum(pcte => pcte.EstoqueAtual) > 0);
                }
                else if (produtoFiltrosViewModel.TipoEstoque == TipoFiltroProdutoEstoque.NEGATIVO)
                {
                    query = query.Where(pct => pct.ProdutoCorTamanhoEstoques
                                        .Where(pcte => pct.Id == pcte.ProdutoCorTamanhoId && pcte.LocalEstoque.LojaId == lojaId && pcte.LocalEstoque.Ativo)
                                        .Sum(pcte => pcte.EstoqueAtual) < 0);
                }
                else if (produtoFiltrosViewModel.TipoEstoque == TipoFiltroProdutoEstoque.ZERADO)
                {
                    query = query.Where(pct => pct.ProdutoCorTamanhoEstoques
                                        .Where(pcte => pct.Id == pcte.ProdutoCorTamanhoId && pcte.LocalEstoque.LojaId == lojaId && pcte.LocalEstoque.Ativo)
                                        .Sum(pcte => pcte.EstoqueAtual) == 0);
                }
                else if (produtoFiltrosViewModel.TipoEstoque == TipoFiltroProdutoEstoque.ABAIXO_MINIMO)
                {
                    query = query.Where(pct => pct.ProdutoCorTamanhoEstoques
                                        .Where(pcte => pct.Id == pcte.ProdutoCorTamanhoId && pcte.LocalEstoque.LojaId == lojaId && pcte.LocalEstoque.Ativo)
                                        .Sum(pcte => pcte.EstoqueAtual) < pct.EstoqueMinimo);
                }
            }

            if (produtoFiltrosViewModel.Cores.Count > 0)
                query = query.Where(p => produtoFiltrosViewModel.Cores.Contains(p.ProdutoCor.CorId));

            if (produtoFiltrosViewModel.Tamanhos.Count > 0)
                query = query.Where(p => produtoFiltrosViewModel.Tamanhos.Contains(p.TamanhoId));

            if (produtoFiltrosViewModel.Marcas.Count > 0)
                query = query.Where(p => produtoFiltrosViewModel.Marcas.Contains(p.ProdutoCor.Produto.MarcaId));

            if (produtoFiltrosViewModel.CategoriasProduto.Count > 0)
                query = query.Where(p => produtoFiltrosViewModel.CategoriasProduto.Contains(p.ProdutoCor.Produto.CategoriaProdutoId));

            if (produtoFiltrosViewModel.Tags != null && produtoFiltrosViewModel.Tags.Any())
                query = query.Where(x => x.ProdutoCor.Produto.TagProdutos.Any(y => produtoFiltrosViewModel.Tags.Contains(y.TagId)));

            return query;
        }

        public IQueryable<ProdutoCorTamanho> ObterEtiquetaPesquisaAvancada(string nomeSkuCodigoExternoBarrasGtinEan, Guid lojaId)
        {
            var query = FiltrarProdutoCorTamanho(nomeSkuCodigoExternoBarrasGtinEan);

            return query.Select(p => new ProdutoCorTamanho
            {
                Id = p.Id,
                Tamanho = new Tamanho
                {
                    Id = p.Tamanho.Id,
                    PadraoSistema = p.Tamanho.PadraoSistema,
                    Descricao = p.Tamanho.Descricao
                },
                ProdutoCor = new ProdutoCor
                {
                    Cor = new Cor
                    {
                        PadraoSistema = p.ProdutoCor.Cor.PadraoSistema,
                        Descricao = p.ProdutoCor.Cor.Descricao
                    },
                    Produto = new Produto
                    {
                        Nome = p.ProdutoCor.Produto.Nome
                    }
                },
                ProdutoCorTamanhoEstoques = p.ProdutoCorTamanhoEstoques.Where(e => e.LocalEstoque.LojaId == lojaId)
                                                 .Select(e => new ProdutoCorTamanhoEstoque
                                                 {
                                                     EstoqueAtual = e.EstoqueAtual
                                                 }).ToList()
            });
        }

        public async Task<List<ItemOrigemCombustivel>> ObterOrigemCombustivel(Guid produtoCorTamanhoId)
        {
            var produtosOrigemCombustivel = await DbSet.Where(p => p.Id == produtoCorTamanhoId)
                             .Select(p => p.ProdutoCor.Produto.ProdutosOrigemCombustivel)
                             .FirstOrDefaultAsync();

            if (produtosOrigemCombustivel == null)
                return new List<ItemOrigemCombustivel>();

            return produtosOrigemCombustivel.Select(p => new ItemOrigemCombustivel
            {
                CodigoUFOrigem = p.CodigoUF,
                IndicadorImportacao = ((int)p.IndicadorImportacao).ToString(),
                PorcentagemOrigem = p.PercentualOriginario,
            }).ToList();
        }

        public async Task<List<ProdutoCorTamanho>> ListarProdutosImportadosBalanca(DateTime dataHoraLocal, Guid lojaId)
        {
            return await DbSet
                    .Where(p => p.Ativo && p.ProdutoCor.Produto.ExportarBalanca)
                    .Select(pct => new ProdutoCorTamanho()
                    {
                        ProdutoCor = new ProdutoCor()
                        {
                            Produto = new Produto()
                            {
                                CodigoIntegracao = pct.ProdutoCor.Produto.CodigoIntegracao,
                                DiasParaValidade = pct.ProdutoCor.Produto.DiasParaValidade,
                                ComposicaoProduto = pct.ProdutoCor.Produto.ComposicaoProduto,
                                UtilizarBalanca = pct.ProdutoCor.Produto.UtilizarBalanca,
                                Nome = pct.ProdutoCor.Produto.Nome,
                                NomeAbreviado = pct.ProdutoCor.Produto.NomeAbreviado,
                                ProdutoPrecoLojas = pct.ProdutoCor.Produto.ProdutoPrecoLojas
                                                       .Where(p => p.LojaId == lojaId)
                                                       .ToList()
                            }
                        },
                        PromocaoItens = pct.PromocaoItens
                                      .AsQueryable()
                                      .Include(i => i.Promocao)
                                      .Where(x => x.Promocao != null && x.Promocao.Ativo &&
                                      x.Promocao.LojasPromocao.Any(lp => lp.LojaId == lojaId) &&
                                      ((x.Promocao.DiasDiferentes &&
                                       x.Promocao.VigenciaInicio.Date <= dataHoraLocal.Date &&
                                       x.Promocao.VigenciaFim.AddDays(1).Date >= dataHoraLocal.Date) ||
                                      (!x.Promocao.DiasDiferentes &&
                                       x.Promocao.VigenciaInicio.Date <= dataHoraLocal.Date &&
                                       x.Promocao.VigenciaFim.Date >= dataHoraLocal.Date)))
                                      .OrderBy(o => o.PrecoVenda)
                                      .ToList(),
                    }).ToListAsync();
        }

        public async Task<IEnumerable<ProdutoComposicaoViewModel>> ListarComposicoesProdutos()
        {
            return await DbSet.Where(p => p.Ativo && p.ProdutoCor.Produto.ExportarBalanca)
                           .Select(pct => new ProdutoComposicaoViewModel()
                           {
                               CodigoProduto = pct.ProdutoCor.Produto.CodigoIntegracao,
                               Composicao = pct.ProdutoCor.Produto.ComposicaoProduto,
                           }).ToListAsync();
        }

        public async Task<bool> VariacaoFoiMovimentada(Guid produtoCorTamanhoId)
        {
            return await Db.OperacaoItem.AnyAsync(i => i.ProdutoCorTamanhoId == produtoCorTamanhoId && i.Status == StatusOperacaoItem.EFETUADO);
        }
	}
}
