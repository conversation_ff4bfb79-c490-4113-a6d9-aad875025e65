import { Flex, Text, Button, Icon, Tr, Td } from '@chakra-ui/react';
import { MouseEvent } from 'react';
import { IoIosCheckmarkCircleOutline } from 'react-icons/io';

import { moneyMask } from 'helpers/format/fieldsMasks';

import { Filtros, Produto } from 'store/PDV/ConsultaProdutoPdv';

import { PaginationData } from 'components/update/Pagination';
import { PagedTable } from 'components/update/Table/PagedTable';

import { PesquisarEstoqueIcon } from 'icons';

import { EnumTelasConsultaProdutos } from './hooks/useConsultaProdutos';

interface ConsultaProdutoProps {
  tela: EnumTelasConsultaProdutos;
  casasDecimais: { casasDecimaisQuantidade: number };
  produtoSelecionado: Produto | null;
  isOpenDrawer: boolean;
  lancarProdutoSimples: (produto: Produto) => void;
  setProdutoSelecionado: (produto: Produto | null) => void;
  setModalVisualizarEstoqueEstaAberto: (aberto: boolean) => void;
  onCloseDrawer: () => void;
  paginationHandle: (
    paginationData: PaginationData,
    data?: Filtros | undefined
  ) => Promise<void>;
  produtos: Produto[];
  totalRegistros: number;
  exibirBotaoVerVariacoes?: (produto: Produto) => boolean;
  exibirConsultaEstoque: boolean;
  handleCliqueNoProdutoItem: (e: MouseEvent, produto: Produto) => void;
  handleCliqueDuploNoProdutoItem: (produto: Produto) => void;
  handleCliqueNoEscolherVariacao: (e: MouseEvent, produto: Produto) => void;
  handleCliqueDuploNoEscolherVariacao: (
    e: MouseEvent,
    produto: Produto
  ) => void;
}

export const ConsultaProdutosTabela = ({
  tela,
  casasDecimais,
  produtoSelecionado,
  isOpenDrawer,
  setProdutoSelecionado,
  setModalVisualizarEstoqueEstaAberto,
  onCloseDrawer,
  paginationHandle,
  produtos,
  totalRegistros,
  exibirBotaoVerVariacoes = () => false,
  exibirConsultaEstoque = false,
  handleCliqueNoProdutoItem,
  handleCliqueDuploNoProdutoItem,
  handleCliqueNoEscolherVariacao,
  handleCliqueDuploNoEscolherVariacao,
}: ConsultaProdutoProps) => {
  if (tela !== EnumTelasConsultaProdutos.PRODUTOS) {
    return null;
  }

  const cabecalhoTabela = () => {
    return [
      {
        content: 'Nome',
        key: 'Nome',
        isOrderable: true,
        width: 'auto',
        pl: totalRegistros > 0 ? '72px !important' : '40px !important',
      },
      {
        content: (
          <Flex alignItems="center" justifyContent="flex-end">
            Preço de venda
          </Flex>
        ),
        key: 'PrecoVenda',
        isOrderable: false,
        width: '150px',
      },
      {
        content: (
          <Flex alignItems="center" justifyContent="flex-end">
            Estoque
          </Flex>
        ),
        key: 'Estoque',
        isOrderable: false,
        width: ['120px', '120px', '200px'],
      },
      {
        content: '',
        key: '',
        isOrderable: false,
        width: '80px',
      },
    ];
  };

  return (
    <>
      <PagedTable
        loadColumnsData={paginationHandle}
        itemsTotalCount={totalRegistros}
        defaultKeyOrdered="Nome"
        itensPerPage={25}
        overflowY="auto"
        maxH="calc(100vh - 150px)"
        tableHeaders={cabecalhoTabela()}
        renderTableRows={produtos?.map((produto) => {
          const possuiEstoque = produto.estoque > 0;

          return (
            <Tr
              key={produto.id}
              sx={{
                '& td': {
                  color: possuiEstoque ? 'gray.700' : 'red.500',
                },
              }}
              height="52px"
              onClick={(e) => handleCliqueNoProdutoItem(e, produto)}
              onDoubleClick={() => handleCliqueDuploNoProdutoItem(produto)}
              cursor="pointer"
              bg={produtoSelecionado?.id === produto.id ? 'purple.50' : ''}
            >
              <Td>
                <Text
                  display="flex"
                  alignItems="center"
                  gap="12px"
                  justifyContent="flex-start"
                  pl={produtoSelecionado?.id === produto.id ? '0px' : '32px'}
                >
                  {produtoSelecionado?.id === produto.id && (
                    <IoIosCheckmarkCircleOutline
                      color="#482ABC"
                      fontSize="20px"
                    />
                  )}
                  {produto.nome}
                  {exibirBotaoVerVariacoes(produto) && (
                    <Button
                      colorScheme="teal"
                      variant="solid"
                      height="16px"
                      fontWeight="500"
                      fontSize="12px"
                      ml="8px"
                      px="8px"
                      py="10px"
                      onClick={(e) =>
                        handleCliqueNoEscolherVariacao(e, produto)
                      }
                      onDoubleClick={(e) =>
                        handleCliqueDuploNoEscolherVariacao(e, produto)
                      }
                    >
                      Ver variações
                    </Button>
                  )}
                </Text>
              </Td>
              <Td isNumeric>
                <Flex justifyContent="flex-end">
                  {moneyMask(produto.precoVenda, true)}
                </Flex>
              </Td>
              <Td
                textAlign="end"
                color={produto.estoque <= 0 ? 'red.400' : undefined}
              >
                <Flex alignItems="center" justify="flex-end" gap="24px">
                  <Text>
                    {produto.estoque.toLocaleString('locale', {
                      minimumFractionDigits:
                        casasDecimais.casasDecimaisQuantidade,
                      maximumFractionDigits:
                        casasDecimais.casasDecimaisQuantidade,
                    })}
                  </Text>
                </Flex>
              </Td>
              {exibirConsultaEstoque ? (
                <Td pt="0" pb="0">
                  <Icon
                    color={produto.estoque <= 0 ? 'red.400' : 'primary.50'}
                    cursor="pointer"
                    as={PesquisarEstoqueIcon}
                    fontSize="lg"
                    onClick={() => {
                      if (isOpenDrawer) onCloseDrawer();
                      setModalVisualizarEstoqueEstaAberto(true);
                      setProdutoSelecionado(produto);
                    }}
                  />
                </Td>
              ) : (
                <Td />
              )}
            </Tr>
          );
        })}
      />
    </>
  );
};
