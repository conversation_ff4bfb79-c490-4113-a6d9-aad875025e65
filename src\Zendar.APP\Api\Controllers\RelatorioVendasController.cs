﻿using Microsoft.AspNetCore.Mvc;
using System;
using System.Threading.Tasks;
using Zendar.APP.Constants;
using Zendar.APP.Extensions;
using Zendar.Business.Interfaces.Services;
using Zendar.Business.Services.RelatorioVendasServices.CurvaAbcServices;
using Zendar.Business.Services.RelatorioVendasServices.HistoricoVendaServices;
using Zendar.Business.Services.RelatorioVendasServices.RelatorioVendaServices;
using Zendar.Business.ViewModels;
using Zendar.Business.ViewModels.Relatorios.Vendas;
using Zendar.Data.Helpers;
using Zendar.Data.ViewModels;

namespace Zendar.APP.Api.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class RelatorioVendasController : MainController
    {
        private readonly ICurvaAbcService _curvaAbcService;
        private readonly IHistoricoVendaService _historicoVendaService;
        private readonly IRelatorioVendaService _vendaRelatorioService;

        public RelatorioVendasController(INotificador notificador,
                                        ICurvaAbcService curvaAbcService,
                                        IHistoricoVendaService historicoVendaService,
                                        IRelatorioVendaService vendaRelatorioService) : base(notificador)
        {
            _curvaAbcService = curvaAbcService;
            _historicoVendaService = historicoVendaService;
            _vendaRelatorioService = vendaRelatorioService;
        }

        #region Curva ABC
        [HttpGet(Endpoints.ObterInformacoesCurvaAbc)]
        [ClaimsAuthorize(Permissoes.RelatorioCurvaAbc)]
        public async Task<ActionResult<CurvaAbcViewModel>> ObterInformacoesCurvaAbc([FromQuery] FiltrosRelatorioCurvaAbcViewModel filtrosRelatorioCurvaAbc)
        {
            try
            {
                return CustomResponse(await _curvaAbcService.ObterInformacoesCurvaAbc(filtrosRelatorioCurvaAbc));
            }
            catch (Exception ex)
            {
                return CustomResponse(ex);
            }
        }

        [HttpGet(Endpoints.ObterParaImprimirCurvaAbc)]
        [ClaimsAuthorize(Permissoes.RelatorioCurvaAbc)]
        public async Task<ActionResult<byte[]>> ObterParaImprimirCurvaAbc([FromQuery] FiltrosRelatorioCurvaAbcViewModel filtrosRelatorioCurvaAbc)
        {
            try
            {
                return CustomResponse(await _curvaAbcService.ObterParaImprimirCurvaAbc(filtrosRelatorioCurvaAbc));
            }
            catch (Exception ex)
            {
                return CustomResponse(ex);
            }
        }

        [HttpGet(Endpoints.EnviarEmailCurvaAbc)]
        [ClaimsAuthorize(Permissoes.RelatorioCurvaAbc)]
        public async Task<ActionResult> EnviarPorEmailCurvaAbc([FromQuery] FiltrosRelatorioCurvaAbcViewModel filtrosRelatorioCurvaAbc, [FromQuery] string[] emails)
        {
            try
            {
                await _curvaAbcService.EnviarPorEmailCurvaAbc(filtrosRelatorioCurvaAbc, emails);
            }
            catch (Exception ex)
            {
                return CustomResponse(ex);
            }
            return CustomResponse();
        }

        [HttpGet(Endpoints.ExportarCsvCurvaAbc)]
        [ClaimsAuthorize(Permissoes.RelatorioCurvaAbc)]
        public async Task<ActionResult<byte[]>> ExportarCsvCurvaAbc([FromQuery] FiltrosRelatorioCurvaAbcViewModel filtrosRelatorioCurvaAbc)
        {
            try
            {
                return CustomResponse(await _curvaAbcService.ExportarCsvCurvaAbc(filtrosRelatorioCurvaAbc));
            }
            catch (Exception ex)
            {
                return CustomResponse(ex);
            }
        }
        #endregion

        #region Histórico Vendas
        [HttpGet(Endpoints.ListarPaginadoHistoricoVenda)]
        [ClaimsAuthorize()]
        public async Task<ActionResult<GridPaginadaRetornoHistoricoVenda<HistoricoVendaViewModel>>> ListarPaginadoHistoricoVendas([FromQuery] GridPaginadaConsulta consulta, [FromQuery] FiltroRelatoriosMetaComissaoViewModel filtros)
        {
            try
            {
                return CustomResponse(await _historicoVendaService.ListarPaginado(consulta, filtros));
            }
            catch (Exception ex)
            {
                NotificarErro(ex);
                return CustomResponse();
            }
        }

        [HttpGet(Endpoints.RelatorioListagemHistoricoVenda)]
        [ClaimsAuthorize()]
        public async Task<ActionResult<byte[]>> RelatorioListagemHistoricoVendas([FromQuery] GridPaginadaConsulta consulta, [FromQuery] FiltroRelatoriosMetaComissaoViewModel filtros)
        {
            try
            {
                return CustomResponse(await _historicoVendaService.RelatorioListagem(consulta, filtros));
            }
            catch (Exception ex)
            {
                NotificarErro(ex);
                return CustomResponse();
            }
        }

        [HttpGet(Endpoints.EnviarEmailHistoricoVenda)]
        [ClaimsAuthorize()]
        public async Task<ActionResult> EnviarEmailHistoricoVendas([FromQuery] GridPaginadaConsulta consulta, [FromQuery] FiltroRelatoriosMetaComissaoViewModel filtros, [FromQuery] string[] emails)
        {
            try
            {
                await _historicoVendaService.EnviarEmailRelatorioListagem(consulta, filtros, emails);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);
            }
            return CustomResponse();
        }
        #endregion

        #region Relatório de Vendas
        [HttpPost(Endpoints.RelatorioVendasTotalizadasPorVendedorGrafico)]
        [ClaimsAuthorize(Permissoes.RelatorioVendasTotalizadasPorVendedoresGrafico)]
        public async Task<ActionResult<byte[]>> RelatorioVendasTotalizadasPorVendedorGrafico([FromBody] RelatorioVendaFiltrosViewModel filtro)
        {
            try
            {
                var relatorio = await _vendaRelatorioService.GerarRelatorio(new FiltrosPorTipoRelatorioVendasViewModel
                {
                    TipoRelatorio = Data.Enums.TipoRelatorioVenda.VENDA_POR_VENDEDOR_GRAFICO,
                    FiltroRelatoriosVendaViewModel = filtro,
                });

                return CustomResponse(relatorio);
            }
            catch (Exception ex)
            {
                NotificarErro(ex, filtro);
                return CustomResponse();
            }
        }

        [HttpPost(Endpoints.RelatorioLucroAgrupadoPorDia)]
        [ClaimsAuthorize(Permissoes.RelatorioLucroAgrupadoPorDia)]
        public async Task<ActionResult<byte[]>> RelatorioLucroAgrupadoPorDia([FromBody] RelatorioVendaFiltrosViewModel filtro)
        {
            try
            {
                var relatorio = await _vendaRelatorioService.GerarRelatorio(new FiltrosPorTipoRelatorioVendasViewModel
                {
                    TipoRelatorio = Data.Enums.TipoRelatorioVenda.LUCRO_POR_DIA,
                    FiltroRelatoriosVendaViewModel = filtro,
                });

                return CustomResponse(relatorio);
            }
            catch (Exception ex)
            {
                NotificarErro(ex, filtro);
                return CustomResponse();
            }
        }

        [HttpPost(Endpoints.RelatorioVendasPorFormaRecebimento)]
        [ClaimsAuthorize(Permissoes.RelatorioVendasPorFormaRecebimento)]
        public async Task<ActionResult<byte[]>> RelatorioVendasPorFormaRecebimento([FromBody] RelatorioVendaFiltrosViewModel vendaFiltrosViewModel)
        {
            try
            {
                vendaFiltrosViewModel.TipoRelatorio = Data.Enums.TipoRelatorioVenda.VENDA_POR_RECEBIMENTO;
                return CustomResponse(await _vendaRelatorioService.GerarRelatorio(new FiltrosPorTipoRelatorioVendasViewModel
                {
                    TipoRelatorio = vendaFiltrosViewModel.TipoRelatorio,
                    FiltroRelatoriosVendaViewModel = vendaFiltrosViewModel
                }));
            }
            catch (Exception ex)
            {
                NotificarErro(ex, vendaFiltrosViewModel);
                return CustomResponse();
            }
        }

        [HttpPost(Endpoints.RelatorioVendasPorEntregador)]
        [ClaimsAuthorize(Permissoes.RelatorioVendasPorEntregador)]
        public async Task<ActionResult<byte[]>> RelatorioVendasPorEntregador([FromBody] RelatorioVendaFiltrosViewModel vendaFiltrosViewModel)
        {
            try
            {
                vendaFiltrosViewModel.TipoRelatorio = Data.Enums.TipoRelatorioVenda.VENDA_DETALHAMENTO_POR_ENTREGADOR;
                return CustomResponse(await _vendaRelatorioService.GerarRelatorio(new FiltrosPorTipoRelatorioVendasViewModel
                {
                    TipoRelatorio = vendaFiltrosViewModel.TipoRelatorio,
                    FiltroRelatoriosVendaViewModel = vendaFiltrosViewModel
                }));
            }
            catch (Exception ex)
            {
                NotificarErro(ex, vendaFiltrosViewModel);
                return CustomResponse();
            }
        }

        [HttpPost(Endpoints.RelatorioVendasPorDia)]
        [ClaimsAuthorize(Permissoes.RelatorioVendasPorDia)]
        public async Task<ActionResult<byte[]>> RelatorioVendasPorDia([FromBody] RelatorioVendaFiltrosViewModel vendaFiltrosViewModel)
        {
            try
            {
                vendaFiltrosViewModel.TipoRelatorio = Data.Enums.TipoRelatorioVenda.VENDA_POR_DIA;
                return CustomResponse(await _vendaRelatorioService.GerarRelatorio(new FiltrosPorTipoRelatorioVendasViewModel
                {
                    TipoRelatorio = vendaFiltrosViewModel.TipoRelatorio,
                    FiltroRelatoriosVendaViewModel = vendaFiltrosViewModel
                }));
            }
            catch (Exception ex)
            {
                NotificarErro(ex, vendaFiltrosViewModel);
                return CustomResponse();
            }
        }

        [HttpPost(Endpoints.RelatorioVendasSimplificadas)]
        [ClaimsAuthorize(Permissoes.RelatorioVendasSimplificadas)]
        public async Task<ActionResult<byte[]>> RelatorioVendasSimplificadas([FromBody] RelatorioVendaFiltrosViewModel vendaFiltrosViewModel)
        {
            try
            {
                return CustomResponse(await _vendaRelatorioService.GerarRelatorio(new FiltrosPorTipoRelatorioVendasViewModel
                {
                    TipoRelatorio = vendaFiltrosViewModel.TipoRelatorio,
                    FiltroRelatoriosVendaViewModel = vendaFiltrosViewModel
                }));
            }
            catch (Exception ex)
            {
                NotificarErro(ex, vendaFiltrosViewModel);
                return CustomResponse();
            }
        }

        [HttpPost(Endpoints.RelatorioVendasTotalizadasPorProduto)]
        [ClaimsAuthorize(Permissoes.RelatorioVendasTotalizadasPorProdutos)]
        public async Task<ActionResult<byte[]>> RelatorioVendasTotalizadasPorProduto([FromBody] RelatorioVendaFiltrosViewModel vendaFiltrosViewModel)
        {
			try
			{
				vendaFiltrosViewModel.TipoRelatorio = Data.Enums.TipoRelatorioVenda.VENDA_POR_PRODUTO;
				return CustomResponse(await _vendaRelatorioService.GerarRelatorio(new FiltrosPorTipoRelatorioVendasViewModel
				{
					TipoRelatorio = vendaFiltrosViewModel.TipoRelatorio,
					FiltroRelatoriosVendaViewModel = vendaFiltrosViewModel
				}));
			}
			catch (Exception ex)
			{
				NotificarErro(ex, vendaFiltrosViewModel);
				return CustomResponse();
			}
		}

        [HttpPost(Endpoints.RelatorioVendasTotalizadasPorProdutoSimples)]
        [ClaimsAuthorize(Permissoes.RelatorioVendasTotalizadasPorProdutosSimples)]
        public async Task<ActionResult<byte[]>> RelatorioVendasTotalizadasPorProdutoSimples([FromBody] RelatorioVendaFiltrosViewModel vendaFiltrosViewModel)
        {
            try
            {
                vendaFiltrosViewModel.TipoRelatorio = Data.Enums.TipoRelatorioVenda.VENDA_TOTALIZADA_POR_PRODUTO;
                return CustomResponse(await _vendaRelatorioService.GerarRelatorio(new FiltrosPorTipoRelatorioVendasViewModel
                {
                    TipoRelatorio = vendaFiltrosViewModel.TipoRelatorio,
                    FiltroRelatoriosVendaViewModel = vendaFiltrosViewModel
                }));
            }
            catch (Exception ex)
            {
                NotificarErro(ex, vendaFiltrosViewModel);
                return CustomResponse();
            }
        }

        [HttpPost(Endpoints.RelatorioVendasTotalizadasPorEntregador)]
        [ClaimsAuthorize(Permissoes.RelatorioVendasTotalizadasPorEntregadores)]
        public async Task<ActionResult<byte[]>> RelatorioVendasTotalizadasPorEntregador([FromBody] RelatorioVendaFiltrosViewModel vendaFiltrosViewModel)
        {
            try
            {
                vendaFiltrosViewModel.TipoRelatorio = Data.Enums.TipoRelatorioVenda.VENDA_POR_ENTREGADOR_RESUMO;
                return CustomResponse(await _vendaRelatorioService.GerarRelatorio(new FiltrosPorTipoRelatorioVendasViewModel
                {
                    TipoRelatorio = vendaFiltrosViewModel.TipoRelatorio,
                    FiltroRelatoriosVendaViewModel = vendaFiltrosViewModel
                }));
            }
            catch (Exception ex)
            {
                NotificarErro(ex, vendaFiltrosViewModel);
                return CustomResponse();
            }
        }

        [HttpPost(Endpoints.RelatorioVendasValoresAdicionais)]
        [ClaimsAuthorize(Permissoes.RelatorioVendasValoresAdicionais)]
        public async Task<ActionResult<byte[]>> RelatorioVendasValoresAdicionais([FromBody] RelatorioVendaFiltrosViewModel vendaFiltrosViewModel)
        {
            try
            {
                vendaFiltrosViewModel.TipoRelatorio = Data.Enums.TipoRelatorioVenda.VENDA_VALORES_ADICIONAIS;
                return CustomResponse(await _vendaRelatorioService.GerarRelatorio(new FiltrosPorTipoRelatorioVendasViewModel
                {
                    TipoRelatorio = vendaFiltrosViewModel.TipoRelatorio,
                    FiltroRelatoriosVendaViewModel = vendaFiltrosViewModel
                }));
            }
            catch (Exception ex)
            {
                NotificarErro(ex, vendaFiltrosViewModel);
                return CustomResponse();
            }
        }
        [HttpPost(Endpoints.RelatorioVendasTotalizadasPorVendedor)]
        [ClaimsAuthorize(Permissoes.RelatorioVendasTotalizadasPorVendedores)]
        public async Task<ActionResult<byte[]>> RelatorioVendasTotalizadasPorVendedor([FromBody] RelatorioVendaFiltrosViewModel filtro)
        {
            try
            {
                var relatorio = await _vendaRelatorioService.GerarRelatorio(new FiltrosPorTipoRelatorioVendasViewModel
                {
                    TipoRelatorio = Data.Enums.TipoRelatorioVenda.VENDA_TOTALIZADA_POR_VENDEDORES,
                    FiltroRelatoriosVendaViewModel = filtro,
                });

                return CustomResponse(relatorio);
            }
            catch (Exception ex)
            {
                NotificarErro(ex, filtro);
                return CustomResponse();
            }
        }

        [HttpPost(Endpoints.RelatorioVendasTotalizadasPorVendedorGraficoFrenteCaixa)]
        [ClaimsAuthorize(Permissoes.RelatorioVendasTotalizadasPorVendedoresGraficoFrenteCaixa)]
        public async Task<ActionResult<byte[]>> RelatorioVendasTotalizadasPorVendedorGraficoFrenteCaixa([FromBody] RelatorioVendaFiltrosViewModel filtro)
        {
            try
            {
                var relatorio = await _vendaRelatorioService.GerarRelatorio(new FiltrosPorTipoRelatorioVendasViewModel
                {
                    TipoRelatorio = Data.Enums.TipoRelatorioVenda.VENDA_POR_VENDEDOR_GRAFICO_FRENTE_CAIXA,
                    FiltroRelatoriosVendaViewModel = filtro,
                });

                return CustomResponse(relatorio);
            }
            catch (Exception ex)
            {
                NotificarErro(ex, filtro);
                return CustomResponse();
            }
        }
        #endregion
    }
}
