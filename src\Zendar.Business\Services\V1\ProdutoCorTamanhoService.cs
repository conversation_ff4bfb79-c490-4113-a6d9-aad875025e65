﻿using LinqKit;
using Microsoft.EntityFrameworkCore;
using Multiempresa.Shared.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Linq.Expressions;
using System.Threading.Tasks;
using Zendar.Business.Interfaces;
using Zendar.Business.Interfaces.Services;
using Zendar.Business.Services.PesquisaProdutoServices;
using Zendar.Business.Services.V2.TabelaPrecoV2Services.CalcularPrecoProdutoService;
using Zendar.Business.ViewModels;
using Zendar.Data.Enums;
using Zendar.Data.Helpers;
using Zendar.Data.Interfaces.Aplicacao;
using Zendar.Data.Models.Aplicacao;
using Zendar.Data.ViewModels;

namespace Zendar.Business.Services
{
    public class ProdutoCorTamanhoService : BaseService, IProdutoCorTamanhoService
    {
        private readonly IAspNetUserInfo _aspNetUserInfo;

        private readonly IPesquisaProdutoService _pesquisaProdutoService;
        private readonly ICategoriaProdutoService _categoriaProdutoService;
        private readonly ICalculoPrecoProdutoV2Service _calculoPrecoProdutoV2Service;

        private readonly IProdutoCorTamanhoRepository _produtoCorTamanhoRepository;
        private readonly IProdutoCorTamanhoEstoqueRepository _produtoCorTamanhoEstoqueRepository;
        private readonly IProdutoRepository _produtoRepository;
        private readonly ITabelaPrecoRepository _tabelaPrecoRepository;

        public ProdutoCorTamanhoService(
            INotificador notificador,
            IAspNetUserInfo aspNetUserInfo,
            IPesquisaProdutoService pesquisaProdutoService,
            ICategoriaProdutoService categoriaProdutoService,
            ICalculoPrecoProdutoV2Service calculoPrecoProdutoV2Service,
            IProdutoCorTamanhoRepository produtoCorTamanhoRepository,
            IProdutoCorTamanhoEstoqueRepository produtoCorTamanhoEstoqueRepository,
            IProdutoRepository produtoRepository,
            ITabelaPrecoRepository tabelaPrecoRepository)
            : base(notificador)
        {
            _aspNetUserInfo = aspNetUserInfo;
            _pesquisaProdutoService = pesquisaProdutoService;
            _categoriaProdutoService = categoriaProdutoService;
            _calculoPrecoProdutoV2Service = calculoPrecoProdutoV2Service;
            _produtoCorTamanhoRepository = produtoCorTamanhoRepository;
            _produtoCorTamanhoEstoqueRepository = produtoCorTamanhoEstoqueRepository;
            _produtoRepository = produtoRepository;
            _tabelaPrecoRepository = tabelaPrecoRepository;
        }

        public void Dispose()
        {
            _pesquisaProdutoService?.Dispose();
            _categoriaProdutoService?.Dispose();
            _calculoPrecoProdutoV2Service?.Dispose();

            _produtoCorTamanhoRepository?.Dispose();
            _produtoCorTamanhoEstoqueRepository?.Dispose();
            _produtoRepository?.Dispose();
            _tabelaPrecoRepository?.Dispose();
        }

        public async Task<List<IdNomeViewModel>> ListarSelect(string nomeSkuCodigoExternoBarrasGtinEan)
        {
            var lista = new List<IdNomeViewModel>();

            var listaProdutoCorTamanho = await _pesquisaProdutoService.ListarSelectProdutoCorTamanho(nomeSkuCodigoExternoBarrasGtinEan);

            if (listaProdutoCorTamanho != null)
            {
                foreach (var item in listaProdutoCorTamanho)
                {
                    //Resolvendo inner joins 
                    item.ProdutoCorTamanho.ProdutoCor = new ProdutoCor
                    {
                        Cor = item.ProdutoCor.Cor,
                        Produto = new Produto { Nome = item.Nome }
                    };

                    lista.Add(new IdNomeViewModel
                    {
                        Id = item.ProdutoCorTamanhoId,
                        Nome = item.ProdutoCorTamanho.FormatarDescricaoCompleta()
                    });
                }
            }

            return lista;
        }

        public async Task<ProdutoSelectNotaFiscalViewModel> ObterInformacoesParaNotaFiscal(Guid id)
        {
            var informacoesDoProduto = new ProdutoSelectNotaFiscalViewModel();

            var produtoCorTamanho = await _produtoCorTamanhoRepository.ObterInformacoesParaNotaFiscal(id, _aspNetUserInfo.LojaId.Value);
            if (produtoCorTamanho != null)
            {
                informacoesDoProduto = new ProdutoSelectNotaFiscalViewModel
                {
                    Id = produtoCorTamanho.Id,
                    Preco = produtoCorTamanho.ProdutoCor.Produto.ProdutoPrecoLojas.First().PrecoVenda,
                    Nome = await FormatarDescricaoCompletaProduto(produtoCorTamanho.Id),
                    IcmsStRetidoBaseCalculo = produtoCorTamanho.ProdutoCor.Produto.IcmsStRetidoBaseCalculo,
                    IcmsStRetidoValor = produtoCorTamanho.ProdutoCor.Produto.IcmsStRetidoValor,
                    FcpStRetidoBaseCalculo = produtoCorTamanho.ProdutoCor.Produto.FcpStRetidoBaseCalculo,
                    FcpStRetidoValor = produtoCorTamanho.ProdutoCor.Produto.FcpStRetidoValor,
                    IcmsAliquota = produtoCorTamanho.ProdutoCor.Produto.IcmsAliquota,
                    PisAliquota = produtoCorTamanho.ProdutoCor.Produto.PisAliquota,
                    CofinsAliquota = produtoCorTamanho.ProdutoCor.Produto.CofinsAliquota,
                    FcpAliquota = produtoCorTamanho.ProdutoCor.Produto.FcpAliquota,
                    IcmsReducaoBaseCalculo = produtoCorTamanho.ProdutoCor.Produto.IcmsReducaoBaseCalculo,
                    PesoBruto = produtoCorTamanho.PesoBruto,
                    PesoLiquido = produtoCorTamanho.PesoLiquido,
                    AliquotaAdREmICMSRetido = produtoCorTamanho.ProdutoCor.Produto.AliquotaAdREmICMSRetido,
                    QuantidadeBCMonoRetido = produtoCorTamanho.ProdutoCor.Produto.QuantidadeBCMonoRetido,
                };

                if (!string.IsNullOrEmpty(produtoCorTamanho.CodigoGTINEAN))
                {
                    informacoesDoProduto.InformacoesAdicionais = $"Código de Barras: {produtoCorTamanho.CodigoGTINEAN}";
                }

                if (!string.IsNullOrEmpty(produtoCorTamanho.ProdutoCor.Produto.CodigoCest))
                {
                    var descricaoCest = $"CEST: {produtoCorTamanho.ProdutoCor.Produto.CodigoCest}";
                    informacoesDoProduto.InformacoesAdicionais = string.IsNullOrEmpty(informacoesDoProduto.InformacoesAdicionais) ? descricaoCest : $"{informacoesDoProduto.InformacoesAdicionais} | {descricaoCest}";
                }

                var descricaoUnidadesMedida = $"Unidade Comercial: {produtoCorTamanho.ProdutoCor.Produto.UnidadeMedida.Sigla}" +
                                              $" | Unidade Tributável: {produtoCorTamanho.ProdutoCor.Produto.UnidadeTributavel.UnidadeTributavel}";

                informacoesDoProduto.InformacoesAdicionais = string.IsNullOrEmpty(informacoesDoProduto.InformacoesAdicionais) ? descricaoUnidadesMedida : $"{informacoesDoProduto.InformacoesAdicionais} | {descricaoUnidadesMedida}";

            }

            return informacoesDoProduto;
        }

        public async Task<List<ProdutoCorTamanhoIdentificadores>> ObterTamanhosProdutoCor(Guid produtoCorId)
        {
            var produtoCorTamanhos = await _produtoCorTamanhoRepository.FindAllSelectAsNoTracking(
                                    t => t.ProdutoCorId == produtoCorId && t.Ativo,
                                    t => new ProdutoCorTamanho
                                    {
                                        Id = t.Id,
                                        CodigoGTINEAN = t.CodigoGTINEAN,
                                        CodigoExterno = t.CodigoExterno,
                                        CodigoBarrasInterno = t.CodigoBarrasInterno,
                                        SequenciaCodigoBarras = t.SequenciaCodigoBarras,
                                        SKU = t.SKU,
                                        Tamanho = new Tamanho
                                        {
                                            Descricao = t.Tamanho.Descricao,
                                            PadraoSistema = t.Tamanho.PadraoSistema
                                        },
                                        ProdutoCor = new ProdutoCor
                                        {
                                            Produto = new Produto
                                            {
                                                UnidadeMedida = new UnidadeMedida
                                                {
                                                    VolumeUnitario = t.ProdutoCor.Produto.UnidadeMedida.VolumeUnitario
                                                },
                                                ProdutoPrecoLojas = t.ProdutoCor.Produto.ProdutoPrecoLojas
                                            }
                                        }
                                    });

            var tabelaPreco = await _tabelaPrecoRepository.FirstOrDefaultAsNoTracking(t => t.PadraoSistema);

            return produtoCorTamanhos.Select(t => new ProdutoCorTamanhoIdentificadores
            {
                Id = t.Id,
                Nome = t.Tamanho.Descricao,
                PadraoSistema = t.Tamanho.PadraoSistema,
                VolumeUnitario = t.ProdutoCor.Produto.UnidadeMedida.VolumeUnitario,
                CodigoGTINEAN = t.CodigoGTINEAN,
                CodigoExterno = t.CodigoExterno,
                CodigoBarrasInterno = t.CodigoBarrasInterno,
                SequenciaCodigoBarras = t.SequenciaCodigoBarras,
                SKU = t.SKU,
                PrecoVenda = _calculoPrecoProdutoV2Service.CalcularPreco(tabelaPreco.Id, t.Id, _aspNetUserInfo.LojaId.Value).Result
            }).ToList();
        }

        public async Task<string> FormatarDescricaoCompletaProduto(Guid? produtoCorTamanhoId = null, ProdutoCorTamanho produtoCorTamanho = null)
        {
            if (produtoCorTamanhoId.HasValue)
                produtoCorTamanho = await _produtoCorTamanhoRepository.ConsultarDescricaoProduto(produtoCorTamanhoId.Value);

            return produtoCorTamanho.FormatarDescricaoCompleta();
        }

        public async Task<List<ProdutoCorTamanhoSelectKitsViewModel>> ListarSelectTamanhoKits(string nomeSkuCodigoExternoBarrasGtinEan, Guid produtoCorId)
        {
            return await _produtoCorTamanhoRepository.ListarSelectTamanhoKits(nomeSkuCodigoExternoBarrasGtinEan, produtoCorId, _aspNetUserInfo.LojaId.Value);
        }

        public List<ProdutoCorTamanhoConferenciaEstoqueViewModel> ListarSelectConferenciaEstoque(ProdutoCorTamanhoConferenciaEstoqueListaSelectParameros produtoCorTamanhoConferenciaEstoqueListaSelectParameros)
        {
            List<ProdutoCorTamanhoConferenciaEstoqueViewModel> ListaRetorno = new List<ProdutoCorTamanhoConferenciaEstoqueViewModel>();

            var listaIdCategorias = new List<Guid>();

            foreach (var categoriaId in produtoCorTamanhoConferenciaEstoqueListaSelectParameros.ListaCategorias)
            {
                listaIdCategorias.Add(categoriaId); // Adiciona a categoria pai
                listaIdCategorias.AddRange(_categoriaProdutoService.ObterCategoriasVinculadas(categoriaId).Result);
            }

            produtoCorTamanhoConferenciaEstoqueListaSelectParameros.ListaCategorias = listaIdCategorias;

            var produtosCorTamanhoAgrupados = _produtoCorTamanhoRepository.ListaSelectConferenciaEstoque(produtoCorTamanhoConferenciaEstoqueListaSelectParameros);

            foreach (var produtoCorTamanhoAgrupado in produtosCorTamanhoAgrupados)
            {
                var produtoCor = produtoCorTamanhoAgrupado.First().ProdutoCor;

                ListaRetorno.Add(new ProdutoCorTamanhoConferenciaEstoqueViewModel
                {
                    Nome = produtoCor.Produto.Nome,
                    Cor = !produtoCor.Cor.PadraoSistema ? produtoCor.Cor.Descricao : "",
                    VolumeUnitario = produtoCor.Produto.UnidadeMedida.VolumeUnitario,
                    ProdutoKit = produtoCor.Produto.TipoProduto.Equals(TipoProduto.PRODUTO_KIT),
                    AdicionarItemAutomaticamente = produtoCorTamanhoAgrupado.FirstOrDefault(p => (!string.IsNullOrEmpty(produtoCorTamanhoConferenciaEstoqueListaSelectParameros.NomeSkuCodigoExternoBarrasGtinEan) &&
                                                    (p.SKU == produtoCorTamanhoConferenciaEstoqueListaSelectParameros.NomeSkuCodigoExternoBarrasGtinEan ||
                                                    p.CodigoBarrasInterno == produtoCorTamanhoConferenciaEstoqueListaSelectParameros.NomeSkuCodigoExternoBarrasGtinEan ||
                                                    p.CodigoGTINEAN == produtoCorTamanhoConferenciaEstoqueListaSelectParameros.NomeSkuCodigoExternoBarrasGtinEan ||
                                                    p.CodigoExterno == produtoCorTamanhoConferenciaEstoqueListaSelectParameros.NomeSkuCodigoExternoBarrasGtinEan))) != null,
                    Tamanhos = produtoCorTamanhoAgrupado.Select(p => new ProdutoCorTamanhoConferenciaEstoqueViewModel.TamanhoCodigoBarrasId
                    {
                        ProdutoCorTamanhoId = p.Id,
                        Tamanho = p.Tamanho.Descricao,
                        PadraoSistema = p.Tamanho.PadraoSistema,
                        CodigoBarrasInterno = p.CodigoBarrasInterno
                    }).ToList()
                });
            }

            return ListaRetorno;
        }

        public IEnumerable<HistoricoProdutoViewModel> ListarSelectHistoricoProduto(string nomeSkuCodigoExternoBarrasGtinEan)
        {
            var produtosAgrupados = _pesquisaProdutoService.ListarSelectProdutoCorAgrupado(nomeSkuCodigoExternoBarrasGtinEan);

            return produtosAgrupados.Select(p =>
            {
                var produto = p.FirstOrDefault();

                return new HistoricoProdutoViewModel
                {
                    ProdutoCorId = produto.ProdutoCorId,
                    Produto = produto.Nome,
                    Cor = produto.ProdutoCor.Cor.PadraoSistema ? "" : produto.ProdutoCor.Cor.Descricao,
                    ProdutoCorTamanhos = p.Select(p => new ListaIdDescricaoViewModel
                    {
                        Id = p.ProdutoCorTamanhoId,
                        Descricao = p.ProdutoCorTamanho.Tamanho.PadraoSistema ? "" : p.ProdutoCorTamanho.Tamanho.Descricao
                    })
                };
            });
        }

        public List<ProdutoCorTamanhoMovimentacaoTransferenciaEstoqueViewModel> ListarSelectMovimentacaoTransferenciaEstoque(string nomeSkuCodigoExternoBarrasGtinEan)
        {
            var listaRetorno = new List<ProdutoCorTamanhoMovimentacaoTransferenciaEstoqueViewModel>();

            var produtosCorTamanhoAgrupados = _produtoCorTamanhoRepository.ListarSelectMovimentacaoTransferenciaEstoque(nomeSkuCodigoExternoBarrasGtinEan);

            foreach (var produtoCorTamanhoAgrupado in produtosCorTamanhoAgrupados)
            {
                var produtoCor = produtoCorTamanhoAgrupado.First().ProdutoCor;

                listaRetorno.Add(new ProdutoCorTamanhoMovimentacaoTransferenciaEstoqueViewModel
                {
                    Nome = produtoCor.Produto.Nome,
                    Cor = !produtoCor.Cor.PadraoSistema ? produtoCor.Cor.Descricao : "",
                    VolumeUnitario = produtoCor.Produto.UnidadeMedida.VolumeUnitario,
                    ProdutoKit = produtoCor.Produto.TipoProduto.Equals(TipoProduto.PRODUTO_KIT),
                    Tamanhos = produtoCorTamanhoAgrupado
                        .Select(p => new TamanhoMovimentacaoTransferenciaEstoqueViewModel
                        {
                            ProdutoCorTamanhoId = p.Id,
                            Tamanho = p.Tamanho.Descricao,
                            PadraoSistema = p.Tamanho.PadraoSistema
                        })
                        .ToList()
                });
            }

            return listaRetorno;
        }

        public async Task<Data.Helpers.GridPaginadaRetorno<ProdutoCorTamanhoListarSelectEntradaMercadoriaViewModel>> ListarSelectEntradaMercadoria(
            GridPaginadaConsulta gridPaginadaConsulta,
            string nomeSkuCodigoExternoBarrasGtinEan)
        {
            var listaRetorno = new List<ProdutoCorTamanhoListarSelectEntradaMercadoriaViewModel>();

            var (produtoFiltrados, total) = await _pesquisaProdutoService.ListarSelectIdProduto(nomeSkuCodigoExternoBarrasGtinEan, gridPaginadaConsulta);

            var produtos = await _produtoRepository
                .FindAllSelectAsNoTracking(
                    p => produtoFiltrados.Contains(p.Id) && p.TipoProduto != TipoProduto.PRODUTO_KIT,
                    p => new Produto
                    {
                        Id = p.Id,
                        Nome = p.Nome,
                        Referencia = p.Referencia,
                        TipoProduto = p.TipoProduto,
                        UnidadeMedida = new UnidadeMedida { VolumeUnitario = p.UnidadeMedida.VolumeUnitario },
                        ProdutoPrecoLojas = p.ProdutoPrecoLojas.Where(pcl => pcl.LojaId.Equals(_aspNetUserInfo.LojaId.Value)).Select(pcl => new ProdutoPrecoLoja
                        {
                            PrecoCompra = pcl.PrecoCompra
                        }).ToList(),
                        ProdutoCores = p.ProdutoCores.Select(pc => new ProdutoCor
                        {
                            ProdutoCorTamanhos = pc.ProdutoCorTamanhos.Select(pct => new ProdutoCorTamanho
                            {
                                CodigoGTINEAN = pct.CodigoGTINEAN,
                            }).ToList()
                        }).ToList()
                    });

            var listaViewModel = produtos
                .Select(p => new ProdutoCorTamanhoListarSelectEntradaMercadoriaViewModel
                {
                    Id = p.Id,
                    Nome = p.Nome,
                    Referencia = p.Referencia,
                    TipoProduto = p.TipoProduto,
                    VolumeUnitario = p.UnidadeMedida.VolumeUnitario,
                    PrecoCompra = p.ProdutoPrecoLojas?.FirstOrDefault()?.PrecoCompra ?? 0m,
                    CodigoGTINEAN = ObterCodigoBarrasParaProdutoSimples(p)
                })
                .AsQueryable();

            listaRetorno = !string.IsNullOrWhiteSpace(gridPaginadaConsulta.CampoOrdenacao)
                ? listaViewModel.OrderBy($"{gridPaginadaConsulta.CampoOrdenacao} {gridPaginadaConsulta.DirecaoOrdenacao}").ToList()
                : listaViewModel.ToList();

            return new GridPaginadaRetorno<ProdutoCorTamanhoListarSelectEntradaMercadoriaViewModel>
            {
                Registros = listaRetorno,
                Total = total
            };
        }

        private static string ObterCodigoBarrasParaProdutoSimples(Produto p)
        {
            if (p.TipoProduto != TipoProduto.PRODUTO_SIMPLES)
            {
                return null;
            }

            return p.ProdutoCores.FirstOrDefault().ProdutoCorTamanhos.FirstOrDefault().CodigoGTINEAN;
        }

        public ProdutoCorTamanhoListarSelectEntradaMercadoriaViewModel ObterOpcaoSelectEntradaMercadoria(string nomeSkuCodigoExternoBarrasGtinEan)
        {
            var (produtoFiltrados, _) = _pesquisaProdutoService.ListarSelectIdProduto(nomeSkuCodigoExternoBarrasGtinEan).Result;

            var produto = _produtoRepository
                .FirstOrDefaultAsNoTracking(
                    p => produtoFiltrados.Contains(p.Id),
                    p => new Produto
                    {
                        Id = p.Id,
                        Nome = p.Nome,
                        Referencia = p.Referencia,
                        TipoProduto = p.TipoProduto,
                        UnidadeMedida = new UnidadeMedida { VolumeUnitario = p.UnidadeMedida.VolumeUnitario }
                    })
                .Result;

            if (produto == null)
            {
                return null;
            }

            return new ProdutoCorTamanhoListarSelectEntradaMercadoriaViewModel
            {
                Id = produto.Id,
                Nome = produto.Nome,
                VolumeUnitario = produto.UnidadeMedida.VolumeUnitario,
                Referencia = produto.Referencia,
                TipoProduto = produto.TipoProduto,
            };
        }

        public ProdutoCorTamanhoListarSelectEntradaMercadoriaViewModel ObterOpcaoSelectEntradaMercadoriaPorId(Guid produtoId)
        {
            var produto = _produtoRepository
                .FirstOrDefaultAsNoTracking(
                    p => p.Id == produtoId,
                    p => new Produto
                    {
                        Id = p.Id,
                        Nome = p.Nome,
                        Referencia = p.Referencia,
                        TipoProduto = p.TipoProduto,
                        UnidadeMedida = new UnidadeMedida { VolumeUnitario = p.UnidadeMedida.VolumeUnitario }
                    })
                .Result;

            if (produto == null)
            {
                return null;
            }

            return new ProdutoCorTamanhoListarSelectEntradaMercadoriaViewModel
            {
                Id = produto.Id,
                Nome = produto.Nome,
                VolumeUnitario = produto.UnidadeMedida.VolumeUnitario,
                Referencia = produto.Referencia,
                TipoProduto = produto.TipoProduto,
            };
        }

        public async Task<bool> ProdutoFoiVendido(List<ProdutoCorTamanho> produtoCorTamanhoId)
        {
            return await _produtoCorTamanhoRepository.ProdutoFoiVendido(produtoCorTamanhoId.Select(pct => pct.Id).ToList());
        }

        public async Task<List<ProdutoCorTamanhoTrocaViewModel>> ListarSelectIdNome(string nomeSkuCodigoExternoBarrasGtinEan)
        {
            var produtos = await _produtoCorTamanhoRepository.ListarSelectIdNome(nomeSkuCodigoExternoBarrasGtinEan);

            return produtos.Select(p => new ProdutoCorTamanhoTrocaViewModel
            {
                ProdutoCorTamanhoId = p.Id,
                Nome = FormatarDescricaoCompletaProduto(p.Id).Result,
            }).ToList();
        }

        public async Task AtualizarCustoMedio(Guid produtoCorTamanhoId, decimal quantidade, decimal custoTotal, decimal saldoProduto)
        {
            // Encontrar preco custo e saldo em estoque
            var produto = await _produtoCorTamanhoRepository.ObterParaAtualizarCusto(produtoCorTamanhoId, _aspNetUserInfo.LojaId.Value);

            var produtoPrecos = produto.ProdutoCor.Produto.ProdutoPrecoLojas.First();

            saldoProduto = saldoProduto < 0 ? 0 : saldoProduto;
            var novoSaldo = saldoProduto + quantidade;

            // Cálculo do custo médio
            var custoMedio = (((produtoPrecos.PrecoCusto * saldoProduto) + (custoTotal * quantidade)) / (novoSaldo == 0 ? 1 : novoSaldo));

            // Atualizo preço de custo e markup
            produto.ProdutoCor.Produto.ProdutoPrecoLojas.First().PrecoCusto = custoMedio;
            produto.ProdutoCor.Produto.ProdutoPrecoLojas.First().Markup = produtoPrecos.PrecoCusto == 0
                                                                          ? 0
                                                                          : Math.Round(((produtoPrecos.PrecoVenda - produtoPrecos.PrecoCusto) / produtoPrecos.PrecoCusto) * 100, 4);

            await _produtoCorTamanhoRepository.SaveChanges();
        }

        public async Task<List<ProdutoCorTamanho>> ObterVariacoesParaExportarNaTabelaPreco()
        {
            return await _produtoCorTamanhoRepository.ObterVariacoesParaExportarNaTabelaPreco(_aspNetUserInfo.LojaId.Value);
        }

        public async Task<Data.Helpers.GridPaginadaRetorno<ProdutoCorTamanhoEtiquetaViewModel>> ObterPorData(
            GridPaginadaConsulta gridPaginadaConsulta,
            DateTime dataInicio,
            DateTime dataFim,
            TipoData tipoData,
            Guid lojaId)
        {
            var query = _produtoCorTamanhoRepository.ObterPorData(dataInicio, dataFim, tipoData, lojaId);

            var produtoCorTamanhoPaginado = query.Skip(gridPaginadaConsulta.Skip)
                                                 .Take(gridPaginadaConsulta.TamanhoPagina);

            var tabelaPreco = await _tabelaPrecoRepository.FirstAsNoTracking(t => t.PadraoSistema);

            var registros = await produtoCorTamanhoPaginado.Select(p => new ProdutoCorTamanhoEtiquetaViewModel
            {
                Id = p.Id,
                Nome = p.ProdutoCor.Produto.Nome + " | " + p.ProdutoCor.Cor.Descricao,
                TamanhoId = p.Tamanho.Id,
                PadraoSistema = p.Tamanho.PadraoSistema,
                DescricaoTamanho = p.Tamanho.Descricao,
                Saldo = p.ProdutoCorTamanhoEstoques.Sum(e => e.EstoqueAtual),

            }).ToListAsync();

            foreach (var p in registros)
            {
                p.PrecoVenda = await _calculoPrecoProdutoV2Service.CalcularPreco(tabelaPreco.Id, p.Id, _aspNetUserInfo.LojaId.Value);
            }

            return new GridPaginadaRetorno<ProdutoCorTamanhoEtiquetaViewModel>
            {
                Registros = registros,
                Total = query.Count()
            };
        }

        public async Task<List<ListaProdutoCorTamanhoPrecoViewModel>> ObterProdutoCorTamanhoDescricaoPorProdutoId(Guid produtoId)
        {
            var lista = new List<ListaProdutoCorTamanhoPrecoViewModel>();

            var listaProdutoCorTamanho = await _produtoCorTamanhoRepository.ObterProdutoCorTamanhoPorProdutoId(produtoId, _aspNetUserInfo.LojaId.Value);

            if (listaProdutoCorTamanho != null)
            {
                lista = listaProdutoCorTamanho.Select(item => new ListaProdutoCorTamanhoPrecoViewModel
                {
                    Id = item.Id,
                    ProdutoId = item.ProdutoCor.Produto.Id,
                    Nome = item.ProdutoCor.Produto.Nome,
                    Cor = item.ProdutoCor.Cor.PadraoSistema ? string.Empty : item.ProdutoCor.Cor.Descricao,
                    Tamanho = item.Tamanho.PadraoSistema ? string.Empty : item.Tamanho.Descricao,
                    PrecoCusto = item.ProdutoCor.Produto.ProdutoPrecoLojas.FirstOrDefault()?.PrecoCusto ?? 0,
                    PrecoVenda = item.ProdutoCor.Produto.ProdutoPrecoLojas.FirstOrDefault()?.PrecoVenda ?? 0
                })
                .ToList();
            }

            return lista;
        }

        public async Task<ProdutoCorTamanho> ObterComPredicate(Expression<Func<ProdutoCorTamanho, bool>> predicate = null)
        {
            try
            {
                return await _produtoCorTamanhoRepository.FirstOrDefaultAsNoTracking(predicate);
            }
            catch (Exception ex)
            {
                return null;
            }
        }

        public async Task<List<ProdutoCorTamanhoEstoque>> ObterProdutosIdQuantidadePorEstoqueId(Guid localEstoqueId)
        {
            return await _produtoCorTamanhoEstoqueRepository.ObterProdutosIdQuantidadePorEstoqueId(localEstoqueId);
        }

        public async Task<Data.Helpers.GridPaginadaRetorno<ProdutoCorTamanhoEtiquetaViewModel>> ListarSelectEtiqueta(
            GridPaginadaConsulta gridPaginadaConsulta,
            string nomeSkuCodigoExternoBarrasGtinEan)
        {
            var query = _produtoCorTamanhoRepository.ObterEtiquetaPesquisaAvancada(nomeSkuCodigoExternoBarrasGtinEan, _aspNetUserInfo.LojaId.Value);

            var tabelaPreco = await _tabelaPrecoRepository.FirstAsNoTracking(t => t.PadraoSistema);

            var produtoCorTamanhoPaginado = query.Skip(gridPaginadaConsulta.Skip)
                                                 .Take(gridPaginadaConsulta.TamanhoPagina);

            var registros = await produtoCorTamanhoPaginado.Select(p => new ProdutoCorTamanhoEtiquetaViewModel
            {
                Id = p.Id,
                Nome = p.FormatarDescricaoCompleta(),
                TamanhoId = p.Tamanho.Id,
                PadraoSistema = p.Tamanho.PadraoSistema,
                DescricaoTamanho = p.Tamanho.Descricao,
                Saldo = p.ProdutoCorTamanhoEstoques.Sum(e => e.EstoqueAtual),
            }).ToListAsync();

            foreach (var p in registros)
            {
                p.PrecoVenda = await _calculoPrecoProdutoV2Service.CalcularPreco(tabelaPreco.Id, p.Id, _aspNetUserInfo.LojaId.Value);
            }

            return new GridPaginadaRetorno<ProdutoCorTamanhoEtiquetaViewModel>
            {
                Registros = registros,
                Total = query.Count()
            };
        }
    }
}