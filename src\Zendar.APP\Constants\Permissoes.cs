﻿namespace Zendar.APP.Constants
{
    public static class Permissoes
    {
        #region [Vendedor]
        public const string VendedorCadastrar = "vendedor_cadastrar";
        public const string VendedorListar = "vendedor_listar";
        public const string VendedorAlterar = "vendedor_alterar";
        public const string VendedorExcluir = "vendedor_excluir";
        public const string VendedorVisualizar = "vendedor_visualizar";
        #endregion

        #region [Categoria Cliente]
        public const string CategoriaClienteCadastrar = "categoriacliente_cadastrar";
        public const string CategoriaClienteListar = "categoriacliente_listar";
        public const string CategoriaClienteAlterar = "categoriacliente_alterar";
        public const string CategoriaClienteExcluir = "categoriacliente_excluir";
        public const string CategoriaClienteVisualizar = "categoriacliente_visualizar";
        #endregion

        #region [Transportadora]
        public const string TransportadoraCadastrar = "transportadora_cadastrar";
        public const string TransportadoraListar = "transportadora_listar";
        public const string TransportadoraAlterar = "transportadora_alterar";
        public const string TransportadoraExcluir = "transportadora_excluir";
        public const string TransportadoraVisualizar = "transportadora_visualizar";
        #endregion

        #region [Produto]
        public const string ProdutoCadastrar = "produto_cadastrar";
        public const string ProdutoCadastrarTag = "produto_cadastrar_tag";
        public const string ProdutoListar = "produto_listar";
        public const string ProdutoAlterar = "produto_alterar";
        public const string ProdutoAlterarEmMassa = "produto_alterar_em_massa";
        public const string ProdutoExcluir = "produto_excluir";
        public const string ProdutoVisualizar = "produto_visualizar";
        #endregion

        #region [Cor]
        public const string CorCadastrar = "cor_cadastrar";
        public const string CorListar = "cor_listar";
        public const string CorAlterar = "cor_alterar";
        public const string CorExcluir = "cor_excluir";
        public const string CorVisualizar = "cor_visualizar";
        #endregion

        #region [Local Estoque]
        public const string LocalEstoqueCadastrar = "localestoque_cadastrar";
        public const string LocalEstoqueListar = "localestoque_listar";
        public const string LocalEstoqueAlterar = "localestoque_alterar";
        public const string LocalEstoqueExcluir = "localestoque_excluir";
        public const string LocalEstoqueVisualizar = "localestoque_visualizar";
        #endregion

        #region [Loja]
        public const string LojaCadastrar = "loja_cadastrar";
        public const string LojaListar = "loja_listar";
        public const string LojaAlterar = "loja_alterar";
        public const string LojaExcluir = "loja_excluir";
        public const string LojaVisualizar = "loja_visualizar";
        #endregion

        #region [Tamanho]
        public const string TamanhoCadastrar = "tamanho_cadastrar";
        public const string TamanhoListar = "tamanho_listar";
        public const string TamanhoAlterar = "tamanho_alterar";
        public const string TamanhoExcluir = "tamanho_excluir";
        public const string TamanhoVisualizar = "tamanho_visualizar";
        #endregion

        #region [Categoria Produto]
        public const string CategoriaProdutoCadastrar = "categoriaproduto_cadastrar";
        public const string CategoriaProdutoListar = "categoriaproduto_listar";
        public const string CategoriaProdutoAlterar = "categoriaproduto_alterar";
        public const string CategoriaProdutoExcluir = "categoriaproduto_excluir";
        public const string CategoriaProdutoVisualizar = "categoriaproduto_visualizar";
        #endregion

        #region [Unidade Medida]
        public const string UnidadeMedidaCadastrar = "unidademedida_cadastrar";
        public const string UnidadeMedidaListar = "unidademedida_listar";
        public const string UnidadeMedidaAlterar = "unidademedida_alterar";
        public const string UnidadeMedidaExcluir = "unidademedida_excluir";
        public const string UnidadeMedidaVisualizar = "unidademedida_visualizar";
        #endregion

        #region [Marca]
        public const string MarcaCadastrar = "marca_cadastrar";
        public const string MarcaListar = "marca_listar";
        public const string MarcaAlterar = "marca_alterar";
        public const string MarcaExcluir = "marca_excluir";
        public const string MarcaVisualizar = "marca_visualizar";
        #endregion

        #region [Historico Acao]
        public const string HistoricoAcaoListar = "historicoacao_listar";
        public const string HistoricoAcaoVisualizar = "historicoacao_visualizar";
        #endregion

        #region [Cliente]
        public const string ClienteCadastrar = "cliente_cadastrar";
        public const string ClienteListar = "cliente_listar";
        public const string ClienteAlterar = "cliente_alterar";
        public const string ClienteExcluir = "cliente_excluir";
        public const string ClienteVisualizar = "cliente_visualizar";
        #endregion

        #region [Fornecedor]
        public const string FornecedorCadastrar = "fornecedor_cadastrar";
        public const string FornecedorListar = "fornecedor_listar";
        public const string FornecedorAlterar = "fornecedor_alterar";
        public const string FornecedorExcluir = "fornecedor_excluir";
        public const string FornecedorVisualizar = "fornecedor_visualizar";
        #endregion

        #region [Conta Financeira]
        public const string ContaFinanceiraCadastrar = "contafinanceira_cadastrar";
        public const string ContaFinanceiraListar = "contafinanceira_listar";
        public const string ContaFinanceiraAlterar = "contafinanceira_alterar";
        public const string ContaFinanceiraExcluir = "contafinanceira_excluir";
        public const string ContaFinanceiraVisualizar = "contafinanceira_visualizar";
        #endregion

        #region [Importacao Cadastro]
        public const string ImportacaoClienteListar = "importacaocliente_listar";
        public const string ImportacaoClienteImportar = "importacaocliente_importar";

        public const string ImportacaoFornecedorListar = "importacaofornecedor_listar";
        public const string ImportacaoFornecedorImportar = "importacaofornecedor_importar";

        public const string ImportacaoProdutoListar = "importacaoproduto_listar";
        public const string ImportacaoProdutoImportar = "importacaoproduto_importar";
        #endregion

        #region [Plano Conta]
        public const string PlanoContaCadastrar = "planoconta_cadastrar";
        public const string PlanoContaListar = "planoconta_listar";
        public const string PlanoContaAlterar = "planoconta_alterar";
        public const string PlanoContaExcluir = "planoconta_excluir";
        #endregion

        #region [Forma Pagamento]
        public const string FormaPagamentoCadastrar = "formapagamento_cadastrar";
        public const string FormaPagamentoListar = "formapagamento_listar";
        public const string FormaPagamentoAlterar = "formapagamento_alterar";
        public const string FormaPagamentoExcluir = "formapagamento_excluir";
        public const string FormaPagamentoVisualizar = "formapagamento_visualizar";
        #endregion

        #region [Forma Recebimento]
        public const string FormaRecebimentoCadastrar = "formarecebimento_cadastrar";
        public const string FormaRecebimentoListar = "formarecebimento_listar";
        public const string FormaRecebimentoAlterar = "formarecebimento_alterar";
        public const string FormaRecebimentoExcluir = "formarecebimento_excluir";
        public const string FormaRecebimentoVisualizar = "formarecebimento_visualizar";
        #endregion

        #region [Perfil Usuario]
        public const string PerfilUsuarioCadastrar = "perfilusuario_cadastrar";
        public const string PerfilUsuarioListar = "perfilusuario_listar";
        public const string PerfilUsuarioAlterar = "perfilusuario_alterar";
        public const string PerfilUsuarioExcluir = "perfilusuario_excluir";
        public const string PerfilUsuarioVisualizar = "perfilusuario_visualizar";
        #endregion

        #region [Usuario]
        public const string UsuarioCadastrar = "usuario_cadastrar";
        public const string UsuarioListar = "usuario_listar";
        public const string UsuarioAlterar = "usuario_alterar";
        public const string UsuarioExcluir = "usuario_excluir";
        public const string UsuarioVisualizar = "usuario_visualizar";
        #endregion

        #region [Campo Personalizado]
        public const string CampoPersonalizadoCadastrar = "campopersonalizado_cadastrar";
        public const string CampoPersonalizadoListar = "campopersonalizado_listar";
        public const string CampoPersonalizadoAlterar = "campopersonalizado_alterar";
        public const string CampoPersonalizadoExcluir = "campopersonalizado_excluir";
        public const string CampoPersonalizadoVisualizar = "campopersonalizado_visualizar";
        #endregion

        #region [PDV]
        public const string PdvAlterar = "pdv_alterar";
        public const string PdvAlterarTabelaPreco = "pdv_alterar_tabela_preco";
        public const string PdvCancelar = "pdv_cancelar";
        public const string PdvAlterarItem = "pdv_alterar_item";
        public const string PdvCancelarItem = "pdv_remover_item";
        public const string PdvSuprimento = "pdv_suprimento";
        public const string PdvSangria = "pdv_sangria";
        public const string PdvRemoverAcrescimo = "pdv_remover_acrescimo";
        public const string PdvLancarTrocaDevolucao = "pdv_lancar_troca_devolucao";
        public const string PdvAlterarLocalEstoque = "pdv_alterar_local_estoque";
        public const string LancarConsignacao = "pdv_lancar_consignacao";
        public const string BaixarConsignacao = "pdv_baixar_consignacao";
        public const string ConcederAcrescimosDescontos = "pdv_conceder_acrescimos_descontos";
        #endregion

        #region [Fiscal]
        public const string NotaFiscalEmitir = "notafiscal_emitir";
        public const string DocumentoFiscalExportacaoListar = "documentofiscalexportacao_listar";
        public const string ManifestoDestinatarioVisualizar = "manifestodestinatario_visualizar";
        #endregion

        #region [Dashboard Inicial]
        public const string DashboardVisualizar = "dashboard_visualizar";
        #endregion

        #region [Regra Fiscal]
        public const string RegraFiscalCadastrar = "regrafiscal_cadastrar";
        public const string RegraFiscalListar = "regrafiscal_listar";
        public const string RegraFiscalAlterar = "regrafiscal_alterar";
        public const string RegraFiscalExcluir = "regrafiscal_excluir";
        public const string RegraFiscalVisualizar = "regrafiscal_visualizar";
        #endregion

        #region [Conferencia Estoque]
        public const string ConferenciaEstoqueIniciar = "conferenciaestoque_iniciar";
        public const string ConferenciaEstoqueDescartar = "conferenciaestoque_descartar";
        public const string ConferenciaEstoqueContinuar = "conferenciaestoque_continuar";
        public const string ConferenciaEstoqueAplicarCorrecao = "conferenciaestoque_aplicarcorrecao";
        #endregion

        #region [Movimentacao Estoque]
        public const string MovimentacaoEstoqueCadastrar = "movimentacaoestoque_cadastrar";
        public const string MovimentacaoEstoqueListar = "movimentacaoestoque_listar";
        public const string MovimentacaoEstoqueAlterar = "movimentacaoestoque_alterar";
        public const string MovimentacaoEstoqueDuplicar = "movimentacaoestoque_duplicar";
        public const string MovimentacaoEstoqueVisualizar = "movimentacaoestoque_visualizar";
        public const string MovimentacaoEstoqueCancelar = "movimentacaoestoque_cancelar";
        #endregion

        #region [Transferencia Estoque]
        public const string TransferenciaEstoqueListar = "transferenciaestoque_listar";
        public const string TransferenciaEstoqueVisualizar = "transferenciaestoque_visualizar";
        public const string TransferenciaEstoqueAlterar = "transferenciaestoque_alterar";
        public const string TransferenciaEstoqueRejeitar = "transferenciaestoque_rejeitar";
        public const string TransferenciaEstoqueDuplicar = "transferenciaestoque_duplicar";
        public const string TransferenciaEstoqueConfirmar = "transferenciaestoque_confirmar";
        public const string TransferenciaEstoqueCancelar = "transferenciaestoque_cancelar";
        public const string TransferenciaEstoqueTransferirEntreLojas = "transferenciaestoque_transferir_entre_lojas";
        public const string TransferenciaEstoqueTransferirEntreEstoques = "transferenciaestoque_transferir_entre_estoques";
        #endregion

        #region [Historico Produto]
        public const string HistoricoProdutoListar = "historicoproduto_listar";
        public const string HistoricoProdutoVisualizar = "historicoproduto_visualizar";
        #endregion

        #region [Tabela Preco]
        public const string TabelaPrecoCadastrar = "tabelapreco_cadastrar";
        public const string TabelaPrecoListar = "tabelapreco_listar";
        public const string TabelaPrecoAlterar = "tabelapreco_alterar";
        public const string TabelaPrecoExcluir = "tabelapreco_excluir";
        public const string TabelaPrecoVisualizar = "tabelapreco_visualizar";
        #endregion

        #region [Entrada Mercadoria]
        public const string EntradaMercadoriaCadastrar = "entradamercadoria_cadastrar";
        public const string EntradaMercadoriaListar = "entradamercadoria_listar";
        public const string EntradaMercadoriaAlterar = "entradamercadoria_alterar";
        public const string EntradaMercadoriaDuplicar = "entradamercadoria_duplicar";
        public const string EntradaMercadoriaVisualizar = "entradamercadoria_visualizar";
        public const string EntradaMercadoriaCancelar = "entradamercadoria_cancelar";
        public const string EntradaMercadoriaExcluir = "entradamercadoria_excluir";
        public const string EntradaMercadoriaLancarEstoque = "entradamercadoria_lancarestoque";
        public const string EntradaMercadoriaEstornarEstoque = "entradamercadoria_estornarestoque";
        public const string EntradaMercadoriaLancarFinanceiro = "entradamercadoria_lancarfinanceiro";
        public const string EntradaMercadoriaEstornarFinanceiro = "entradamercadoria_estornarfinanceiro";
        #endregion

        #region [Controle Caixa]
        public const string ControleCaixaListar = "controlecaixa_listar";
        public const string ControleCaixaVisualizar = "controlecaixa_visualizar";
        public const string ControleCaixaAbrirFechar = "controlecaixa_abrirfechar";
        public const string ControleCaixaReabrir = "controlecaixa_reabrir";
        public const string ControleCaixaVerTodosCaixa = "controlecaixa_vertodoscaixas";
        public const string ControleCaixaImprimirTodosCaixa = "controlecaixa_imprimirtodoscaixas";
        public const string ControleCaixaAlterarSaldoAbertura = "controlecaixa_alterarsaldoabertura";
        #endregion

        #region [Extrato]
        public const string ExtratoListar = "extrato_listar";
        public const string ExtratoTransferencia = "extrato_transferencia";
        #endregion

        #region [Fatura]
        public const string FaturaListar = "fatura_listar";
        public const string FaturaVisualizar = "fatura_visualizar";
        #endregion

        #region [Lancamento Financeiro]
        public const string LancamentoFinanceiroCadastrar = "lancamento_cadastrar";
        public const string LancamentoFinanceiroAlterar = "lancamento_alterar";
        #endregion

        #region [Conta Pagar]
        public const string ContasPagarCadastrar = "contaspagar_cadastrar";
        public const string ContasPagarListar = "contaspagar_listar";
        public const string ContasPagarAlterar = "contaspagar_alterar";
        public const string ContasPagarExcluir = "contaspagar_excluir";
        public const string ContasPagarBaixa = "contaspagar_baixa";
        public const string ContasPagarCancelarBaixa = "contaspagar_cancelarbaixa";
        #endregion

        #region [Conta Receber]
        public const string ContasReceberCadastrar = "contasreceber_cadastrar";
        public const string ContasReceberListar = "contasreceber_listar";
        public const string ContasReceberAlterar = "contasreceber_alterar";
        public const string ContasReceberExcluir = "contasreceber_excluir";
        public const string ContasReceberBaixa = "contasreceber_baixa";
        public const string ContasReceberCancelarBaixa = "contasreceber_cancelarbaixa";
        public const string ContasReceberZerarMultaJuros = "contasreceber_zerarmultajuros";
        #endregion

        #region [Recebimento Conta]
        public const string RecebimentoContaListar = "recebimentoconta_listar";
        public const string RecebimentoContaBaixarConta = "recebimentoconta_baixarconta";
        public const string RecebimentoContaZerarMultaJuros = "recebimentoconta_zerarmultajuros";
        #endregion

        #region [Etiqueta Personalizada]
        public const string PersonalizarEtiquetaCadastrar = "personalizaretiqueta_cadastrar";
        public const string PersonalizarEtiquetaListar = "personalizaretiqueta_listar";
        public const string PersonalizarEtiquetaAlterar = "personalizaretiqueta_alterar";
        public const string PersonalizarEtiquetaExcluir = "personalizaretiqueta_excluir";
        public const string PersonalizarEtiquetaVisualizar = "personalizaretiqueta_visualizar";
        #endregion

        #region [Relatorio Personalizado Produto]
        public const string RelatorioPersonalizadoProdutoCadastrar = "relatoriopersonalizadoproduto_cadastrar";
        public const string RelatorioPersonalizadoProdutoListar = "relatoriopersonalizadoproduto_listar";
        public const string RelatorioPersonalizadoProdutoAlterar = "relatoriopersonalizadoproduto_alterar";
        public const string RelatorioPersonalizadoProdutoExcluir = "relatoriopersonalizadoproduto_excluir";
        #endregion

        #region [Relatorio Personalizado Cliente]
        public const string RelatorioPersonalizadoClienteCadastrar = "relatoriopersonalizadocliente_cadastrar";
        public const string RelatorioPersonalizadoClienteListar = "relatoriopersonalizadocliente_listar";
        public const string RelatorioPersonalizadoClienteAlterar = "relatoriopersonalizadocliente_alterar";
        public const string RelatorioPersonalizadoClienteExcluir = "relatoriopersonalizadocliente_excluir";
        #endregion

        #region [Relatorio Produto]
        public const string RelatorioProdutosPorVenda = "relatorio_produto_por_venda";
        public const string RelatorioLucroPorProduto = "relatorio_lucro_por_produto";
        public const string RelatorioClienteAgrupado = "relatorio_produtos_por_clientes";
        public const string RelatorioNumeroContaAgrupado = "relatorio_produtos_agrupados_frente_caixa";
        public const string RelatorioProdutosComPreco = "relatorio_produtoscompreco";
        public const string RelatorioProdutoPersonalizado = "relatorio_produtopersonalizado";
        public const string RelatorioInventario = "relatorio_inventario";
        public const string RelatorioEstoqueSemPreco = "relatorio_estoque_sem_preco";
        public const string RelatorioEstoqueComPrecoVenda = "relatorio_estoque_preco_venda";
        public const string RelatorioEstoqueComPrecoCusto = "relatorio_estoque_preco_custo";
        public const string RelatorioCatalogoProdutos = "relatorio_catalogoproduto";
        public const string RelatorioProdutosPorGrupos = "relatorio_produtos_por_grupos";
        public const string RelatorioProdutosAgrupadoPorDia = "relatorio_produtos_agrupado_dia";
        public const string RelatorioItensMaisVendidos = "relatorio_itens_mais_vendidos";
        #endregion

        #region [Relatorio Compras]
        public const string RelatorioProdutoCompra = "relatorio_produto_por_compra";
        public const string RelatorioProdutoCompraGrade = "relatorio_compra_por_produto_grade";
        public const string RelatorioProdutoCompraDetalhado = "relatorio_produto_por_compra_detalhado";
        #endregion

        #region [Relatorio Cliente]
        public const string RelatorioCadastroCompletoCliente = "relatorio_cadastrocompletocliente";
        public const string RelatorioPersonalizadoCliente = "relatorio_clientepersonalizado";
        public const string RelatorioInformacoesClientes = "relatorio_informacoesclientes";
        #endregion

        #region [Relatorio Venda]
        public const string RelatorioVendasPorFormaRecebimento = "relatorio_vendas_por_forma_recebimento";
        public const string RelatorioVendasPorDia = "relatorio_vendas_por_dia";
        public const string RelatorioVendasSimplificadas = "relatorio_venda_simplificadas";
        public const string RelatorioCurvaAbc = "relatorio_curvaabc";
        public const string RelatorioVendasTotalizadasPorVendedores = "relatorio_vendas_totalizadas_vendedores";
        public const string RelatorioLucroAgrupadoPorDia = "relatorio_lucro_agrupado_dia";
        public const string RelatorioVendasTotalizadasPorVendedoresGrafico = "relatorio_vendas_totalizadas_vendedores_grafico";
        public const string RelatorioVendasTotalizadasPorVendedoresGraficoFrenteCaixa = "relatorio_vendas_totalizadas_vendedores_grafico_frente_caixa";
        public const string RelatorioVendasTotalizadasPorProdutos = "relatorio_vendas_totalizadas_produtos";
        public const string RelatorioVendasTotalizadasPorProdutosSimples = "relatorio_vendas_totalizadas_por_produto_simples";
        public const string RelatorioVendasPorEntregador = "relatorio_vendas_detalhamento_entregador";
        public const string RelatorioVendasValoresAdicionais = "relatorio_vendas_valores_adicionais";
        public const string RelatorioVendasTotalizadasPorEntregadores = "relatorio_vendas_totalizadas_entregadores";

        #endregion

        #region [Relatorio Operacao]
        public const string RelatorioConsignacao = "relatorio_consignacao";
        public const string RelatorioItensRemovidos = "relatorio_itens_removidos_frente_caixa";
        #endregion

        #region [Configuracao]
        public const string MultaJurosAlterar = "multajuros_alterar";
        public const string PadronizacaoAlterar = "padronizacao_alterar";
        #endregion

        #region [Troca Devolucao]
        public const string ListarTrocaDevolucao = "listar_devolucao";
        public const string VisualizarTrocaDevolucao = "visualizar_devolucao";
        public const string ListarVoucher = "voucher_listar";
        #endregion

        #region [Meta Comissao]
        public const string MetaComissaoConfigurar = "metacomissao_configurar";
        #endregion

        #region [Smart POS]
        public const string SmartPOSHabilitarDispositivo = "caixamovel_habilitar_dispositivo";
        public const string SmartPOSExcluirConfiguracao = "caixamovel_excluir_configuracao";
        #endregion

        public const string TrayHabilitar = "tray_habilitar_integracao";

        #region [Módulo Dashboard]
        public const string ModuloDashboardVisualizar = "dashboard_visualizar";
        #endregion

        #region [Promocao]
        public const string PromocaoConfigurar = "promocao_configurar";
        #endregion

        #region [Frente Caixa]
        public const string FrenteCaixaConfigurar = "frentecaixa_configurar";
        public const string FrenteCaixaExcluir = "frentecaixa_excluir";
        #endregion

        #region [PDV Offline]
        public const string PdvOfflineConfigurar = "pdvoffline_configurar";
        public const string PdvOfflineExcluir = "pdvoffline_excluir";
		#endregion

		#region [Fomer Delivery]
		public const string FomerDeliveryConfigurar = "fomerdelivery_configurar";
		public const string FomerDeliveryExcluir = "fomerdelivery_excluir";
		#endregion

		#region [Credenciadora Cartao]
		public const string CredenciadoraCartaoCadastrar = "credenciadora_cartao_cadastrar";
        public const string CredenciadoraCartaoListar = "credenciadora_cartao_listar";
        public const string CredenciadoraCartaoAlterar = "credenciadora_cartao_alterar";
        public const string CredenciadoraCartaoExcluir = "credenciadora_cartao_excluir";
        public const string CredenciadoraCartaoVisualizar = "credenciadora_cartao_visualizar";
        #endregion
    }
}
