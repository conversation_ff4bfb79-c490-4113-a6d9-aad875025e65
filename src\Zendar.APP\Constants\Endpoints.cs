﻿namespace Zendar.APP.Constants
{
    public static class Endpoints
    {
        #region Comum
        public const string Cadastrar = "cadastrar";
        public const string CadastrarExterno = "cadastrar-externo";
        public const string Alterar = "alterar";
        public const string AlterarStone = "alterar-stone";
        public const string AlterarPdv = "alterar-pdv";
        public const string Excluir = "excluir";
        public const string Inativar = "inativar";
        public const string Ativar = "ativar";
        public const string Cancelar = "cancelar";
        public const string Duplicar = "duplicar/{id}";
        public const string Listar = "listar";
        public const string ListarSelect = "listar-select";
        public const string ListarSelectNotaFiscal = "listar-select-nota-fiscal";
        public const string ListarSelectPdv = "listar-select-pdv";
        public const string ListarSelectCodigo = "listar-select-codigo";
        public const string ListarSelectIdNome = "listar-select-id-nome";
        public const string ListarPaginado = "listar-paginado";
        public const string ListarDetalhado = "listar-detalhado";
        public const string ListarCache = "listar-cache";
        public const string ListarPorNome = "listar-por-nome";
        public const string ListarTodosAtivos = "listar-todos-ativos";
        public const string ListarPorLoja = "listar-por-loja";
        public const string ListarPorProduto = "listar-por-produto";
        public const string Obter = "obter";
        public const string ObterPorSku = "obter-por-sku";
        public const string ObterPorCodIntegracaoOuReferencia = "obter-por-cod-integracao-referencia";
        public const string ObterPorCodigoGtinEanOuExterno = "obter-por-gtin-ean-externo";
        public const string ObterPorCodigoBarrasInternoOuSequencia = "obter-por-codigo-barras-interno-sequencia";
        public const string Status = "status";
        public const string ObterPdv = "obter-pdv";
        public const string ObterParaImprimir = "obter-para-imprimir";
        public const string ObterParaImprimirCarne = "obter-para-imprimir-carne";
        public const string Transferir = "transferir";
        public const string ObterCache = "obter-cache";
        public const string ObterPadraoSistema = "obter-padrao-sistema";
        public const string Download = "download";
        public const string RetornoAssinaturaA3 = "retorno-assinatura-a3";
        public const string RetornoAutorizacaoA3 = "retorno-autorizacao-a3";
        public const string RetornoInutilizacaoA3 = "retorno-inutilizacao-a3";
        public const string RetornoConsultarReciboA3 = "retorno-consultar-recibo-a3";
        public const string RetornoConsultarProtocoloA3 = "retorno-consultar-protocolo-a3";
        public const string Detalhes = "detalhes";
        public const string Fechar = "fechar";
        public const string ImpressaoA4 = "impressao-a4";
        public const string ImpressaoMeiaFolha = "impressao-meia-folha";
        public const string EnviarEmail = "enviar-email";
        public const string RelatorioListagem = "relatorio-listagem";
        public const string EnviarEmailRelatorioListagem = "enviar-email-relatorio-listagem";
        public const string ListarRelatoriosPersonalizados = "listar-relatoriospersonalizados";
        public const string GerarRelatorio = "gerar-relatorio";
        public const string ObterTotalizadoresListaPaginada = "obter-totalizadores-lista-paginada";
        public const string ObterImagem = "obter-imagem/{id}";
        public const string GerarPin = "gerar-pin";
        public const string ObterDadosPin = "obter-dados-pin/{pin}";
        public const string ObterNome = "obter-nome";
        public const string Importar = "importar";
        public const string ImportarComId = "importar/{id}";
        public const string CadastrarProdutoCorTamanho = "cadastrar-produto/{id}";
        public const string Pesquisar = "pesquisar";
        public const string ListarIcones = "listar-icones";
        public const string Exportar = "exportar";
        public const string Finalizar = "finalizar";
        public const string Quantidade = "quantidade";
        #endregion

        #region [CaixaMovel]
        public const string AlterarEnvioLog = "alterar-envio-log";
        #endregion

        #region [ Autenticação ]
        public const string Login = "login";
        public const string RefreshToken = "refresh-token";
        public const string RecuperarSenha = "recuperar-senha/{email}";
        public const string AlterarSenha = "alterar-senha";
        public const string Logoff = "logoff";
        public const string LogoutUser = "logout-user/{userId}";
        public const string ValidarPermissaoUsuario = "validar-permissao-usuario";
        public const string ValidarUsuarioLogado = "validar-usuario-logado";
        #endregion

        #region [ CamposDinamicos ]
        public const string ListarPorTipoCadastro = "listar-por-tipocadastro";
        #endregion

        #region [ Cliente Fornecedor]
        public const string CadastrarClienteModal = "cadastrar-cliente-modal";
        public const string CadastrarFornecedorModal = "cadastrar-fornecedor-modal";
        public const string ObterContasAberto = "obter-contas-em-aberto";
        public const string ValidarCpfCnpj = "validar-cpf-cnpj";
        #endregion

        #region [ Cor ]
        public const string CadastrarCorModal = "cadastrar-cor-modal";
        #endregion

        #region [ Loja ]
        public const string ListarLojaComEnderecoELocalEstoque = "listar-loja-com-endereco-e-local-estoque";
        public const string ListarLojaTransferenciaEstoque = "listar-loja-transferencia-estoque";
        public const string ListarLojaUsuario = "listar-loja-usuario";
        public const string ListarLojaVendedor = "listar-loja-vendedor";
        public const string ListarLojaTabelaPreco = "listar-loja-tabela-preco";
        public const string ObterNumerosSerie = "obter-numeros-serie";
        public const string ObterInformacoesFiscais = "obter-informacoes-fiscais";
        public const string ObterCidade = "obter-cidade";
        public const string ObterRegraFiscalLoja = "obter-regra-fiscal-loja";
        public const string ObterEmailContabilista = "obter-email-contabilista";
        public const string ObterInformacoesFinalizarVenda = "obter-informacoes-finalizar-venda";
        public const string ObterConfigEmitirNFe = "obter-config-emitir-nfe";
        public const string ObterTipoAmbienteFiscal = "obter-tipo-ambiente-fiscal";
        public const string GerarPinA3 = "gerar-pin-a3/{lojaId}";
        public const string ObterInformacoesPinA3 = "obter-informacoes-pin-a3/{pin}";
        public const string ListarLojasComContasFinanceiras = "listar-lojas-com-contas-financeiras";
        public const string AtualizarServicos = "atualizar-servicos";
        public const string ObterVencimentos = "obter-vencimentos";
        public const string ObterIdentificadorUrlDelivery = "{lojaId}/identificador-url-delivery";
        #endregion

        #region [Log Auditoria]
        public const string ListarDescricaoTelas = "listar-descricao-telas";
        public const string CadastrarLogConcederPermissao = "cadastrar-log-conceder-permissao";
        public const string CadastrarLogCaixaMovel = "cadastrar-log-caixa-movel";
        #endregion

        #region [ Tamanho ]
        public const string AlterarSequenciaOrdenacao = "alterar-sequencia-ordenacao";
        public const string CadastrarTamanhoModal = "cadastrar-tamanho-modal";
        #endregion

        #region [ MultiEmpresa ]
        public const string ValidarDominio = "validar-dominio";
        public const string RecuperarDominio = "recuperar-dominio/{email}";
        public const string ValidarVersaoBancoDados = "validar-versao-banco-dados";
        public const string DominioDelivery = "dominio-delivery";
        #endregion

        #region [ Log Auditoria ]
        public const string ListarUsuariosLog = "listar-usuarios-log";
        #endregion

        #region [ Cliente/Fornecedor ]
        public const string ExcluirCliente = "excluir-cliente";
        public const string InativarCliente = "inativar-cliente";
        public const string AnonimizarCliente = "anonimizar-cliente";
        public const string ExcluirFornecedor = "excluir-fornecedor";
        public const string InativarFornecedor = "inativar-fornecedor";
        public const string ListarSelectConsultaOperacoes = "listar-select-consulta-operacoes";
        public const string ObterInformacoesClientePdv = "obter-cliente-informacoes-pdv/{id}";
        public const string CadastrarCliente = "cadastrar-cliente";
        public const string CadastrarFornecedor = "cadastrar-fornecedor";
        public const string AlterarCliente = "alterar-cliente";
        public const string AlterarFornecedor = "alterar-fornecedor";
        public const string ListarPaginadoCliente = "listar-paginado-cliente";
        public const string ListarPaginadoFornecedor = "listar-paginado-fornecedor";
        public const string ObterCliente = "obter-cliente";
        public const string ObterFornecedor = "obter-fornecedor";
        public const string ListarItensConsignados = "listar-itens-consignados";
        public const string ValidarClienteBoletoPdv = "validar-cliente-boleto-pdv/{id}";
        #endregion

        #region [ Perfil Usuário ]
        public const string ListarCategoriaPermissoes = "listar-categoria-permissoes";
        #endregion

        #region [ Nota Fiscal ]
        public const string ObterInformacoesCriarCartaCorrecao = "obter-info-criar-carta-correcao";
        public const string TransmitirNotaFiscal = "transmitir/{documentoFiscalId}";
        public const string EventoNotaFiscal = "evento";
        public const string InutilizarNotaFiscal = "inutilizar";
        public const string ConsultarProtocoloNotaFiscal = "consultar-protocolo/{documentoFiscalId}";
        public const string CalcularTotaisNotaFiscal = "calcular-totais";
        public const string CalcularValoresItemNotaFiscal = "calcular-valores-item";
        public const string ListarArquivosXml = "listar-arquivos-xml";
        public const string ImprimirNFCe = "imprimir-nfce";
        public const string ImprimirCartaCorrecao = "imprimir-carta-correcao";
        public const string Danfe = "danfe/{documentoFiscalId}";
        public const string EnviarEmailNotaFiscal = "enviar-email-nota-fiscal/{documentoFiscalId}";
        public const string ObterInformacoesOperacao = "obter-informacoes-operacao";
        public const string AlterarStatusRetorno = "alterar-status-retorno";

        #endregion

        #region [ Movimentação Financeira ]
        public const string ValidarContasEmAberto = "validar-contas-em-aberto";
        public const string ValidarLimiteDeCredito = "validar-limite-credito";
        public const string AlterarValorVencimento = "alterar-valor-vencimento";
        public const string AlterarInformacoesFiscais = "agrupamento/{identificadorAgrupamento}/alterar-informacoes-fiscais";
        public const string ObterParcelas = "obter-parcelas";
        #endregion

        #region [ Produto ]
        public const string AlterarTodosProdutosEmMassa = "alterar-todos";
        public const string AlterarProdutosFiltradosEmMassa = "alterar-filtrados";
        public const string SubstituirValorProdutosEmMassa = "substituir-valor";
        public const string CadastrarProdutoModal = "cadastrar-produto-modal";
        public const string ExcluirVariacoesCor = "excluir-variacoes-cor";
        public const string ExcluirVariacoesTamanho = "excluir-variacoes-tamanho";
        public const string ObterSaldoVariacoes = "obter-saldo-variacoes";
        public const string ObterSaldoTotalProduto = "obter-saldo-total-produto";
        public const string ObterVariacoesProduto = "obter-variacoes-produto";
        public const string ListarProdutoCores = "listar-produto-cores";
        public const string ListarProdutosBalanca = "listar-produtos-balanca";
        public const string ListarProdutosComposicao = "listar-produtos-composicao";
        #endregion

        #region [ Conciliacao ]
        public const string Conciliacao = "conciliacao";
        public const string EmailListagemConciliacao = "enviar-email-relatorio-listagem";
        public const string Antecipacao = "antecipacao";
        public const string CancelarAntecipacao = "cancelar-antecipacao";
        #endregion

        #region [ ProdutoCor] 
        public const string ListarSelectItensKit = "listar-select-itens-kit";
        #endregion

        #region [ ProdutoCorTamanho ]
        public const string ObterInformacoesParaNotaFiscal = "obter-informacoes-para-nota-fiscal";
        public const string ObterTamanhosProdutoCor = "obter-tamanhos";
        public const string ListarSelectTamanhoKits = "listar-select-tamanho-kits";
        public const string ListarSelectConferenciaEstoque = "listar-select-conferencia-estoque";
        public const string ListarSelectHistoricoProduto = "listar-select-historico-produto";
        public const string ListarSelectMovimentacaoTransferenciaEstoque = "listar-select-movimentacao-transferencia-estoque";
        public const string ListarSelectTabelaPreco = "listar-select-tabela-preco";
        public const string ObterAdicionarAutomaticamente = "obter-adicionar-automaticamente";
        public const string ObterProdutoCorTamanhoDescricaoPorProdutoId = "obter-Produto-Cor-Tamanho-Descricao";

        #endregion

        #region [ Parâmetro ]
        public const string ObterCasasDecimais = "obter-casas-decimais";
        #endregion

        #region [ Tabelas Preço ]
        public const string ListarTabelasPreco = "listar-tabelas-preco";
        public const string ObterPrecoProduto = "obter-preco-produto";
        public const string ObterProdutosParaExportar = "obter-produtos-para-exportar";
        public const string ListarPaginadoTabelaPrecoProdutoCorTamanho = "listar-paginado-tabela-preco-produto-cor-tamanho";
        public const string AlterarTabelaPrecoProdutoCorTamanho = "alterar-tabela-preco-produto-cor-tamanho";
        public const string ExcluirTabelaPrecoProdutoCorTamanho = "excluir-tabela-preco-produto-cor-tamanho";

        public const string ImportarTabelaPrecoProdutoSimplesComId = "importar-tabela-produto-simples/{id}";
        public const string ObterProdutosSimplesParaExportar = "obter-produtos-simples-para-exportar";
        public const string ListarPaginadoTabelaPrecoProdutoSimples = "listar-paginado-tabela-preco-produto-simples";
        public const string CadastrarTabelaPrecoProdutoSimples = "cadastrar-produto-simples/{id}";
        public const string AlterarTabelaPrecoProdutoSimples = "alterar-tabela-preco-produto-simples";
        public const string ExcluirTabelaPrecoProdutoSimples = "excluir-tabela-preco-produto-simples";

        #endregion

        #region [ Operação ]
        public const string ObterQuantidadePedidosDoDia = "obter-quantidade-pedidos-dia";
        public const string ObterComPagamentos = "obter-com-pagamentos";
        public const string ObterParaFinalizar = "obter-para-finalizar";
        public const string ObterParaCompartilhar = "obter-para-compartilhar";
        public const string ObterNotaFiscalAlterar = "obter-nota-fiscal-alterar";
        public const string ObterComItens = "obter-com-itens";
        public const string ObterDetalhado = "obter-detalhado";
        public const string AtualizarTotais = "atualizar-totais";
        public const string ListarPedidosDoDia = "listar-pedidos-dia";
        public const string RevisarItensKit = "revisar-itens-kit";
        public const string ListarPaginadoVendas = "listar-paginado-vendas";
        public const string ListarVendasParaTrocaPaginado = "listar-vendas-para-troca-paginado";
        public const string FinalizarVenda = "finalizar-venda";
        public const string ObterInformacoesAlterar = "obter-informacoes-alterar";
        public const string GerarNotaFiscal = "gerar-nota-fiscal";
        public const string EnviarEmailCupomNaoFiscal = "enviar-email-cupom-nao-fiscal/{operacaoId}";
        public const string EnviarEmailCupomNaoFiscalMeiaFolha = "enviar-email-cupom-nao-fiscal-meia-folha";
        public const string AlterarOperacaoTabelaPreco = "alterar-operacao-tabela-preco";
        public const string AlterarInformacoesComplementaresItem = "alterar-informacoes-complementares-item";
        public const string ObterUltimoPedidoEfetuado = "obter-ultimo-pedido-efetuado";
        public const string ListarItensVendaParaTrocaPaginado = "listar-itens-venda-para-troca-paginado";
        public const string ListarPaginadoHistoricoVenda = "listar-paginado-historico-venda";
        public const string RelatorioListagemHistoricoVenda = "relatorio-listagem-historico-venda";
        public const string EnviarEmailHistoricoVenda = "enviar-email-historico-venda";
        public const string VincularUsuarioLiberacaoDesconto = "vincular-usuario-liberacao-desconto/{operacaoId}";
        #endregion

        #region [ Troca/Devoluçao]
        public const string DevolverDinheiro = "devolver-dinheiro";
        public const string DevolverComVoucher = "devolver-com-voucher";
        public const string ObterParaCompartilharDevolucaoDinheiro = "obter-para-compartilhar-devolucao-dinheiro";
        #endregion

        #region [ Consignação ]
        public const string DevolverProdutosConsignados = "devolver-produtos-consignados";
        public const string GerarVendaConsignacao = "gerar-venda-consignacao";
        public const string ValidarConsignacaoClienteFornecedorEmAberto = "valida-consignacao-em-aberto";
        #endregion

        #region [ContaFinanceira]
        public const string ConsultarContasCaixaCofrePorLoja = "consultar-contas-caixa-cofre-por-loja";
        public const string ListarSelectContaBancos = "listar-select-conta-bancos";
        public const string ListarSelectCaixas = "listar-select-caixas";
        public const string ListarSelectCaixasLojas = "listar-select-caixas-lojas";
        public const string ListarContasFinanceirasMenosCaixas = "listar-contas-financeiras-menos-caixas";
        public const string ObterCaixaAbertoUsuarioLogado = "obter-caixa-aberto-usuario-logado";
        public const string ObterCaixaAbertoUsuario = "obter-caixa-aberto-usuario";
        public const string CadastrarCaixaMovel = "cadastrar-caixa-movel";
        public const string ObterContaCofre = "obter-conta-cofre";
        public const string ObterContaFinanceiraCaixaMovimentacao = "obter-conta-financeira-caixa-movimentacao/{caixaMovimentacaoId}";
        public const string ListarCaixasAtivos = "listar-caixas-ativos";
        #endregion

        #region [Extrato]
        public const string ListarExtratoPorConta = "listar-por-conta-financeira";
        public const string RelatorioListagemExtratoFinanceiro = "relatorio-listagem-extrato-financeiro";
        public const string ExportarExtratoFinanceiro = "exportar-extrato-financeiro";
        public const string EnviarEmailExtratoBancario = "enviar-email-extrato-financeiro";


        #endregion

        #region [CertificadoDigital]
        public const string ValidarSalvar = "ValidarSalvar";
        #endregion

        #region [Vendedor]
        public const string ListarSelectVendedorPorLoja = "listar-select-vendedor-por-loja";
        public const string ListarSelectVendedorPorLojaPorPeriodo = "listar-select-vendedor-por-loja-por-periodo";
        public const string ObterVendedorUsuario = "obter-vendedor-usuario";
        public const string VerificarPermissaoConcederDescontos = "verificar-permissao-conceder-descontos";
        #endregion

        #region [Documento Fiscal Exportação]
        public const string DocumentoFiscalExportacaoObter = "obter/{id}/{hashIdentificador}";
        public const string DocumentoFiscalExportacaoListarArquivos = "listar-arquivos/{id}/{hashIdentificador}";
        public const string DocumentoFiscalExportacaoReenviarEmail = "reenviar-email/{id}";

        #endregion

        #region [Importacoes]
        public const string ListarColunas = "listar-colunas";
        public const string ListarColunasParaImportacao = "listar-colunas-para-importacao";
        public const string DownloadCsvExemplo = "download-csv-exemplo";
        #endregion

        #region [Dashboard]
        public const string DashboardObterUltimosDozeMeses = "obter-ultimos-doze-meses";
        public const string DashboardObterMesAtual = "obter-mes-atual";
        #endregion

        #region [Conferência de Estoque]
        public const string ConferenciaEstoqueCadastrarNovosFiltros = "cadastrar-novos-filtros";
        public const string ConferenciaEstoqueVerificarConferenciaExistente = "verificar-conferencia-existente";
        public const string ConferenciaEstoqueCadastrarItens = "cadastrar-itens";
        public const string ConferenciaEstoqueAlterarItem = "alterar-item";
        public const string ConferenciaEstoqueCadastrarItensPorCodigo = "cadastrar-itens-por-codigo";
        public const string ConferenciaEstoqueExcluirItem = "excluir-item";
        public const string ConferenciaEstoqueObterConferencia = "obter-conferencia";
        public const string ConferenciaEstoqueObterDivergencias = "obter-divergencias";
        public const string ConferenciaEstoqueCorrecao = "correcao";
        public const string ConferenciaEstoqueListarItensPaginado = "listar-itens-paginado";
        public const string ConferenciaEstoqueExportarProdutos = "exportar-produtos";
        public const string ConferenciaEstoqueExportarConferencia = "exportar-conferencia";
        #endregion

        #region Movimentação de Estoque

        public const string MovimentacaoEstoqueListarItensPaginado = "listar-itens-paginado";
        public const string MovimentacaoEstoqueDuplicar = "duplicar";
        #endregion

        #region [Movidesk]
        public const string GerarTokenTemporarioMovidesk = "gerar-token-temporario-movidesk";
        #endregion

        #region Transferência de Estoque

        public const string TransferenciaEstoqueAlterarStatus = "alterar-status";
        public const string TransferenciaEstoqueListarDivergenciasEstoque = "listar-divergencias-estoque";
        public const string TransferenciaEstoqueAlterarItensDivergentes = "alterar-itens-divergentes";
        public const string ListarIdItensTransferencia = "listar-id-itens-conferencia";
        public const string ListarDescricaoItensTransferencia = "listar-descricao-itens-conferencia";


        #endregion

        #region Transportadora
        public const string CadastrarTransportadoraModal = "cadastrar-transportadora-modal";
        public const string ListarSelectEntradaMercadoria = "listar-select-entrada-mercadoria";
        public const string ObterOpcaoSelectEntradaMercadoria = "obter-opcao-select-entrada-mercadoria";
        public const string ObterOpcaoSelectEntradaMercadoriaProduto = "obter-opcao-select-entrada-mercadoria-produto";
        #endregion

        #region Entrada Mercadoria
        public const string CadastrarEntradaManual = "cadastrar-entrada-manual";
        public const string CadastrarEntradaImportacao = "cadastrar-entrada-importacao";
        public const string AdicionarItens = "adicionar-itens";
        public const string AlterarItem = "alterar-item";
        public const string AlterarItemParticipaRateioIcmsSt = "alterar-item-participa-rateio-icmsst";
        public const string RemoverItem = "remover-item";
        public const string ListarItensPaginados = "listar-itens-paginados";
        public const string ListarItensParaRateioIcmsSt = "listar-itens-para-rateio-icmsst";
        public const string AdicionarPagamentos = "adicionar-pagamentos";
        public const string AdicionarValores = "adicionar-valores";
        public const string AdicionarInformacoesGerais = "adicionar-informacoes-gerais";
        public const string ObterInformacoesProdutos = "obter-informacoes-produtos";
        public const string ObterValores = "obter-valores";
        public const string ObterInformacoesGerais = "obter-informacoes-gerais";
        public const string ObterPagamento = "obter-pagamento";
        public const string ObterProdutosLancarEstoque = "obter-produtos-lancar-estoque";
        public const string ObterHistoricoLancamentoEstoque = "obter-historico-lancamento-estoque";
        public const string LancarEstoque = "lancar-estoque";
        public const string ObterImportacaoXmlEmAndamento = "obter-importacao-xml-em-andamento";
        public const string ListarItensPaginadosImportacaoXml = "listar-itens-paginados-importacao-xml";
        public const string VincularItem = "vincular-item";
        public const string VincularXml = "vincular-xml";
        public const string EntradaMercadoriaDuplicar = "duplicar";
        public const string ObterStatusLancamentos = "obter-status-lancamentos";
        public const string ObterItensVinculados = "obter-itens-vinculados";
        public const string EstornarEstoque = "estornar-estoque";
        public const string LancarFinanceiro = "lancar-financeiro";
        public const string EstornarFinanceiro = "estornar-financeiro";
        public const string ListarNotasManifesto = "listar-notas-manifesto";
        public const string CadastrarEntradaManifesto = "cadastrar-entrada-manifesto";
        #endregion

        #region Forma de Pagamento
        public const string ListarSelectPagamento = "listar-select-pagamento";
        #endregion

        #region Forma Pagamento Recebimento
        public const string ListarFormasRecebimentoFisicas = "listar-formas-recebimento-fisicas";
        public const string ListarSelectRecebimento = "listar-select-recebimento";
        public const string ListarSelectRecebimentoFiltro = "listar-select-recebimento-filtro";
        public const string ListarSelectRecebimentoBaixa = "listar-select-recebimento-baixa";
        public const string ObterIdDinheiro = "obter-id-dinheiro";
        #endregion

        #region Suprimento/Sangria
        public const string CadastrarSuprimento = "cadastrar-suprimento";
        public const string CadastrarSangria = "cadastrar-sangria";
        #endregion

        #region Caixa Movimentação
        public const string AbrirCaixa = "abrir-caixa";
        public const string AbrirCaixaMovel = "abrir-caixa-movel";
        public const string ObterPorAgrupamento = "obter-por-agrupamento";
        public const string ExitePorIdentificadorDispositivo = "existe-por-identificador-dispositivo";
        public const string VincularAgrupamentoIntegracao = "vincular-agrupamento-integracao";
        public const string ObterInformacoesAberturaCaixa = "obter-informacoes-abertura-caixa/{contaFinanceiraId}";
        #endregion

        #region TransferenciaFinanceira
        public const string TransferenciaFinanceira = "transferencia-financeira";
        #endregion

        #region Controle Caixa  
        public const string ListarMovimentacoesPaginadasPorCaixaMovimentacao = "listar-movimentacoes-paginadas-por-caixa-movimentacao";
        public const string ReabrirCaixa = "reabrir-caixa";
        #endregion

        #region Fatura
        public const string ListarMesAtual = "listar-mes-atual";
        public const string ObterFaturaCompleta = "obter-fatura-completa";
        public const string ExcluirMovimentacaoFinanceira = "excluir-movimentacao-financeira";
        public const string MoverMovimentacaoFinanceira = "mover-movimentacao-financeira";
        #endregion

        #region Fechamento Caixa
        public const string ListarMovimentacoes = "listar-movimentacoes";
        public const string FecharCaixaMovel = "fechar-caixa-movel";
        #endregion

        #region Manifesto do Destinatario
        public const string ConsultarNotas = "consulta-notas";
        public const string RetornoConsultarNotasA3 = "retorno-consultar-notas-a3";
        public const string BaixarXml = "baixar-xml";
        public const string ImportarXml = "importar-xml";
        #endregion

        #region Eventos Manifesto do Destinatário
        public const string Ciencia = "eventos/ciencia/{documentoFiscalManifestoId}";
        public const string Confirmacao = "eventos/confirmacao/{documentoFiscalManifestoId}";
        public const string Desconhecimento = "eventos/desconhecimento/{documentoFiscalManifestoId}";
        public const string NaoRealizada = "eventos/nao-realizada/{documentoFiscalManifestoId}";
        #endregion

        #region Contas Pagar
        public const string BaixarContas = "baixar-contas";
        public const string ExcluirBaixa = "excluir-baixa";
        public const string ExcluirContas = "excluir-contas";
        public const string ObterDetalhes = "obter-detalhes";
        public const string BaixarContasCliente = "baixar-contas-cliente";
        #endregion

        #region Contas Receber
        public const string ImpressaoReciboA4 = "impressao-recibo-a4";
        public const string ListarParaConciliar = "listar-conciliar";

        #endregion

        #region Relatorio
        public const string ListarHistorico = "listar-historico";
        public const string ProcessarRelatorio = "processar-relatorio";
        #endregion

        #region Etiquetas Personalizadas
        public const string ObterInformacoesParaImprimir = "obter-informacoes-para-imprimir";
        public const string ImprimirEtiquetas = "imprimir-etiquetas";
        public const string PesquisarProdutoEtiquetas = "pesquisa-produto-etiqueta";
        #endregion

        #region Relatórios de cliente
        public const string ClienteGerarRelatorioCadastroCompleto = "gerar-relatorio-cadastro-completo";
        public const string ClienteGerarRelatorioPersonalizado = "gerar-relatorio-personalizado";
        #endregion

        #region Relatórios de produto
        public const string RelatorioProdutosPorVenda = "relatorio-produtos-por-venda";
        public const string RelatorioLucroPorProduto = "relatorio-lucro-por-produto";
        public const string RelatorioClienteAgrupado = "relatorio-produtos-por-clientes";
        public const string RelatorioNumeroContaAgrupado = "relatorio-produtos-por-conta-frente-caixa";
        public const string RelatorioProdutosComPreco = "gerar-relatorio-produtos-com-preco";
        public const string RelatorioPersonalizadoProduto = "gerar-relatorio-personalizado";
        public const string RelatorioCatalogo = "gerar-relatorio-catalogo";
        public const string RelatorioProdutosPorGrupo = "relatorio-produto-por-grupos";
        public const string RelatorioProdutosAgrupadoPorDia = "relatorio-produtos-agrupado-dia";

        public const string RelatorioItensMaisVendidos = "relatorio-itens-mais-vendidos";

        #endregion

        #region Relatórios de compra
        public const string RelatorioProdutosPorCompraDetalhado = "produto-por-compra-detalhado";
        public const string RelatorioProdutosPorCompra = "produto-por-compra";
        public const string RelatorioProdutosPorCompraComGrade = "produto-com-grade";
        #endregion

        #region Vale
        public const string ObterSaldoVale = "obter-saldo-vale";
        public const string ListarPaginadoPorCliente = "listar-paginado-por-cliente";
        #endregion

        #region Relatórios de Vendas
        public const string RelatorioVendasPorFormaRecebimento = "relatorio-vendas-por-forma-recebimento";
        public const string RelatorioVendasTotalizadasPorVendedor = "relatorio-vendas-totalizadas-vendedores";
        public const string RelatorioVendasTotalizadasPorVendedorGrafico = "relatorio-vendas-totalizadas-vendedores-grafico";
        public const string RelatorioVendasTotalizadasPorVendedorGraficoFrenteCaixa = "relatorio-vendas-totalizadas-vendedores-grafico-frente-caixa";
        public const string RelatorioLucroAgrupadoPorDia = "relatorio-lucro-por-dia";
        public const string RelatorioVendasPorDia = "relatorio-vendas-por-dia";
        public const string RelatorioVendasSimplificadas = "relatorio-vendas-simplificadas";
        public const string RelatorioVendasTotalizadasPorProduto = "relatorio-vendas-totalizadas-produto";
        public const string RelatorioVendasTotalizadasPorProdutoSimples = "relatorio-vendas-totalizadas-produto-simples";
        public const string RelatorioVendasPorEntregador = "relatorio-vendas-detalhamento-entregador";
        public const string RelatorioVendasTotalizadasPorEntregador = "relatorio-vendas-totalizadas-entregador";
        public const string ObterInformacoesCurvaAbc = "obter-informacoes-curva-abc";
        public const string ObterParaImprimirCurvaAbc = "obter-para-imprimir-curva-abc";
        public const string EnviarEmailCurvaAbc = "enviar-email-curva-abc";
        public const string ExportarCsvCurvaAbc = "exportar-csv-curva-abc";
        public const string RelatorioVendasValoresAdicionais = "relatorio-vendas-valores-adicionais";

        #endregion

        #region Metas e Comissões
        public const string ObterMetaGeral = "obter-meta-geral";
        public const string ObterRegraRepasse = "obter-regra-repasse";
        public const string ObterTipoPagamentoComissao = "obter-tipo-pagamento-comissao";

        public const string CadastrarMetaGeral = "cadastrar-meta-geral";
        public const string AlterarMetaGeral = "alterar-meta-geral";
        public const string AlterarRegraRepasse = "alterar-regra-repasse";
        public const string AlterarTipoPagamentoComissao = "alterar-tipo-pagamento-comissao";

        public const string ListarVendedoresFaixasComissao = "listar-vendedores-faixas-comissao";
        public const string ListarVendedoresMetas = "listar-vendedores-metas";

        public const string CadastrarComissaoVendedores = "cadastrar-comissao-vendedores";

        public const string ObterGraficoMetaLojaPorDia = "obter-grafico-meta-loja-por-dia";
        public const string ObterTotalizadoresMetaLoja = "obter-totalizadores-meta-loja";

        public const string ObterFaturamentoVendedores = "obter-faturamento-vendedores";
        public const string ObterPerformanceVendedores = "obter-performance-vendedores";

        public const string ObterResumoLojaVendedor = "obter-resumo-loja-vendedor";
        public const string ObterCalculoComissao = "obter-calculo-comissao";
        public const string ObterHistoricoComissaoPaginado = "obter-historico-comissao-paginado";

        public const string ImpressaoRelatorioResumoLojaMetaComissao = "impressao-relatorio-resumo-loja-meta-comissao";
        public const string EnviarEmailRelatorioResumoLojaMetaComissao = "enviar-email-relatorio-resumo-loja-meta-comissao";

        public const string ImpressaoRelatorioResumoVendedorMetaComissao = "impressao-relatorio-resumo-vendedor-meta-comissao";
        public const string EnviarEmailRelatorioResumoVendedorMetaComissao = "enviar-email-relatorio-resumo-vendedor-meta-comissao";

        public const string ImpressaoRelatorioHistoricoComissaoLoja = "impressao-relatorio-historico-comissao-loja";
        public const string EnviarEmailRelatorioHistoricoComissaoLoja = "enviar-email-relatorio-historico-comissao-loja";
        #endregion

        #region Correção
        public const string CorrecaoRecalcularVendaDashboardInicial = "recalcular-venda-dashboard-inicial";
        public const string CorrecaoRecalcularDevolucaoDashboardInicial = "recalcular-devolucao-dashboard-inicial";
        public const string CorrecaoRecriarVariacaoProduto = "recriar-variacao-produto";
        public const string CorrecaoRecriarVariacaoPadraoProduto = "recriar-variacao-padrao-produto";
        public const string PopularPesquisaProduto = "popular-pesquisa-produto";
        public const string CorrecaoFormaPagamentoRecebimentoCheque = "forma-pagamento-recebimento-cheque";
        public const string CorrecaoSaldoFormaRecebimentoContaFinanceira = "saldo-forma-recebimento-conta-financeira";
        public const string RecalcularVendasPorMes = "recalcular-vendas-por-mes";
        public const string RecalcularVendasPorDia = "recalcular-vendas-por-dia";
        public const string RecalcularItensVendidos = "recalcular-itens-vendidos";
        public const string RecalcularDevolucoes = "recalcular-devolucoes";
        public const string CorrecaoGraficoContasPagasRecebidasSTi3Dashboard = "grafico-contas-pagas-recebidas-sti3-dashboard";
        public const string CorrecaoGraficoContasPagarReceberSTi3Dashboard = "grafico-contas-pagar-receber-sti3-dashboard";
        public const string CorrecaoGraficoProdutoEmEstoqueSTi3Dashboard = "grafico-produto-em-estoque-sti3-dashboard";
        public const string CorrecaoStatusCienciaManifesto = "status-ciencia-manifesto";
        public const string RecalcularMetaComissao = "recalcular-meta-comissao";
        public const string LimparNotificacao = "limpar-notificacao";
        #endregion

        #region Integracao
        public const string EnviarCategorias = "enviar-categorias";
        public const string EnviarProdutos = "enviar-produtos";
        public const string EnviarFormasRecebimento = "enviar-formas-recebimento";
        #endregion

        #region Relatorios Operacao
        public const string RelatorioConsignacao = "relatorio-consignacao";
        public const string RelatorioItensRemovidos = "relatorio-itens-removidos-frente-caixa";

        #endregion

        #region Usuário
        public const string AlterarAdministrador = "alterar-administrador";
        public const string ObterQuantidadeLicencasUsuario = "obter-quantidade-licencas-usuario";
        public const string ObterQuantidadeUsuariosAtivos = "obter-quantidade-usuarios-ativos";
        public const string ObterDescontoMaximoPermitido = "obter-desconto-maximo-permitido";
        #endregion

        #region [Estoque]
        public const string RelatorioEstoqueSemPreco = "relatorio-estoque-sem-preco";
        public const string RelatorioEstoquePrecoVenda = "relatorio-estoque-preco-venda";
        public const string RelatorioEstoquePrecoCusto = "relatorio-estoque-preco-custo";
        public const string RelatorioCsvEstoqueSemPreco = "relatorio-csv-estoque-sem-preco";
        public const string RelatorioCsvEstoquePrecoVenda = "relatorio-csv-estoque-preco-venda";
        public const string RelatorioCsvEstoquePrecoCusto = "relatorio-csv-estoque-preco-custo";
        public const string ObterProdutosEstoque = "obter-produtos-estoque";
        #endregion

        #region [Dispositivo]
        public const string DesabilitarHabilitarLicenca = "desabilitar-habilitar-licenca";
        public const string AicionarNomePDV = "adicionar-nome-pdv";
        public const string QuantidadeContratada = "quantidade-contratada";
        public const string QuantidadeAtiva = "quantidade-ativa";
        #endregion

        #region Dashboard
        public const string ExportarGraficoFormaPagamento = "exportar-formapagto-loja";
        #endregion

        #region PesquisaProduto
        public const string ListarSelectProduto = "listar-select-produto";
        public const string ListarSelectProdutoCor = "listar-select-produto-cor";
        public const string ListarSelectProdutoCorPaginado = "listar-select-produto-cor-paginado";
        public const string ListarSelectProdutoCorTamanho = "listar-select-produto-cor-tamanho";
        #endregion

        #region [Transação Stone / Zoop]
        public const string AtualizarTransacao = "atualizar-transacao";
        public const string FecharTransacao = "fechar-transacao";

        public const string Credenciar = "credenciar";
        public const string ContaBancariaCredenciamento = "conta-bancaria-credenciamento";
        public const string ConfigurarCobrancas = "configurar-cobrancas";
        public const string ObterCredenciamento = "obter-credenciamento";
        public const string GerarTransacao = "gerar-transacao";
        public const string ListarAtividades = "listar-atividades";

        #endregion

        #region [Relatorios v2]

        public const string PDF = "pdf";
        public const string Email = "email";

        #endregion

        #region [Integração]

        public const string ObterQuantidadePendencia = "obter-quantidade-pendencia";
        public const string ObterListaClienteCpfPendencia = "obter-lista-cliente-cpf-pendencia";
        public const string ObterTotalizador = "obter-totalizador";
        public const string ObterListaMarca = "obter-lista-marca";
        public const string ObterListaCategoriaProduto = "obter-lista-categoria-produto";
        public const string ObterListaCor = "obter-lista-cor";
        public const string ObterListaTamanho = "obter-lista-tamanho";
        public const string ObterListaProduto = "obter-lista-produto";
        public const string ObterListaProdutoPaginado = "obter-lista-produto-paginado";
        public const string ObterListaProdutoTabelaPrecoPaginado = "obter-lista-produto-tabela-preco-paginado";
        public const string ObterListaProdutoPromocaoPaginado = "obter-lista-produto-promocao-paginado";
        public const string ObterListaProdutoFiltro = "obter-lista-produto-filtro";
        public const string ObterQuantidadeNotaFiscalAlerta = "obter-quantidade-nota-fiscal-alerta";
        public const string NotificarNotaFiscalEmitindo = "notificar-nota-fiscal-emitindo";
        public const string NotificarNotaFiscalAviso = "notificar-nota-fiscal-aviso";
        public const string NotificarNotaFiscalStatus = "notificar-nota-fiscal-status";
        public const string ObterTabelaPreco = "obter-tabela-preco";
        public const string ObterPromocao = "obter-promocao";
        public const string ObterPorDescricaoEIdentificacaoIntegracao = "obter-por-descricao-identificacao-integracao";

        #endregion

        #region [SmartPOS]

        public const string AlterarLocalEstoque = "alterar-local-estoque";
        public const string AlterarImagemImpressao = "alterar-imagem-impressao";
        public const string AlterarTextoImpressao = "alterar-texto-impressao";
        public const string AlterarTipoImpressao = "alterar-tipo-impressao";
        public const string AlterarIdentificacaoEtapa = "alterar-identificacao-etapa";
        public const string AlterarImpressao = "alterar-impressao";
        public const string EnviarDadosFormularioStone = "enviar-dados-formulario-stone";
        public const string AutenticarDadosStone = "autenticar-dados-stone";
        public const string ObterTipoImpressao = "obter-tipo-impressao";
        public const string ObterImagemImpressao = "obter-imagem-impressao";
        public const string ObterSenhaAdministrativa = "obter-senha-admin";
        public const string ObterAutenticacaoStone = "obter-autenticacao-stone";
        public const string ObterFormularioStone = "obter-formulario-stone";
        public const string ObterImpressao = "obter-impressao";
        public const string ObterIdentificacaoEtapa = "obter-identificacao-etapa";
        public const string ObterLocalEstoque = "obter-local-estoque";

        #endregion

        #region [Tray]

        public const string TrayObterLocalEstoque = "obter-local-estoque";
        public const string TrayAlterarIdentificacaoEtapa = "alterar-identificacao-etapa";
        public const string TrayObterIdentificacaoEtapa = "obter-identificacao-etapa";
        public const string TrayObterAutenticacao = "obter-autenticacao";
        public const string TrayObterCanalVenda = "obter-canal-venda";
        public const string TrayObterVendedor = "obter-vendedor";
        public const string TrayObterComissaoVenda = "obter-comissao-venda";
        public const string TrayObterTipoCadastro = "obter-tipo-cadastro";
        public const string TrayObterBuscarProduto = "obter-buscar-produto";
        public const string TrayObterQuantidadeProduto = "obter-quantidade-produto";
        public const string TrayObterTabelaPreco = "obter-tabela-preco";
        public const string TrayObterPromocao = "obter-promocao";
        public const string TrayCadastrarEtapa = "cadastrar-etapa";
        public const string TrayAlterarEtapa = "alterar-etapa";
        public const string TrayNotificarImportacaoEtapa = "notificar-importacao-etapa";
        public const string TrayNotificarExportacaoEtapa = "notificar-exportacao-etapa";
        public const string TrayNotificarExportacaoLimite = "notificar-exportacao-limite";
        public const string TrayNotificarPedido = "notificar-pedido";
        public const string TrayNotificarAtualizacaoTabelaPreco = "notificar-atualizacao-tabela-preco";
        public const string TrayNotificarAtualizacaoPromocao = "notificar-atualizacao-promocao";
        public const string TrayAlterarCanalVenda = "alterar-canal-venda";
        public const string TrayAlterarVendedorId = "alterar-vendedor/{vendedorId}";
        public const string TrayAlterarLocalEstoqueId = "alterar-local-estoque/{localEstoqueId}";
        public const string TrayCopiarTabelaPrecoId = "copiar-tabela-preco/{tabelaPrecoId}";
        public const string TrayAlterarTabelaPrecoId = "alterar-tabela-preco/{tabelaPrecoId}";
        public const string TrayAlterarPromocaoId = "alterar-promocao/{promocaoId}";
        public const string TrayAlterarComissaoVenda = "alterar-comissao-venda";
        public const string TrayAlterarTipoCadastro = "alterar-tipo-cadastro";
        public const string TrayAlterarBuscarProduto = "alterar-buscar-produto";
        public const string TrayVincularMarca = "vincular-marca";
        public const string TrayVincularCategoria = "vincular-categoria";
        public const string TrayVincularCor = "vincular-cor";
        public const string TrayVincularTamanho = "vincular-tamanho";
        public const string TrayVincularVariacao = "vincular-variacao";
        public const string TrayVincularProduto = "vincular-produto";
        public const string TrayVincularFormaPagamento = "vincular-forma-pagamento";
        public const string TrayPublicarProduto = "publicar-produto";
        public const string TrayExcluirProduto = "excluir-produto";
        public const string TrayCancelarPedido = "cancelar-pedido";
        public const string TrayEnviarPedido = "enviar-pedido";
        public const string TrayAlterarRastreioPedido = "alterar-rastreio-pedido";
        public const string TrayFinalizarPedido = "finalizar-pedido";
        public const string TrayAutenticarDados = "autenticar-dados";
        public const string TrayExportarCadastroProduto = "exportar-produto";
        public const string TrayImportarCadastroProduto = "importar-produto/{ignorarReferenciaEan}";
        public const string TrayAtualizacaoTabelaPreco = "atualizacao-tabela-preco";
        public const string TrayAtualizacaoPromocao = "atualizacao-promocao";
        public const string TrayCadastrarProduto = "cadastrar-produto/{cadastroPlataformaId}";
        public const string TrayDesistir = "desistir";
        public const string TrayObterListaProdutoComCadastroPaginado = "obter-lista-produto-com-cadastro-paginado";
        public const string TrayObterListaProdutoSemCadastroPaginado = "obter-lista-produto-sem-cadastro-paginado";
        public const string TrayObterListaMarcaSnapshot = "obter-lista-marca-snapshot";
        public const string TrayObterListaCategoriaSnapshot = "obter-lista-categoria-snapshot";
        public const string TrayObterListaFormaPagamentoSnapshot = "obter-lista-forma-pagamento-snapshot";
        public const string TrayObterListaProdutoSnapshot = "obter-lista-produto-snapshot";

        #endregion

        #region [Pdv Autonomo]

        public const string PdvAutonomoObterIntegracao = "obter-integracao";
        public const string PdvAutonomoCadastrarIntegracao = "cadastrar-integracao";
        public const string PdvAutonomoObterConfiguracaoParaAlterar = "obter-configuracao-para-alterar";
        public const string PdvAutonomoAlterarConfiguracao = "alterar-configuracao";
        public const string PdvAutonomoObterConfiguracaoTefParaAlterar = "obter-configuracao-tef-para-alterar";
        public const string PdvAutonomoAlterarConfiguracaoTef = "alterar-configuracao-tef";
        public const string PdvAutonomoObterConfiguracaoTabelaPrecoParaAlterar = "obter-configuracao-tabela-preco-para-alterar";
        public const string PdvAutonomoAlterarConfiguracaoTabelaPreco = "alterar-configuracao-tabela-preco";

        public const string PdvAutonomoExportar = "exportar";
        public const string PdvAutonomoNotificarExportacao = "notificar-exportacao";
        public const string PdvAutonomoGerarScript = "gerar-script";
        public const string PdvAutonomoNotificarScript = "notificar-script";
        public const string PdvAutonomoObterUrlScript = "obter-url-script";
        public const string PdvAutonomoDesistir = "desistir";
        public const string PdvAutonomoGerarPin = "gerar-pin";
        public const string PdvAutonomoObterQuantidadeAtivaDispositivo = "obter-quantidade-ativa-dispositivo";
        public const string PdvAutonomoObterListaDispositivo = "obter-lista-dispositivo";
        public const string PdvAutonomoObterDispositivo = "obter-dispositivo";
        public const string PdvAutonomoInativarDispositivo = "inativar";
        public const string PdvAutonomoAtivarDispositivo = "ativar";
        public const string PdvAutonomoAtivarLicenca = "ativar-licenca";
        public const string PdvAutonomoMarcarDispositivoComDataHoraUltimaSincronizacao = "marcar-dispositivo-com-data-hora-ultima-sincronizacao";

        public const string PdvAutonomoObterDadosPin = "obter-dados-pin/{pin}";
        public const string PdvAutonomoAbrirCaixaDispositivo = "abrir-caixa-dispositivo";
        public const string PdvAutonomoReabrirPorDispositivo = "reabrir-caixa-dispositivo";
        public const string PdvAutonomoFecharCaixaDispositivo = "fechar-caixa-dispositivo";
        public const string PdvAutonomoTransferencia = "transferencia";
        public const string PdvAutonomoVincularAgrupamento = "vincular-agrupamento";
        public const string PdvAutonomoEnviarVenda = "enviar-venda";
        public const string PdvAutonomoEnviarNotaFiscal = "enviar-nota-fiscal";
        public const string PdvAutonomoEnviarCupomSat = "enviar-cupom-sat";
        public const string PdvAutonomoEnviarInutilizacao = "enviar-inutilizacao";
        public const string PdvAutonomoClienteFornecedorRegra = "cliente-fornecedor-regra/{clienteFornecedorId}";

        public const string PdvAutonomoObterConfiguracao = "obter-configuracao";
        public const string PdvAutonomoObterListaLoja = "obter-lista-loja";
        public const string PdvAutonomoObterListaMarca = "obter-lista-marca";
        public const string PdvAutonomoObterListaCor = "obter-lista-cor";
        public const string PdvAutonomoObterListaCliente = "obter-lista-cliente";
        public const string PdvAutonomoObterListaTamanho = "obter-lista-tamanho";
        public const string PdvAutonomoObterListaCategoria = "obter-lista-categoria";
        public const string PdvAutonomoObterListaUnidade = "obter-lista-unidade";
        public const string PdvAutonomoObterListaFatorConversao = "obter-lista-fator-conversao";
        public const string PdvAutonomoObterListaAliquotaIcms = "obter-lista-aliquota-icms";
        public const string PdvAutonomoObterListaVendedor = "obter-lista-vendedor";
        public const string PdvAutonomoObterListaUsuario = "obter-lista-usuario";
        public const string PdvAutonomoObterListaUsuarioPermissao = "obter-lista-usuario-permissao";
        public const string PdvAutonomoObterListaProduto = "obter-lista-produto";
        public const string PdvAutonomoObterListaFormaPagamentoRecebimento = "obter-lista-forma-pagamento-recebimento";
        public const string PdvAutonomoObterListaCredenciadoraCartao = "obter-lista-credenciadora-cartao";
        public const string PdvAutonomoObterListaOperacaoTef = "obter-lista-operacao-tef";
        public const string PdvAutonomoObterListaParcelamentoTef = "obter-lista-parcelamento-tef";
        public const string PdvAutonomoObterListaTipoCartaoTef = "obter-lista-tipo-cartao-tef";
        public const string PdvAutonomoObterListaRegraFiscal = "obter-lista-regra-fiscal";
        public const string PdvAutonomoObterListaNotificacaoPorDispositivo = "obter-lista-notificacao-por-dispositivo";
        public const string PdvAutonomoMarcarNotificacaoComoSincronizada = "marcar-notificacao-como-sincronizada";
        public const string PdvAutonomoObterListaNotificacaoExclusaoPorDispositivo = "obter-lista-notificacao-exclusao-por-dispositivo";
        public const string PdvAutonomoMarcarNotificacaoExclusaoComoSincronizada = "marcar-notificacao-exclusao-como-sincronizada";
        public const string PdvAutonomoObterListaPeriodoCaixa = "obter-lista-periodo-caixa";

        #endregion

        #region [Frente Caixa]

        public const string FrenteCaixaObterIntegracao = "obter-integracao";
        public const string FrenteCaixaCadastrarIntegracao = "cadastrar-integracao";
        public const string FrenteCaixaObterConfiguracaoParaAlterar = "obter-configuracao-para-alterar";
        public const string FrenteCaixaAlterarConfiguracao = "alterar-configuracao";
        public const string FrenteCaixaObterConfiguracaoTefParaAlterar = "obter-configuracao-tef-para-alterar";
        public const string FrenteCaixaAlterarConfiguracaoTef = "alterar-configuracao-tef";
        public const string FrenteCaixaObterConfiguracaoTabelaPrecoParaAlterar = "obter-configuracao-tabela-preco-para-alterar";
        public const string FrenteCaixaAlterarConfiguracaoTabelaPreco = "alterar-configuracao-tabela-preco";

        public const string FrenteCaixaExportar = "exportar";
        public const string FrenteCaixaNotificarExportacao = "notificar-exportacao";
        public const string FrenteCaixaGerarScript = "gerar-script";
        public const string FrenteCaixaNotificarScript = "notificar-script";
        public const string FrenteCaixaObterUrlScript = "obter-url-script";
        public const string FrenteCaixaDesistir = "desistir";
        public const string FrenteCaixaGerarPin = "gerar-pin";
        public const string FrenteCaixaObterQuantidadeAtivaDispositivo = "obter-quantidade-ativa-dispositivo";
        public const string FrenteCaixaObterListaDispositivo = "obter-lista-dispositivo";
        public const string FrenteCaixaObterDispositivo = "obter-dispositivo";
        public const string FrenteCaixaInativarDispositivo = "inativar";
        public const string FrenteCaixaAtivarDispositivo = "ativar";
        public const string FrenteCaixaAtivarLicenca = "ativar-licenca";
        public const string FrenteCaixaMarcarDispositivoComDataHoraUltimaSincronizacao = "marcar-dispositivo-com-data-hora-ultima-sincronizacao";

        public const string FrenteCaixaObterDadosPin = "obter-dados-pin/{pin}";
        public const string FrenteCaixaAbrirCaixaDispositivo = "abrir-caixa-dispositivo";
        public const string FrenteCaixaReabrirPorDispositivo = "reabrir-caixa-dispositivo";
        public const string FrenteCaixaFecharCaixaDispositivo = "fechar-caixa-dispositivo";
        public const string FrenteCaixaTransferencia = "transferencia";
        public const string FrenteCaixaVincularAgrupamento = "vincular-agrupamento";
        public const string FrenteCaixaEnviarVenda = "enviar-venda";
        public const string FrenteCaixaEnviarNotaFiscal = "enviar-nota-fiscal";
        public const string FrenteCaixaEnviarCupomSat = "enviar-cupom-sat";
        public const string FrenteCaixaEnviarInutilizacao = "enviar-inutilizacao";
        public const string FrenteCaixaClienteFornecedorRegra = "cliente-fornecedor-regra/{clienteFornecedorId}";

        public const string FrenteCaixaObterConfiguracao = "obter-configuracao";
        public const string FrenteCaixaObterListaLoja = "obter-lista-loja";
        public const string FrenteCaixaObterListaMarca = "obter-lista-marca";
        public const string FrenteCaixaObterListaCor = "obter-lista-cor";
        public const string FrenteCaixaObterListaCliente = "obter-lista-cliente";
        public const string FrenteCaixaObterListaTamanho = "obter-lista-tamanho";
        public const string FrenteCaixaObterListaCategoria = "obter-lista-categoria";
        public const string FrenteCaixaObterListaObservacao = "obter-lista-observacao";
        public const string FrenteCaixaObterListaCategoriaObservacao = "obter-lista-categoria-observacao";
        public const string FrenteCaixaObterListaCategoriaComplemento = "obter-lista-categoria-complemento";
        public const string FrenteCaixaObterListaUnidade = "obter-lista-unidade";
        public const string FrenteCaixaObterListaFatorConversao = "obter-lista-fator-conversao";
        public const string FrenteCaixaObterListaAliquotaIcms = "obter-lista-aliquota-icms";
        public const string FrenteCaixaObterListaVendedor = "obter-lista-vendedor";
        public const string FrenteCaixaObterListaUsuario = "obter-lista-usuario";
        public const string FrenteCaixaObterListaUsuarioPermissao = "obter-lista-usuario-permissao";
        public const string FrenteCaixaObterListaProduto = "obter-lista-produto";
        public const string FrenteCaixaObterListaFormaPagamentoRecebimento = "obter-lista-forma-pagamento-recebimento";
        public const string FrenteCaixaObterListaOperacaoTef = "obter-lista-operacao-tef";
        public const string FrenteCaixaObterListaParcelamentoTef = "obter-lista-parcelamento-tef";
        public const string FrenteCaixaObterListaTipoCartaoTef = "obter-lista-tipo-cartao-tef";
        public const string FrenteCaixaObterListaRegraFiscal = "obter-lista-regra-fiscal";
        public const string FrenteCaixaObterListaSetorEntrega = "obter-lista-setor-entrega";
        public const string FrenteCaixaObterListaPromocao = "obter-lista-promocao";
        public const string FrenteCaixaObterListaDepartamento = "obter-lista-departamento";
        public const string FrenteCaixaObterListaEntregador = "obter-lista-entregador";
        public const string FrenteCaixaObterListaProdutoFichaTecnica = "obter-lista-produto-ficha-tecnica";
        public const string FrenteCaixaObterListaEtapaProduto = "obter-lista-etapa-produto";
        public const string FrenteCaixaObterListaLojaServico = "obter-lista-loja-servico";
        public const string FrenteCaixaObterListaConfiguracaoGerenciadorImpressao = "obter-lista-configuracao-gerenciador-impressao";
        public const string FrenteCaixaObterListaGerenciadorImpressao = "obter-lista-gerenciador-impressao";
        public const string FrenteCaixaObterListaDepartamentoImpressora = "obter-lista-departamento-impressora";
        public const string FrenteCaixaObterListaImpressora = "obter-lista-impressora";
        public const string FrenteCaixaObterListaMarketplace = "obter-lista-marketplace";
        public const string FrenteCaixaObterListaCredenciadoraCartao = "obter-lista-credenciadora-cartao";
        public const string FrenteCaixaObterListaPeriodoCaixa = "obter-lista-periodo-caixa";
        public const string FrenteCaixaObterListaRelatorioPersonalizado = "obter-lista-relatorio-personalizado";
        public const string FrenteCaixaObterListaAutoAtendimento = "obter-lista-auto-atendimento";
        public const string FrenteCaixaObterListaNotificacaoPorDispositivo = "obter-lista-notificacao-por-dispositivo";
        public const string FrenteCaixaMarcarNotificacaoComoSincronizada = "marcar-notificacao-como-sincronizada";
        public const string FrenteCaixaObterListaNotificacaoExclusaoPorDispositivo = "obter-lista-notificacao-exclusao-por-dispositivo";
        public const string FrenteCaixaMarcarNotificacaoExclusaoComoSincronizada = "marcar-notificacao-exclusao-como-sincronizada";
        public const string FrenteCaixaCadastrarItemRemovido = "cadastrar-operacao-item-removido";

        #endregion

        #region [Fomer Delivery]

        #region [Aplicativo]

        public const string FomerDeliveryObterPorIdentificador = "identificador/{identificador}";
        public const string FomerDeliveryObterPerfil = "{lojaId}/perfil";

        #endregion

        #region [Configuracao]

        public const string FomerDeliveryEtapa = "etapa";
        public const string FomerDeliveryIdentificadorUrl = "identificador-url";
        public const string FomerDeliveryPerfil = "perfil";
        public const string FomerDeliveryLogo = "logo";
        public const string FomerDeliveryImagemCapa = "imagem-capa";
        public const string FomerDeliveryImagemSelecaoLoja = "imagem-selecao-loja";
        public const string FomerDeliveryCorPersonalizada = "cor-personalizada";
        public const string FomerDeliveryTempoPreparo = "tempo-preparo";
        public const string FomerDeliveryHorarioFuncionamento = "horario-funcionamento";
        public const string FomerDeliveryMidiasSociais = "midias-sociais";
        public const string FomerDeliveryTabelaPreco = "tabela-preco";
        public const string FomerDeliveryCopiarTabelaPreco = "tabela-preco/{tabelaPrecoId}/copia";
        public const string FomerDeliveryProduto = "produto";
        public const string FomerDeliveryAlterarProduto = "produtos-enviados";
        public const string FomerDeliveryAtualizarPerfil = "atualizar-perfil";
        public const string FomerDeliveryAtualizarImagens = "imagens";
        public const string FomerDeliveryAtualizarConfiguracoes = "configuracoes";
        public const string FomerDeliveryAlterarLinkAcesso = "link-acesso";

        #endregion

        #endregion
    }
}