﻿using System.ComponentModel;

namespace Zendar.Data.Enums
{
    public enum TipoRelatorioVenda
    {
        [Description("Vendas por forma de recebimento")]
        VENDA_POR_RECEBIMENTO = 0,
        [Description("Vendas por dia")]
        VENDA_POR_DIA = 1,
        [Description("Vendas simplificadas")]
        VENDA_SIMPLIFICADA = 2,
        [Description("Vendas e pedidos")]
        VENDAS_PEDIDOS = 3,
        [Description("Gráfico Vendas totalizadas por vendedores")]
        VENDA_POR_VENDEDOR_GRAFICO = 4,
		[Description("Vendas totalizadas por produtos")]
		VENDA_POR_PRODUTO = 5,
        [Description("Detalhamento de entregas por entregador")]
        VENDA_DETALHAMENTO_POR_ENTREGADOR = 6,
        [Description("Resumo de entregas por entregador")]
        VENDA_POR_ENTREGADOR_RESUMO = 7,
        [Description("Valores adicionais cobrados nas vendas")]
        VENDA_VALORES_ADICIONAIS = 8,
        [Description("Vendas Totalizadas por vendedores")]
        VENDA_TOTALIZADA_POR_VENDEDORES = 9,
        [Description("Lucro agrupado por dia")]
        LUCRO_POR_DIA = 10,
        [Description("Totalização de vendas por produto")]
        VENDA_TOTALIZADA_POR_PRODUTO = 11,
        [Description("Totalização de vendas por produto (Frente de caixa)")]
        VENDA_POR_VENDEDOR_GRAFICO_FRENTE_CAIXA = 12,
    }
}
