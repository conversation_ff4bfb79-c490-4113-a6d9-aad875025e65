﻿using Multiempresa.Shared.Extension;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Zendar.Business.Interfaces;
using Zendar.Business.Interfaces.Services;
using Zendar.Business.Services.Fiscal.DocumentoFiscalObterServices;
using Zendar.Business.Services.V1.IntegracaoServices.Tray.Interfaces;
using Zendar.Data.Enums;
using Zendar.Data.Interfaces.Aplicacao;
using Zendar.Data.Models.Aplicacao;
using Zendar.Data.Resources.Mensagens;
using Zendar.QueueService;
using Zendar.QueueService.Enums;
using Zendar.QueueService.Interfaces;
using Zendar.QueueService.Messages.BackgroundTasks;

namespace Zendar.Business.Services.V1.Fenix
{
    public class FenixService : BaseService, IFenixService
    {
        private readonly IAspNetUserInfo _aspNetUserInfo;
        private readonly IServiceBusEnqueueMessage _serviceBusEnqueueMessage;
        private readonly ILogAuditoriaService _logAuditoriaService;
        private readonly IDocumentoFiscalObterService _documentoFiscalObterService;
        private readonly IAutenticacaoService _autenticacaoService;
        private readonly ILojaRepository _lojaRepository;
        private readonly IUsuarioRepository _usuarioRepository;
        private readonly IIntegracaoTrayService _integracaoTrayService;

        public FenixService(
            INotificador notificador,
            IAspNetUserInfo aspNetUserInfo,
            IServiceBusEnqueueMessage serviceBusEnqueueMessage,
            ILogAuditoriaService logAuditoriaService,
            IDocumentoFiscalObterService documentoFiscalObterService,
            IAutenticacaoService autenticacaoService,
            ILojaRepository lojaRepository,
            IUsuarioRepository usuarioRepository,
            IIntegracaoTrayService integracaoTrayService)
            : base(notificador)
        {
            _aspNetUserInfo = aspNetUserInfo;
            _serviceBusEnqueueMessage = serviceBusEnqueueMessage;
            _logAuditoriaService = logAuditoriaService;
            _documentoFiscalObterService = documentoFiscalObterService;
            _autenticacaoService = autenticacaoService;
            _lojaRepository = lojaRepository;
            _usuarioRepository = usuarioRepository;
            _integracaoTrayService = integracaoTrayService;
        }

        public void Dispose()
        {
            _logAuditoriaService?.Dispose();
            _documentoFiscalObterService?.Dispose();
            _autenticacaoService?.Dispose();
            _lojaRepository?.Dispose();
            _usuarioRepository?.Dispose();
            _integracaoTrayService?.Dispose();
        }

        public async Task ResetDatabase()
        {
            BackgroundTasksMessage message = null;

            try
            {
                if (await _integracaoTrayService.PossuiIntegracaoAtiva())
                {
                    NotificarAviso("Você ainda tem uma integração ativa com a Tray, cancele antes de prosseguir.");
                    return;
                }

                await ExportTaxFilesAllStoresAsync();

                if (PossuiErros()) return;

                var activeUsers = await GetActiveUsersAsync();

                if (activeUsers.Count > 0)
                    await ForceUsersLogoutAsync(activeUsers);

                await _usuarioRepository.Inativar();

                message = new BackgroundTasksMessage(
                    _aspNetUserInfo.HostUrl,
                    _aspNetUserInfo.CodigoContaEmpresa,
                    _aspNetUserInfo.LojaId.Value,
                    Guid.Parse(_aspNetUserInfo.Id),
                    _aspNetUserInfo.Nome,
                    _aspNetUserInfo.Email,
                    _aspNetUserInfo.TimezoneOffset.Value,
                    JsonConvert.SerializeObject(activeUsers.Select(u => u.Id)),
                    _aspNetUserInfo.Sistema.ObterDescricao(),
                    BackgroundTask.Limpar_Banco_Dados);

                _serviceBusEnqueueMessage.Send(
                    QueueNames.BackgroundTasks,
                    JsonConvert.SerializeObject(message));
            }
            catch (Exception ex)
            {
                NotificarErro(ex.Message,
                              new
                              {
                                  Fila = QueueNames.BackgroundTasks,
                                  Mensagem = message,
                                  StackTrace = ex.StackTrace,
                              });

                return;
            }

            await _logAuditoriaService.Inserir(
                new ViewModels.LogAuditoriaInserirViewModel()
                {
                    Tela = LogAuditoriaTela.RECOMECO,
                    Operacao = LogAuditoriaOperacao.LIMPAR_BANCO_DADOS,
                    Descricao = $"Solicitou a limpeza do banco de dados."
                });
        }

        private async Task ExportTaxFilesAllStoresAsync()
        {
            var lojas = await _lojaRepository.FindAllSelectAsNoTracking(l => new Loja { Id = l.Id });

            if (lojas == null || !lojas.Any()) return;

            foreach (var loja in lojas)
                await _documentoFiscalObterService.ExportarArquivos(loja.Id, lancarAvisos: false);
        }

        private async Task ForceUsersLogoutAsync(IEnumerable<Usuario> usuarios)
        {
            if (usuarios == null || !usuarios.Any()) return;

            foreach (var usuario in usuarios)
                await _autenticacaoService
                        .ForceUserLogoutAsync(
                            usuario.Id.ToString(),
                            ResourceMensagem.AutenticacaoService_LogoutLimpezaBancoDados,
                            usuario.SecurityStamp);
        }

        private async Task<List<Usuario>> GetActiveUsersAsync()
            => await _usuarioRepository
                .FindAllActiveAsync(u => new Usuario { Id = u.Id, SecurityStamp = u.SecurityStamp });
    }
}
