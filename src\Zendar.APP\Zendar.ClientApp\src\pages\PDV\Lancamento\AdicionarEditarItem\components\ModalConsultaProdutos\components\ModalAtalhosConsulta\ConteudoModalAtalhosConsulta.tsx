import {
  Heading,
  Grid,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Kbd,
  useMediaQuery,
  Text,
  Flex,
} from '@chakra-ui/react';

export function ConteudoModalAtalhosConsulta() {
  const [isLargerThan700] = useMediaQuery('(min-width: 700px)');

  return (
    <>
      <Heading as="h2" fontSize="md" mb={3}>
        Navegação por Teclado
      </Heading>
      <Grid templateColumns={{ base: '1fr', sm: '1fr 1fr' }} gap={2}>
        <Table size="sm">
          <Thead>
            <Tr>
              <Th w="full">Descrição</Th>
              <Th>Tecla</Th>
            </Tr>
          </Thead>
          <Tbody>
            <Tr>
              <Td>Navegar para baixo</Td>
              <Td>
                <Kbd color="black">↓</Kbd>
              </Td>
            </Tr>
            <Tr>
              <Td>Navegar para cima</Td>
              <Td>
                <Kbd color="black">↑</Kbd>
              </Td>
            </Tr>
            <Tr>
              <Td>Selecionar item</Td>
              <Td>
                <Kbd color="black">Enter</Kbd>
              </Td>
            </Tr>
            {!isLargerThan700 && (
              <>
                <Tr>
                  <Td>Confirmar seleção</Td>
                  <Td>
                    <Flex justify="flex-start" align="center" gap="6px">
                      <Kbd color="black">Enter</Kbd>{' '}
                      <Text as="span" fontSize="xs">
                        (2x)
                      </Text>
                    </Flex>
                  </Td>
                </Tr>
                <Tr>
                  <Td>Confirmar diretamente</Td>
                  <Td>
                    <Flex justify="flex-start" align="center" gap="6px">
                      <Kbd color="black">Shift</Kbd> +{' '}
                      <Kbd color="black">Enter</Kbd>
                    </Flex>
                  </Td>
                </Tr>
                <Tr>
                  <Td>Fechar modal</Td>
                  <Td w="fit-content">
                    <Kbd color="black">Esc</Kbd>
                  </Td>
                </Tr>
              </>
            )}
          </Tbody>
        </Table>
        {isLargerThan700 && (
          <Table size="sm">
            <Thead>
              <Tr>
                <Th w="full">Descrição</Th>
                <Th>Tecla</Th>
              </Tr>
            </Thead>
            <Tbody>
              <Tr>
                <Td>Confirmar seleção</Td>
                <Td>
                  <Flex justify="flex-start" align="center" gap="6px">
                    <Kbd color="black">Enter</Kbd>{' '}
                    <Text as="span" fontSize="xs">
                      (2x)
                    </Text>
                  </Flex>
                </Td>
              </Tr>
              <Tr>
                <Td>Confirmar diretamente</Td>
                <Td>
                  <Flex justify="flex-start" align="center" gap="6px">
                    <Kbd color="black">Shift</Kbd> +{' '}
                    <Kbd color="black">Enter</Kbd>
                  </Flex>
                </Td>
              </Tr>
              <Tr>
                <Td>Fechar modal</Td>
                <Td>
                  <Kbd color="black">Esc</Kbd>
                </Td>
              </Tr>
            </Tbody>
          </Table>
        )}
      </Grid>

      <Heading as="h2" fontSize="md" mb={3} mt={6}>
        Busca e Filtros
      </Heading>
      <Grid templateColumns={{ base: '1fr', sm: '1fr 1fr' }} gap={2}>
        <Table size="sm">
          <Thead>
            <Tr>
              <Th w="full">Descrição</Th>
              <Th>Tecla</Th>
            </Tr>
          </Thead>
          <Tbody>
            <Tr>
              <Td>Focar campo "Descrição"</Td>
              <Td>
                <Flex justify="flex-start" align="center" gap="6px">
                  <Kbd color="black">Ctrl</Kbd> + <Kbd color="black">F</Kbd>
                </Flex>
              </Td>
            </Tr>
            <Tr>
              <Td>Limpar todos os filtros</Td>
              <Td>
                <Flex justify="flex-start" align="center" gap="6px">
                  <Kbd color="black">Ctrl</Kbd> + <Kbd color="black">L</Kbd>
                </Flex>
              </Td>
            </Tr>
            {!isLargerThan700 && (
              <>
                <Tr>
                  <Td>Abrir filtros</Td>
                  <Td>
                    <Flex justify="center" align="center" gap="6px">
                      <Kbd color="black">Ctrl</Kbd> +{' '}
                      <Kbd color="black">Shift</Kbd> +{' '}
                      <Kbd color="black">F</Kbd>
                    </Flex>
                  </Td>
                </Tr>
                <Tr>
                  <Td>Realizar pesquisa</Td>
                  <Td>
                    <Flex justify="flex-start" align="center" gap="6px">
                      <Kbd color="black">Ctrl</Kbd> +{' '}
                      <Kbd color="black">Enter</Kbd>
                    </Flex>
                  </Td>
                </Tr>
              </>
            )}
          </Tbody>
        </Table>
        {isLargerThan700 && (
          <Table size="sm">
            <Thead>
              <Tr>
                <Th w="full">Descrição</Th>
                <Th>Tecla</Th>
              </Tr>
            </Thead>
            <Tbody>
              <Tr>
                <Td>Abrir filtros</Td>
                <Td>
                  <Flex justify="flex-start" align="center" gap="6px">
                    <Kbd color="black">Ctrl</Kbd> +{' '}
                    <Kbd color="black">Shift</Kbd> + <Kbd color="black">F</Kbd>
                  </Flex>
                </Td>
              </Tr>
              <Tr>
                <Td>Realizar pesquisa</Td>
                <Td>
                  <Flex justify="flex-start" align="center" gap="6px">
                    <Kbd color="black">Ctrl</Kbd> +{' '}
                    <Kbd color="black">Enter</Kbd>
                  </Flex>
                </Td>
              </Tr>
            </Tbody>
          </Table>
        )}
      </Grid>
    </>
  );
}
