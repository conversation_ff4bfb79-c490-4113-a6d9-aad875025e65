﻿using Multiempresa.Shared.Enums;
using Multiempresa.Shared.Helpers.Convertores;
using Multiempresa.Shared.Helpers.Formatadores;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Zendar.Business.Helpers.Formatadores;
using Zendar.Business.Helpers.ImpressaoRelatorioPdf;
using Zendar.Business.Interfaces;
using Zendar.Business.Interfaces.Services;
using Zendar.Business.ViewModels;
using Zendar.Business.ViewModels.Relatorios;
using Zendar.Business.ViewModels.Relatorios.Vendas;
using Zendar.Business.ViewModels.V1.Relatorios;
using Zendar.Business.ViewModels.V1.Relatorios.Vendas;
using Zendar.Data.Enums;
using Zendar.Data.Enums.Integracao;
using Zendar.Data.Helpers;
using Zendar.Data.Interfaces.Aplicacao;
using Zendar.Data.Models.Aplicacao;
using Zendar.Data.Repository.Aplicacao.IntegracaoRepositories.IntegracaoRepository;
using Zendar.Data.Resources.Mensagens;
using Zendar.Data.ViewModels;
using ZendarPackage.NotaFiscal.Enums;

namespace Zendar.Business.Services.RelatorioVendasServices.RelatorioVendaServices
{
    public class RelatorioVendaService : BaseService, IRelatorioVendaService
    {
        private readonly ILogAuditoriaService _logAuditoriaService;
        private readonly IAspNetUserInfo _aspNetUserInfo;
        private readonly IOperacaoRepository _operacaoRepository;
        private readonly IPadronizacaoService _padronizacaoService;
        private readonly IClienteFornecedorService _clienteFornecedorService;
        private readonly IVendedorService _vendedorService;
        private readonly ILojaRepository _lojaRepository;
        private readonly IPedidoOrcamentoVendaRepository _pedidoOrcamentoVendaRepository;
        private readonly IOperacaoItemRepository _operacaoItemRepository;
        private readonly IIntegracaoRepository _integracaoRepository;
        private readonly ILocalEstoqueRepository _localEstoqueRepository;

        public RelatorioVendaService(
            INotificador notificador,
            ILogAuditoriaService logAuditoriaService,
            IAspNetUserInfo aspNetUserInfo,
            IOperacaoRepository operacaoRepository,
            IClienteFornecedorService clienteFornecedorService,
            IVendedorService vendedorService,
            ILojaRepository lojaRepository,
            IPadronizacaoService padronizacaoService,
            IPedidoOrcamentoVendaRepository pedidoOrcamentoVendaRepository,
            IOperacaoItemRepository operacaoItemRepository,
            IIntegracaoRepository integracaoRepository,
            ILocalEstoqueRepository localEstoqueRepository) : base(notificador)
        {
            _logAuditoriaService = logAuditoriaService;
            _aspNetUserInfo = aspNetUserInfo;
            _operacaoRepository = operacaoRepository;
            _clienteFornecedorService = clienteFornecedorService;
            _vendedorService = vendedorService;
            _lojaRepository = lojaRepository;
            _padronizacaoService = padronizacaoService;
            _pedidoOrcamentoVendaRepository = pedidoOrcamentoVendaRepository;
            _operacaoItemRepository = operacaoItemRepository;
            _integracaoRepository = integracaoRepository;
            _localEstoqueRepository = localEstoqueRepository;
        }

        public async Task<byte[]> GerarRelatorio(FiltrosPorTipoRelatorioVendasViewModel filtrosViewModel)
        {
            var loja = await _lojaRepository.FirstOrDefaultAsNoTracking(x => x.Id == _aspNetUserInfo.LojaId.Value, x => new Loja
            {
                Fantasia = x.Fantasia
            });

            byte[] relatorio = null;

            switch (filtrosViewModel.TipoRelatorio)
            {
                case TipoRelatorioVenda.VENDA_POR_VENDEDOR_GRAFICO:
                    var vendaPorVendedor = new RelatorioVendaPorVendedorViewModel
                    {
                        TipoSistema = _aspNetUserInfo.Sistema,
                        LojaFantasia = loja.Fantasia,
                        Emissao = DateTime.UtcNow,
                        CasasDecimais = await _padronizacaoService.ObterCasasDecimais()
                    };
                    relatorio = await GerarRelatorioVendaPorVendedor(filtrosViewModel.FiltroRelatoriosVendaViewModel, vendaPorVendedor);
                    break;

                case TipoRelatorioVenda.VENDA_POR_RECEBIMENTO:
                    var vendaPorRecebimento = new RelatorioVendaPorRecebimentoViewModel
                    {
                        TipoSistema = _aspNetUserInfo.Sistema,
                        LojaFantasia = loja.Fantasia,
                        Emissao = DateTime.UtcNow,
                    };
                    relatorio = await GerarRelatorioVendaPorRecebimento(filtrosViewModel.FiltroRelatoriosVendaViewModel, vendaPorRecebimento);
                    break;

                case TipoRelatorioVenda.VENDA_POR_DIA:
                    var vendaPorDia = new RelatorioVendaPorDiaViewModel
                    {
                        TipoSistema = _aspNetUserInfo.Sistema,
                        LojaFantasia = loja.Fantasia,
                        Emissao = DateTime.UtcNow,
                    };
                    relatorio = await GerarRelatorioVendaPorDia(filtrosViewModel.FiltroRelatoriosVendaViewModel, vendaPorDia);
                    break;

                case TipoRelatorioVenda.VENDA_POR_PRODUTO:
                    var vendaPorProduto = new RelatorioVendaPorProdutoViewModel
                    {
                        TipoSistema = _aspNetUserInfo.Sistema,
                        LojaFantasia = loja.Fantasia,
                        Emissao = DateTime.UtcNow,
                        CasasDecimais = await _padronizacaoService.ObterCasasDecimais()
                    };
                    relatorio = await GerarRelatorioVendaPorProduto(filtrosViewModel.FiltroRelatoriosVendaViewModel, vendaPorProduto);
                    break;

                case TipoRelatorioVenda.VENDA_SIMPLIFICADA:
                case TipoRelatorioVenda.VENDAS_PEDIDOS:
                    var relatorioVendas = new OperacaoRelatorioListagemViewModel
                    {
                        TipoSistema = _aspNetUserInfo.Sistema,
                        LojaFantasia = loja.Fantasia,
                        Emissao = DateTime.UtcNow,
                        Titulo = filtrosViewModel.TipoRelatorio.ObterDescricao()
                    };

                    relatorio = await GerarRelatorioVendaSimples(filtrosViewModel, relatorioVendas);
                    break;

                case TipoRelatorioVenda.VENDA_VALORES_ADICIONAIS:
                    var relatorioVendasValoresAdicionais = new RelatorioVendaValoresAdicionaisViewModel
                    {
                        TipoSistema = _aspNetUserInfo.Sistema,
                        LojaFantasia = loja.Fantasia,
                        Emissao = DateTime.UtcNow,
                        Titulo = filtrosViewModel.TipoRelatorio.ObterDescricao()
                    };
                    relatorio = await GerarRelatorioVendaValoresAdicionais(filtrosViewModel.FiltroRelatoriosVendaViewModel, relatorioVendasValoresAdicionais);
                    break;

                case TipoRelatorioVenda.VENDA_DETALHAMENTO_POR_ENTREGADOR:
                    var relatorioVendasPorEntregador = new RelatorioVendaPorEntregadorDetalhadoViewModel
                    {
                        TipoSistema = _aspNetUserInfo.Sistema,
                        LojaFantasia = loja.Fantasia,
                        Emissao = DateTime.UtcNow,
                        CasasDecimais = await _padronizacaoService.ObterCasasDecimais()
                    };

                    relatorio = await GerarRelatorioVendaPorEntregadorDetalhado(filtrosViewModel.FiltroRelatoriosVendaViewModel, relatorioVendasPorEntregador);
                    break;

                case TipoRelatorioVenda.VENDA_POR_ENTREGADOR_RESUMO:
                    var vendaPorEntregador = new RelatorioVendaPorEntregadorResumoViewModel
                    {
                        TipoSistema = _aspNetUserInfo.Sistema,
                        LojaFantasia = loja.Fantasia,
                        Emissao = DateTime.UtcNow,
                        CasasDecimais = await _padronizacaoService.ObterCasasDecimais()
                    };
                    relatorio = await GerarRelatorioVendaPorEntregadorResumo(filtrosViewModel.FiltroRelatoriosVendaViewModel, vendaPorEntregador);
                    break;

                case TipoRelatorioVenda.LUCRO_POR_DIA:
                    var lucroPorDia = new RelatorioLucroPorDiaViewModel
                    {
                        TipoSistema = _aspNetUserInfo.Sistema,
                        LojaFantasia = loja.Fantasia,
                        Emissao = DateTime.UtcNow,
                    };
                    relatorio = await GerarRelatorioLucroPorDia(filtrosViewModel.FiltroRelatoriosVendaViewModel, lucroPorDia);
                    break;
                case TipoRelatorioVenda.VENDA_TOTALIZADA_POR_PRODUTO:
                    var vendaTotalizadaPorProduto = new RelatorioVendaTotalizadaPorProdutoSimples
                    {
                        TipoSistema = _aspNetUserInfo.Sistema,
                        LojaFantasia = loja.Fantasia,
                        Emissao = DateTime.UtcNow,
                    };
                    relatorio = await GerarRelatorioVendaTotalizadaPorProdutoSimples(filtrosViewModel.FiltroRelatoriosVendaViewModel, vendaTotalizadaPorProduto);
                    break;
                case TipoRelatorioVenda.VENDA_TOTALIZADA_POR_VENDEDORES:
                    var relatorioVendaTotalizadaPorVendedor = new RelatorioVendaTotalizadaPorVendedorViewModel
                    {
                        TipoSistema = _aspNetUserInfo.Sistema,
                        LojaFantasia = loja.Fantasia,
                        Emissao = DateTime.UtcNow,
                        CasasDecimais = await _padronizacaoService.ObterCasasDecimais()
                    };
                    relatorio = await GerarRelatorioVendasTotalizadasPorVendedores(filtrosViewModel.FiltroRelatoriosVendaViewModel, relatorioVendaTotalizadaPorVendedor);
                    break;
                case TipoRelatorioVenda.VENDA_POR_VENDEDOR_GRAFICO_FRENTE_CAIXA:
                    var vendaPorVendedorFrenteCaixa = new RelatorioVendaPorVendedorViewModel
                    {
                        TipoSistema = _aspNetUserInfo.Sistema,
                        LojaFantasia = loja.Fantasia,
                        Emissao = DateTime.UtcNow,
                        CasasDecimais = await _padronizacaoService.ObterCasasDecimais()
                    };
                    relatorio = await GerarRelatorioVendaPorVendedorFrenteCaixa(filtrosViewModel.FiltroRelatoriosVendaViewModel, vendaPorVendedorFrenteCaixa);
                    break;

            }

            await _logAuditoriaService.Inserir(new LogAuditoriaInserirViewModel(LogAuditoriaTela.IMPRESSAO_PERSONALIZADA, LogAuditoriaOperacao.GERAR,
                       $"Relatorio: {filtrosViewModel.TipoRelatorio.ObterDescricao()}"));

            return relatorio;
        }

        private async Task<byte[]> GerarRelatorioVendaPorRecebimento(RelatorioVendaFiltrosViewModel filtrosViewModel, RelatorioVendaPorRecebimentoViewModel relatorioVendaPorRecebimento)
        {
            var operacoes = await _operacaoRepository.ObterRelatoriosVendaRecebimento(filtrosViewModel, _aspNetUserInfo.LojaId.Value);

            if (operacoes.Count == 0)
            {
                NotificarAviso(ResourceMensagem.NenhumRegistroEncontrado);
                return null;
            }

            ConverterDate.AplicarUTC(_aspNetUserInfo.TimezoneOffset.Value, filtrosViewModel);
            ConverterDate.AplicarUTC(_aspNetUserInfo.TimezoneOffset.Value, relatorioVendaPorRecebimento);

            //Totalizadores
            decimal totalDevolucao = 0;
            decimal totalVendas = 0;
            decimal valor = 0;
            decimal porcentagem = 0;
            decimal totalPorcentagem = 0;
            decimal totalValorTotal = 0;

            relatorioVendaPorRecebimento.VendaPorRecebimento = new List<VendaPorRecebimentoViewModel>();

            var movimentacoesFinanceiras = operacoes.SelectMany(x => x.MovimentacoesFinanceiras);
            var formaRecebimento = movimentacoesFinanceiras.GroupBy(x => x.FormaPagamentoRecebimentoId);

            var somaVendasMovimentacoes = movimentacoesFinanceiras.Sum(v => v.Operacao.TipoOperacao.IdentificacaoTipoOperacao == IdentificacaoTipoOperacao.VENDA ? v.Valor : 0);

            foreach (var item in formaRecebimento)
            {
                var vendaPorRecebimento = item.ToList();

                valor = vendaPorRecebimento.Sum(x => x.Operacao.TipoOperacao.IdentificacaoTipoOperacao == IdentificacaoTipoOperacao.DEVOLUCAO ? -x.Valor : x.Valor);

                porcentagem = valor > 0 ? Math.Round(valor / somaVendasMovimentacoes * 100, 2) : 0;

                relatorioVendaPorRecebimento.VendaPorRecebimento.Add(new VendaPorRecebimentoViewModel
                {
                    Porcentagem = porcentagem,
                    Valor = valor,
                    FormaPagamento = FormatarTexto.TratarTamanhoTexto(vendaPorRecebimento.FirstOrDefault()?.FormaPagamentoRecebimento.Nome, 75).ToTitle()
                });

                totalVendas += vendaPorRecebimento.Sum(x => x.Operacao.TipoOperacao.IdentificacaoTipoOperacao == IdentificacaoTipoOperacao.VENDA ? x.Valor : 0);
                totalDevolucao += vendaPorRecebimento.Sum(x => x.Operacao.TipoOperacao.IdentificacaoTipoOperacao == IdentificacaoTipoOperacao.DEVOLUCAO ? -x.Valor : 0);
                totalPorcentagem += porcentagem;
            }

            //Colocando a diferença no primeiro valor
            var resto = 100 - totalPorcentagem;
            var first = relatorioVendaPorRecebimento.VendaPorRecebimento.Where(x => x.Porcentagem > 0).FirstOrDefault();
            if (first != null)
                first.Porcentagem += resto;

            // Preenchendo os totalizadores
            totalValorTotal += totalVendas + totalDevolucao;

            relatorioVendaPorRecebimento.TotalDevolucoes = FormatarValor.FormatarValorComPontuacao(totalDevolucao);
            relatorioVendaPorRecebimento.TotalVendas = FormatarValor.FormatarValorComPontuacao(totalVendas);
            relatorioVendaPorRecebimento.TotalValorTotal = FormatarValor.FormatarValorComPontuacao(totalValorTotal);
            relatorioVendaPorRecebimento.Filtro = await GerarTextoExibicaoFiltros(filtrosViewModel);

            return new ImpressaoRelatorioVendaPorRecebimento(relatorioVendaPorRecebimento).ToArray();
        }

        private async Task<byte[]> GerarRelatorioVendaPorDia(RelatorioVendaFiltrosViewModel filtrosViewModel, RelatorioVendaPorDiaViewModel relatorioVendaPorDia)
        {
            var operacoes = await _operacaoRepository.ObterRelatoriosVendaDia(filtrosViewModel, _aspNetUserInfo.LojaId.Value);

            if (operacoes.Count == 0)
            {
                NotificarAviso(ResourceMensagem.NenhumRegistroEncontrado);
                return null;
            }

            operacoes = operacoes.OrderBy(v => v.DataEmissao).ToList();

            ConverterDate.AplicarUTC(_aspNetUserInfo.TimezoneOffset.Value, filtrosViewModel);
            ConverterDate.AplicarUTC(_aspNetUserInfo.TimezoneOffset.Value, relatorioVendaPorDia);
            ConverterDate.AplicarUTC(_aspNetUserInfo.TimezoneOffset.Value, operacoes);

            var casasDecimais = await _padronizacaoService.ObterCasasDecimais();

            decimal qntdItens = 0;
            int qntdVendas = 0;
            decimal valor = 0;
            decimal totalQntdItens = 0;
            int totalQntdVendas = 0;
            decimal totalValor = 0;
            decimal ticketMedio = 0;
            decimal totalTicketMedio = 0;
            int qtdPessoas = 0;
            bool possuiFrenteCaixa = false;
            decimal totalTicketMedioPessoas = 0;
            relatorioVendaPorDia.vendaPorDia = new List<VendaPorDiaViewModel>();
            possuiFrenteCaixa = await _integracaoRepository.ValidarModulo(_aspNetUserInfo.LojaId.Value, Data.Enums.Integracao.IdentificacaoIntegracao.FRENTE_CAIXA);

            var DataEmissao = operacoes.GroupBy(x => x.DataEmissao.Date);

            foreach (var item in DataEmissao)
            {
                var vendaPorDia = item;

                qntdVendas = vendaPorDia.Count(x => x.TipoOperacao.IdentificacaoTipoOperacao == IdentificacaoTipoOperacao.VENDA);

                qntdItens = vendaPorDia.Sum(x => x.OperacaoItens.Sum(y => x.TipoOperacao.IdentificacaoTipoOperacao == IdentificacaoTipoOperacao.VENDA
                                                                          ? y.Quantidade
                                                                          : -y.Quantidade));

                valor = vendaPorDia.Sum(x => x.TipoOperacao.IdentificacaoTipoOperacao == IdentificacaoTipoOperacao.VENDA ? x.ValorTotal : -x.ValorTotal);

                qtdPessoas = vendaPorDia.Sum(x => x.QuantidadePessoas);

                if (qntdVendas == 0)
                    ticketMedio = 0;
                else
                    ticketMedio = qntdVendas < 0 ? (valor / qntdVendas) * -1 : valor / qntdVendas;

                totalQntdItens += qntdItens;
                totalQntdVendas += qntdVendas;
                totalValor += valor;

                relatorioVendaPorDia.vendaPorDia.Add(new VendaPorDiaViewModel
                {
                    TicketMedio = FormatarValor.FormatarValorComPontuacao(ticketMedio),
                    Data = vendaPorDia.FirstOrDefault().DataEmissao.ToString("dd/MM/yyyy"),
                    Valor = FormatarValor.FormatarValorComPontuacao(valor),
                    QuantidadeItens = FormatarValor.FormatarValorComPontuacao(qntdItens, casasDecimais.CasasDecimaisQuantidade),
                    QuantidadeVendas = qntdVendas.ToString(),
                    QtdPessoas = qtdPessoas.ToString(),
                    TicketMediaPorPessoas = qtdPessoas > 0 ? (valor / qtdPessoas) : 0,
                });
            }
            totalTicketMedio = totalQntdVendas == 0 ? 0 : totalValor / totalQntdVendas;
            totalTicketMedioPessoas = relatorioVendaPorDia.vendaPorDia.Sum(v => v.TicketMediaPorPessoas);
            relatorioVendaPorDia.TotalTicketMedioPorPessoas = FormatarValor.FormatarValorComPontuacao(totalTicketMedioPessoas);
            relatorioVendaPorDia.TotalTicketMedio = FormatarValor.FormatarValorComPontuacao(totalTicketMedio);
            relatorioVendaPorDia.TotalQuantidadeItens = FormatarValor.FormatarValorComPontuacao(totalQntdItens, casasDecimais.CasasDecimaisQuantidade);
            relatorioVendaPorDia.TotalQuantidadeVendas = totalQntdVendas.ToString();
            relatorioVendaPorDia.TotalValorTotal = FormatarValor.FormatarValorComPontuacao(totalValor);
            relatorioVendaPorDia.Filtro = await GerarTextoExibicaoFiltros(filtrosViewModel);
            relatorioVendaPorDia.PossuiFrenteDeCaixa = possuiFrenteCaixa;

            return new ImpressaoRelatorioVendaPorDia(relatorioVendaPorDia).ToArray();
        }

        private async Task<byte[]> GerarRelatorioVendaPorEntregadorDetalhado(RelatorioVendaFiltrosViewModel filtrosViewModel, RelatorioVendaPorEntregadorDetalhadoViewModel relatorioVendaPorEntregador)
        {
            var operacoesEntregador = await _operacaoRepository.ObterRelatoriosVendaEntregadorDetalhado(filtrosViewModel, _aspNetUserInfo.LojaId.Value);

            if (operacoesEntregador.Count == 0)
            {
                NotificarAviso(ResourceMensagem.NenhumRegistroEncontrado);
                return null;
            }

            ConverterDate.AplicarUTC(_aspNetUserInfo.TimezoneOffset.Value, filtrosViewModel);
            ConverterDate.AplicarUTC(_aspNetUserInfo.TimezoneOffset.Value, relatorioVendaPorEntregador);

            IEnumerable<IGrouping<string, Operacao>> operacoesAgrupadasPorEntregador = operacoesEntregador.Where(o => o.Entregador.Nome != null)
                                                                                                          .GroupBy(o => o.Entregador.Nome);
            foreach (var operacaoEntregador in operacoesAgrupadasPorEntregador)
            {
                var entregadorOperacoes = new EntregadorOperacoes
                {
                    EntregadorNome = operacaoEntregador.Key,
                    Operacoes = operacaoEntregador.Select(o => new ClienteOperacao(o.NumeroOperacao.ToString(),
                                                                                   o.ClienteFornecedor.Codigo.ToString(),
                                                                                   o.ClienteFornecedor.Nome,
                                                                                   o.ValorEntrega,
                                                                                   o.ValorTotal)).ToList()
                };
                relatorioVendaPorEntregador.EntregadorOperacoes.Add(entregadorOperacoes);
            }

            decimal totalPeriodo = _operacaoRepository.ObterValorTotalVendasPeriodo(filtrosViewModel.DataEmissaoInicio, filtrosViewModel.DataEmissaoFim, _aspNetUserInfo.LojaId.Value);
            relatorioVendaPorEntregador.TotalVendasPeriodo = FormatarValor.FormatarValorComPontuacao(totalPeriodo);
            relatorioVendaPorEntregador.Filtro = await GerarTextoExibicaoFiltros(filtrosViewModel);

            return new ImpressaoRelatorioVendaPorEntregadorDetalhado(relatorioVendaPorEntregador).ToArray();
        }
        private async Task<byte[]> GerarRelatorioLucroPorDia(RelatorioVendaFiltrosViewModel filtrosViewModel, RelatorioLucroPorDiaViewModel relatorioLucroPorDia)
        {
            var operacoes = await _operacaoRepository.ObterRelatoriosLucroPorDia(filtrosViewModel, _aspNetUserInfo.LojaId.Value);

            if (operacoes.Count == 0)
            {
                NotificarAviso(ResourceMensagem.NenhumRegistroEncontrado);
                return null;
            }
            
            ConverterDate.AplicarUTC(_aspNetUserInfo.TimezoneOffset.Value, filtrosViewModel);
            ConverterDate.AplicarUTC(_aspNetUserInfo.TimezoneOffset.Value, relatorioLucroPorDia);

            var operacoesAgrupadasPorDataEmissao = operacoes.GroupBy(g => g.DataEmissao.Date);

            foreach (var operacao in operacoesAgrupadasPorDataEmissao)
            {
                var lucroPorDia = new LucroPorDiaViewModel
                {
                    DataEmissao = operacao.Key,
                    Venda = operacao.SelectMany(s => s.OperacaoItens)
                                    .Sum(s => s.Operacao.TipoOperacao.IdentificacaoTipoOperacao == IdentificacaoTipoOperacao.VENDA ?
                                       s.ValorTotal
                                    : -s.ValorTotal),
                    Custo = operacao.SelectMany(s => s.OperacaoItens)
                                    .Sum(s => s.Operacao.TipoOperacao.IdentificacaoTipoOperacao == IdentificacaoTipoOperacao.VENDA ?
                                       s.PrecoCusto * s.Quantidade
                                    : -(s.PrecoCusto * s.Quantidade))
                };

                lucroPorDia.Lucro = lucroPorDia.Venda - lucroPorDia.Custo;
                lucroPorDia.PorcentagemLucro = lucroPorDia.Venda != 0 ? lucroPorDia.Lucro / lucroPorDia.Venda * 100 : 0;

                relatorioLucroPorDia.LucroPorDias.Add(lucroPorDia);
            }

            relatorioLucroPorDia.TotalCusto = relatorioLucroPorDia.LucroPorDias.Sum(s => s.Custo);
            relatorioLucroPorDia.TotalVenda = relatorioLucroPorDia.LucroPorDias.Sum(s => s.Venda);
            relatorioLucroPorDia.TotalLucro = relatorioLucroPorDia.LucroPorDias.Sum(s => s.Lucro);
            relatorioLucroPorDia.TotalPorcentagemLucro = relatorioLucroPorDia.TotalVenda != 0 ? (relatorioLucroPorDia.TotalLucro / relatorioLucroPorDia.TotalVenda) * 100 : 0;

            relatorioLucroPorDia.Filtro = await GerarTextoExibicaoFiltros(filtrosViewModel);

            return new ImpressaoRelatorioLucroPorDia(relatorioLucroPorDia).ToArray();
        }

        private async Task<byte[]> GerarRelatorioVendaSimples(FiltrosPorTipoRelatorioVendasViewModel filtrosViewModel, OperacaoRelatorioListagemViewModel relatorioVendas)
        {
            IQueryable<Operacao> query = null;

            if (filtrosViewModel.TipoRelatorio == TipoRelatorioVenda.VENDAS_PEDIDOS)
            {
                filtrosViewModel.FiltroRelatorioOperacoesViewModel.LojaId = _aspNetUserInfo.LojaId.Value;
                query = _pedidoOrcamentoVendaRepository.FiltrarParaRelatorioListagem(filtrosViewModel.FiltroRelatorioOperacoesViewModel);
            }
            else if (filtrosViewModel.TipoRelatorio == TipoRelatorioVenda.VENDA_SIMPLIFICADA)
            {
                query = _operacaoRepository.FiltrarVendas(filtrosViewModel.FiltroRelatoriosVendaViewModel, _aspNetUserInfo.LojaId.Value);
            }

            relatorioVendas.Operacoes = query.Select(o => new OperacaoPaginadaViewModel
            {
                NumeroOperacao = o.NumeroOperacao,
                IdentificacaoTipoOperacao = o.TipoOperacao.IdentificacaoTipoOperacao,
                Cliente = o.ClienteFornecedor.Nome,
                DataEmissao = o.DataEmissao,
                ValorTotal = o.TipoOperacao.IdentificacaoTipoOperacao == IdentificacaoTipoOperacao.DEVOLUCAO ? o.ValorTotal * -1 : o.ValorTotal,
                Status = o.Status,
                Observacao = o.Observacao,
                DocumentosFiscais = new()
                {
                    NotasFiscais = o.DocumentoFiscal.Where(d => d.Status == StatusFiscal.AUTORIZADA || d.Status == StatusFiscal.USO_DENEGADO)
                                                    .Select(d => new ModeloNumeroFiscalStatusViewModel
                                                    {
                                                        ModeloFiscal = d.ModeloFiscal,
                                                        NumeroFiscal = d.Numero
                                                    }).ToList(),
                    CuponsSat = o.CupomSat.Where(s => string.IsNullOrEmpty(s.ChaveAcessoCancelado))
                                          .Select(s => new ModeloNumeroFiscalStatusViewModel
                                          {
                                              ModeloFiscal = ModeloFiscal.SAT,
                                              NumeroFiscal = s.NumeroCupom
                                          })
                                          .ToList()
                }
            }).ToList();

            relatorioVendas.Operacoes = relatorioVendas.Operacoes.OrderBy(o => o.DataEmissao).ToList();

            if (relatorioVendas.Operacoes.Count == 0)
            {
                NotificarAviso(ResourceMensagem.NenhumRegistroEncontrado);
                return null;
            }

            ConverterDate.AplicarUTC(_aspNetUserInfo.TimezoneOffset.Value, filtrosViewModel);

            if (filtrosViewModel.FiltroRelatorioOperacoesViewModel is not null)
            {
                relatorioVendas.Filtros = await GerarTextoExibicaoFiltros(filtrosViewModel.FiltroRelatorioOperacoesViewModel);
            }
            else if (filtrosViewModel.FiltroRelatoriosVendaViewModel is not null)
            {
                relatorioVendas.Filtros = await GerarTextoExibicaoFiltros(filtrosViewModel.FiltroRelatoriosVendaViewModel);
            }

            // Preencher totalizadores
            relatorioVendas.QtdOperacoes = relatorioVendas.Operacoes.Count(o => o.Status == StatusOperacao.EFETUADA);
            relatorioVendas.ValorOperacoes = relatorioVendas.Operacoes.Where(o => o.Status == StatusOperacao.EFETUADA).Sum(o => o.ValorTotal);
            relatorioVendas.QtdCanceladas = relatorioVendas.Operacoes.Count(o => o.Status == StatusOperacao.CANCELADA);
            relatorioVendas.ValorCanceladas = relatorioVendas.Operacoes.Where(o => o.Status == StatusOperacao.CANCELADA).Sum(o => o.ValorTotal);

            if (relatorioVendas.ValorCanceladas < 0)
            {
                relatorioVendas.ValorCanceladas *= -1;
            }

            ConverterDate.AplicarUTC(_aspNetUserInfo.TimezoneOffset.Value, relatorioVendas);

            return new ImpressaoRelatorioListagemVendas(relatorioVendas).ToArray();
        }

        private async Task<byte[]> GerarRelatorioVendaPorVendedor(RelatorioVendaFiltrosViewModel filtrosViewModel, RelatorioVendaPorVendedorViewModel relatorioVendaPorVendedor)
        {
            var operacoes = await _operacaoRepository.ObterRelatoriosVendaVendedor(filtrosViewModel, _aspNetUserInfo.LojaId.Value);
            if (operacoes.Count == 0)
            {
                NotificarAviso(ResourceMensagem.NenhumRegistroEncontrado);
                return null;
            }

            ConverterDate.AplicarUTC(_aspNetUserInfo.TimezoneOffset.Value, filtrosViewModel);
            ConverterDate.AplicarUTC(_aspNetUserInfo.TimezoneOffset.Value, relatorioVendaPorVendedor);

            foreach (var devolucao in operacoes.Where(op => op.TipoOperacao.IdentificacaoTipoOperacao == IdentificacaoTipoOperacao.DEVOLUCAO))
            {
                devolucao.ValorTotal *= -1;

                foreach (var devolucaoItem in devolucao.OperacaoItens)
                    devolucaoItem.Quantidade *= -1;
            }

            var totalValorTotal = operacoes.Sum(o => o.ValorTotal);
            var totalQuantidadeVendas = operacoes.Count(o => o.TipoOperacao.IdentificacaoTipoOperacao == IdentificacaoTipoOperacao.VENDA);
            var totalItensVendidos = operacoes.Sum(oi => oi.OperacaoItens.Sum(oi => oi.Quantidade));
            var totalTicketMedio = totalQuantidadeVendas == 0 ? 0 : totalValorTotal / totalQuantidadeVendas;
            var totalProdutoPorAtendimento = totalQuantidadeVendas == 0 ? 0 : totalItensVendidos / totalQuantidadeVendas;
            var possuiIntegracao = await _integracaoRepository.ValidarModulo(_aspNetUserInfo.LojaId.Value, Data.Enums.Integracao.IdentificacaoIntegracao.FRENTE_CAIXA);

            var VendaPorVendedor = operacoes.GroupBy(op => op.VendedorId).Select(op =>
            {
                var operacao = op.FirstOrDefault();
                var ItensVendidos = op.Sum(o => o.OperacaoItens.Sum(oi => oi.Quantidade));
                var valorTotal = op.Sum(o => o.ValorTotal);
                var quantidadeVendas = op.Count(o => o.TipoOperacao.IdentificacaoTipoOperacao == IdentificacaoTipoOperacao.VENDA);
                var porcentagem = totalValorTotal == 0 ? 0 : valorTotal / totalValorTotal * 100;
                var ticketMedio = quantidadeVendas == 0 ? 0 : valorTotal / quantidadeVendas;
                var produtoPorAtendimento = quantidadeVendas == 0 ? 0 : ItensVendidos / quantidadeVendas;
                var taxaServico = op.SelectMany(s => s.OperacaoItens).Sum(s => s.ValorTaxaServico);

                return new VendaPorVendedorViewModel
                {
                    Vendedor = operacao.Vendedor.Nome,
                    ItensVendidos = ItensVendidos,
                    QuantidadeVendas = quantidadeVendas,
                    Valor = valorTotal,
                    Porcentagem = porcentagem,
                    TicketMedio = ticketMedio,
                    TaxaServico = taxaServico,
                    ProdutoPorAtendimento = produtoPorAtendimento,
                };
            });

            relatorioVendaPorVendedor.VendaPorVendedor = VendaPorVendedor.OrderByDescending(c => c.Valor).ToList();
            relatorioVendaPorVendedor.TotalTaxaServico = VendaPorVendedor.Sum(s => s.TaxaServico);
            relatorioVendaPorVendedor.TotalQuantidadeVendas = totalQuantidadeVendas;
            relatorioVendaPorVendedor.TotalTicketMedio = totalTicketMedio;
            relatorioVendaPorVendedor.TotalProdutoPorAtendimento = totalProdutoPorAtendimento;
            relatorioVendaPorVendedor.TotalValor = totalValorTotal;
            relatorioVendaPorVendedor.PossuiFrenteCaixa = possuiIntegracao;
            relatorioVendaPorVendedor.TotalItensVendidos = totalItensVendidos;
            relatorioVendaPorVendedor.Filtro = await GerarTextoExibicaoFiltros(filtrosViewModel);

            if (relatorioVendaPorVendedor.TotalValor == 0)
            {
                NotificarAviso(ResourceMensagem.RelatorioVendas_ValorTotalZerado);
                return null;
            }

            return new ImpressaoRelatorioVendaPorVendedor(relatorioVendaPorVendedor).ToArray();
        }
        private async Task<byte[]> GerarRelatorioVendasTotalizadasPorVendedores(RelatorioVendaFiltrosViewModel filtrosViewModel, RelatorioVendaTotalizadaPorVendedorViewModel relatorioVendaTotalizadaPorVendedor)
        {
            var operacoes = await _operacaoRepository.ObterRelatoriosVendaTotalizadasVendedor(filtrosViewModel, _aspNetUserInfo.LojaId.Value);

            if (operacoes.Count == 0)
            {
                NotificarAviso(ResourceMensagem.NenhumRegistroEncontrado);
                return null;
            }

            ConverterDate.AplicarUTC(_aspNetUserInfo.TimezoneOffset.Value, filtrosViewModel);
            ConverterDate.AplicarUTC(_aspNetUserInfo.TimezoneOffset.Value, relatorioVendaTotalizadaPorVendedor);

            foreach (var devolucao in operacoes.Where(op => op.TipoOperacao.IdentificacaoTipoOperacao == IdentificacaoTipoOperacao.DEVOLUCAO))
            {
                devolucao.ValorTotal *= -1;

                foreach (var devolucaoItem in devolucao.OperacaoItens)
                    devolucaoItem.Quantidade *= -1;
            }

            var VendaPorVendedor = operacoes.GroupBy(op => op.VendedorId).Select(op =>
            {
                var operacao = op.FirstOrDefault();
                var totalProdutos = op.Sum(o => o.ValorTotalItensSemDesconto);
                var totalAcrescimo = op.SelectMany(s => s.OperacaoItens).Sum(oi => oi.ValorTotalAcrescimo);
                var taxaServico = op.SelectMany(sm => sm.OperacaoItens).Sum(s => s.ValorTaxaServico);
                var outrosAcrescimos = op.Sum(s => s.ValorTotalFrete + s.ValorTotalOutrasDespesas + s.ValorEntrega);
                var desconto = op.Sum(s => s.ValorTotalDescontoItem + s.ValorTotalDescontoAdicional);
                var total = op.Sum(s => s.ValorTotal);

                return new VendaTotalizadaPorVendedorViewModel
                {
                    Vendedor = operacao.Vendedor.Nome,
                    Valor = totalProdutos,
                    Desconto = desconto,
                    TaxaServico = taxaServico,
                    OutrosAcrescimos = outrosAcrescimos,
                    Total = total,
                };
            });

            relatorioVendaTotalizadaPorVendedor.VendaTotalizadaPorVendedor = VendaPorVendedor.OrderByDescending(c => c.Valor).ToList();
            relatorioVendaTotalizadaPorVendedor.TotalDesconto = VendaPorVendedor.Sum(s => s.Desconto);
            relatorioVendaTotalizadaPorVendedor.TotalValor = VendaPorVendedor.Sum(s => s.Total);
            relatorioVendaTotalizadaPorVendedor.TotalProdutos = VendaPorVendedor.Sum(s => s.Valor);
            relatorioVendaTotalizadaPorVendedor.TotalOutrosAcrescimos = VendaPorVendedor.Sum(s => s.OutrosAcrescimos);
            relatorioVendaTotalizadaPorVendedor.TotalTaxaServico = VendaPorVendedor.Sum(s => s.TaxaServico);
            relatorioVendaTotalizadaPorVendedor.Filtro = await GerarTextoExibicaoFiltros(filtrosViewModel);

            if (relatorioVendaTotalizadaPorVendedor.TotalValor == 0)
            {
                NotificarAviso(ResourceMensagem.RelatorioVendas_ValorTotalZerado);
                return null;
            }

            return new ImpressaoRelatorioVendaTotalizadaPorVendedor(relatorioVendaTotalizadaPorVendedor).ToArray();
        }
        private async Task<byte[]> GerarRelatorioVendaPorProduto(RelatorioVendaFiltrosViewModel filtrosViewModel, RelatorioVendaPorProdutoViewModel relatorioVendaPorProduto)
        {
            var operacaoItens = await _operacaoItemRepository.ObterRelatoriosVendaProduto(filtrosViewModel, _aspNetUserInfo.LojaId.Value);

            if (operacaoItens.Count == 0)
            {
                NotificarAviso(ResourceMensagem.NenhumRegistroEncontrado);
                return null;
            }

            ConverterDate.AplicarUTC(_aspNetUserInfo.TimezoneOffset.Value, filtrosViewModel);
            ConverterDate.AplicarUTC(_aspNetUserInfo.TimezoneOffset.Value, relatorioVendaPorProduto);

            var produtosAgrupados = operacaoItens.GroupBy(oi => oi.ProdutoCorTamanho.ProdutoCor.Produto.Id);
            foreach (var produto in produtosAgrupados)
            {
                var first = produto.First();

                relatorioVendaPorProduto.Itens.Add(new ItemRelatorioVendaPorProdutoViewModel()
                {
                    Categoria = first.ProdutoCorTamanho.ProdutoCor.Produto.CategoriaProduto.Nome,
                    Produto = first.ProdutoCorTamanho.FormatarDescricaoCompleta(),
                    Quantidade = produto.Sum(i => i.Operacao.TipoOperacao.IdentificacaoTipoOperacao == IdentificacaoTipoOperacao.VENDA
                                                 ? i.Volumes
                                                 : -i.Volumes),
                    ValorTotal = produto.Sum(i => i.Operacao.TipoOperacao.IdentificacaoTipoOperacao == IdentificacaoTipoOperacao.VENDA
                                                 ? i.ValorTotal
                                                 : -i.ValorTotal)
                });
            }

            relatorioVendaPorProduto.Filtro = await GerarTextoExibicaoFiltros(filtrosViewModel);

            return new ImpressaoRelatorioVendaPorProduto(relatorioVendaPorProduto).ToArray();
        }
        private async Task<byte[]> GerarRelatorioVendaPorEntregadorResumo(RelatorioVendaFiltrosViewModel filtrosViewModel, RelatorioVendaPorEntregadorResumoViewModel relatorioVendaPorEntregador)
        {
            var operacoes = await _operacaoRepository.ObterRelatoriosVendaEntregadorResumo(filtrosViewModel, _aspNetUserInfo.LojaId.Value);

            if (operacoes.Count == 0)
            {
                NotificarAviso(ResourceMensagem.NenhumRegistroEncontrado);
                return null;
            }

            if (!operacoes.Any(a => a.EntregadorId.HasValue))
            {
                NotificarAviso(ResourceMensagem.NenhumRegistroEncontrado);
                return null;
            }

            ConverterDate.AplicarUTC(_aspNetUserInfo.TimezoneOffset.Value, filtrosViewModel);
            ConverterDate.AplicarUTC(_aspNetUserInfo.TimezoneOffset.Value, relatorioVendaPorEntregador);

            var operacoesEntregador = operacoes.Where(x => x.EntregadorId.HasValue);

            var valorTotalVendaEntregas = operacoesEntregador.Sum(s => s.ValorTotal);
            var valorTotalEntregas = operacoesEntregador.Sum(s => s.ValorEntrega);
            var totalEntregas = operacoesEntregador.Count(o => o.TipoOperacao.IdentificacaoTipoOperacao == IdentificacaoTipoOperacao.VENDA);
            var vendaPorEntregador = operacoesEntregador.GroupBy(g => g.EntregadorId).Select(op =>
            {
                var operacao = op.FirstOrDefault();
                var quantidadeEntregas = op.Count(o => o.TipoOperacao.IdentificacaoTipoOperacao == IdentificacaoTipoOperacao.VENDA);
                var valorTotal = op.Sum(o => o.ValorTotal);
                var valorEntregas = op.Sum(s => s.ValorEntrega);

                return new VendaPorEntregadorViewModel
                {
                    Entregador = operacao.Entregador.Nome,
                    Quantidade = quantidadeEntregas,
                    ValorEntregas = valorEntregas,
                    TotalVenda = valorTotal
                };
            });

            relatorioVendaPorEntregador.VendasPorEntregador = vendaPorEntregador.OrderByDescending(c => c.ValorEntregas).ToList();
            relatorioVendaPorEntregador.TotalEntregas = totalEntregas;
            relatorioVendaPorEntregador.ValorTotalEntregas = valorTotalEntregas;
            relatorioVendaPorEntregador.TotalVendasEntregas = valorTotalVendaEntregas;
            relatorioVendaPorEntregador.TotalVendasPeriodo = _operacaoRepository.ObterValorTotalVendasPeriodo(filtrosViewModel.DataEmissaoInicio, filtrosViewModel.DataEmissaoFim, _aspNetUserInfo.LojaId.Value); ;

            relatorioVendaPorEntregador.Filtro = await GerarTextoExibicaoFiltros(filtrosViewModel);

            return new ImpressaoRelatorioVendaPorEntregadorResumo(relatorioVendaPorEntregador).ToArray();
        }
        private async Task<byte[]> GerarRelatorioVendaTotalizadaPorProdutoSimples(RelatorioVendaFiltrosViewModel filtrosViewModel, RelatorioVendaTotalizadaPorProdutoSimples relatorioTotalizadoPorProduto)
        {
            var operacoes = await _operacaoRepository.ObterRelatoriosVendaTotalizadaPorProdutoSimples(filtrosViewModel, _aspNetUserInfo.LojaId.Value);

            if (operacoes.Count == 0)
            {
                NotificarAviso(ResourceMensagem.NenhumRegistroEncontrado);
                return null;
            }

            foreach (var devolucao in operacoes.Where(op => op.TipoOperacao.IdentificacaoTipoOperacao == IdentificacaoTipoOperacao.DEVOLUCAO))
            {
                devolucao.ValorTotal *= -1;

                foreach (var devolucaoItem in devolucao.OperacaoItens)
                    devolucaoItem.Quantidade *= -1;
            }

            ConverterDate.AplicarUTC(_aspNetUserInfo.TimezoneOffset.Value, filtrosViewModel);
            ConverterDate.AplicarUTC(_aspNetUserInfo.TimezoneOffset.Value, relatorioTotalizadoPorProduto);

            var vendaPorProduto = operacoes.SelectMany(sm => sm.OperacaoItens)
            .GroupBy(g => g.ProdutoCorTamanho.ProdutoCor.Produto.Nome)
            .Select(op =>
            {
                var operacao = op.FirstOrDefault();

                return new VendaTotalizadaPorProdutoViewModel
                {
                    Descricao = op.Key,
                    Quantidade = op.Sum(s => s.Quantidade),
                    ValorTotal = op.Sum(i => i.Operacao.TipoOperacao.IdentificacaoTipoOperacao == IdentificacaoTipoOperacao.VENDA
                                                 ? i.ValorTotal
                                                 : -i.ValorTotal),

                };
            });

            relatorioTotalizadoPorProduto.VendasTotalizadasPorProduto = vendaPorProduto.ToList();

            relatorioTotalizadoPorProduto.Filtro = await GerarTextoExibicaoFiltros(filtrosViewModel);
            relatorioTotalizadoPorProduto.ValorTotal = vendaPorProduto.Sum(s => s.ValorTotal);
            relatorioTotalizadoPorProduto.TotalQuantidade = vendaPorProduto.Sum(s => s.Quantidade);

            return new ImpressaoRelatorioVendaTotalizadaPorProdutoSimples(relatorioTotalizadoPorProduto).ToArray();
        }
        private async Task<byte[]> GerarRelatorioVendaValoresAdicionais(RelatorioVendaFiltrosViewModel filtrosViewModel, RelatorioVendaValoresAdicionaisViewModel relatorioVendasValoresAdicionais)
        {
            var operacoes = await _operacaoRepository.ObterRelatoriosVendaValoresAdicionais(filtrosViewModel, _aspNetUserInfo.LojaId.Value);

            if (operacoes.Count == 0)
            {
                NotificarAviso(ResourceMensagem.NenhumRegistroEncontrado);
                return null;
            }

            ConverterDate.AplicarUTC(_aspNetUserInfo.TimezoneOffset.Value, filtrosViewModel);
            ConverterDate.AplicarUTC(_aspNetUserInfo.TimezoneOffset.Value, relatorioVendasValoresAdicionais);

            relatorioVendasValoresAdicionais.ValoresAdicionais = operacoes.Select(o => new VendaValoresAdicionaisViewModel(o.NumeroOperacao.ToString(),
                                                                                                                           o.ClienteFornecedor.Codigo.ToString(),
                                                                                                                           o.ClienteFornecedor.Nome,
                                                                                                                           o.DataEmissao.ToShortDateString(),
                                                                                                                           o.ValorTotalItensSemDesconto,
                                                                                                                           o.ValorTotalAcrescimo,
                                                                                                                           o.ValorTotalFrete,
                                                                                                                           o.ValorTotalOutrasDespesas,
                                                                                                                           o.ValorTotalDescontoAdicional,
                                                                                                                           o.ValorTotalDescontoItem,
                                                                                                                           o.ValorCouvert,
                                                                                                                           o.ValorEntrega,
                                                                                                                           o.ValorTotal,
                                                                                                                           o.CupomSat,
                                                                                                                           o.DocumentoFiscal)).ToList();

            relatorioVendasValoresAdicionais.Filtro = await GerarTextoExibicaoFiltros(filtrosViewModel);
            relatorioVendasValoresAdicionais.TotalizadorVenda.CalcularTotais(relatorioVendasValoresAdicionais.ValoresAdicionais);
            return new ImpressaoRelatorioVendaValoresAdicionais(relatorioVendasValoresAdicionais).ToArray();
        }
        private async Task<string> GerarTextoExibicaoFiltros(RelatorioVendaFiltrosViewModel filtrosViewModel)
        {
            var filtro = new StringBuilder();

            filtro.Append($"Período: {filtrosViewModel.DataEmissaoInicio:dd/MM/yyyy HH:mm} até {filtrosViewModel.DataEmissaoFim:dd/MM/yyyy HH:mm}");


            if (filtrosViewModel.StatusConsulta != null)
            {
                if (filtro.Length > 0)
                    filtro.Append(" | ");

                filtro.Append($"Status: {filtrosViewModel.StatusConsulta.ObterDescricao()}");
            }

            if (filtrosViewModel.ClienteFornecedorId.HasValue)
            {
                var cliente = await _clienteFornecedorService.Obter(filtrosViewModel.ClienteFornecedorId.Value);

                if (filtro.Length > 0)
                    filtro.Append(" | ");

                filtro.Append($"Cliente: {cliente.Nome}");
            }

            if (filtrosViewModel.VendedorId.HasValue)
            {
                var vendedor = await _vendedorService.Obter(filtrosViewModel.VendedorId.Value);

                if (filtro.Length > 0)
                    filtro.Append(" | ");

                filtro.Append($"Vendedor: {vendedor.Nome}");
            }

            if (filtrosViewModel.Origem.HasValue)
            {
                if (filtro.Length > 0) filtro.Append(" | ");
                filtro.Append($"Origem: {filtrosViewModel.Origem.Value.ObterDescricao()}");
            }
            else
            {
                if (filtro.Length > 0) filtro.Append(" | ");
                filtro.Append($"Origem: PDV");
            }

            if (filtrosViewModel.LocalEstoqueId.HasValue)
            {
                if (filtro.Length > 0) filtro.Append(" | ");
                filtro.Append($"Local Estoque: {await _localEstoqueRepository.ObterNome(filtrosViewModel.LocalEstoqueId.Value)}");
            }

            if (filtrosViewModel.TipoFiscal.Any() && !filtrosViewModel.TipoFiscal.Contains(TipoFiscal.TODOS))
            {
                if (filtro.Length > 0) filtro.Append(" | ");

                var tipoFiscal = filtrosViewModel.TipoFiscal
                    .Select(tipo => tipo.ObterDescricao());

                filtro.Append($"Tipo Fiscal: {string.Join(", ", tipoFiscal)}");
            }

            if (filtrosViewModel.Origem.HasValue && filtrosViewModel.Origem != IdentificacaoIntegracao.TODAS)
            {
                if (filtro.Length > 0) filtro.Append(" | ");
                filtro.Append($"Origem: {filtrosViewModel.Origem.Value.ObterDescricao()}");
            }

            if (filtrosViewModel.StatusVenda.HasValue && filtrosViewModel.StatusVenda != StatusVenda.TODAS)
            {
                if (filtro.Length > 0) filtro.Append(" | ");
                var statusOperacao = filtrosViewModel.StatusVenda.Value;
                filtro.Append($"Status das Vendas: {statusOperacao}");
            }

            return filtro.ToString();
        }
        private async Task<string> GerarTextoExibicaoFiltros(OperacaoFiltrosViewModel filtros)
        {
            var filtro = new StringBuilder();

            if (filtros.Status.HasValue
                || filtros.NumeroOperacao.HasValue
                || filtros.ClienteId.HasValue
                || filtros.IdentificacaoTipoOperacao.Any())
            {
                filtro.Append($"Período: {filtros.DataEmissaoInicio:dd/MM/yyyy} até {filtros.DataEmissaoFim:dd/MM/yyyy}");

                if (filtros.NumeroOperacao.HasValue)
                {
                    filtro.Append($"Número: {filtros.NumeroOperacao.ToString().PadLeft(8, '0')}");
                }

                if (filtros.IdentificacaoTipoOperacao.Any())
                {
                    if (filtro.Length > 0) filtro.Append(" | ");

                    var operacoes = filtros.IdentificacaoTipoOperacao.Select(i => i.ObterDescricao());

                    filtro.Append($"Operação: {string.Join(", ", operacoes)}");
                }

                if (filtros.Status != null)
                {
                    if (filtro.Length > 0) filtro.Append(" | ");
                    filtro.Append($"Status: {filtros.Status.ObterDescricao()}");
                }

                if (filtros.ClienteId.HasValue)
                {
                    var cliente = await _clienteFornecedorService.Obter(filtros.ClienteId.Value);

                    if (filtro.Length > 0) filtro.Append(" | ");
                    filtro.Append($"Cliente: {cliente.Nome}");
                }

                if (filtros.Origem.HasValue)
                {
                    if (filtro.Length > 0) filtro.Append(" | ");
                    filtro.Append($"Origem: {filtros.Origem.Value.ObterDescricao()}");
                }
                else
                {
                    if (filtro.Length > 0) filtro.Append(" | ");
                    filtro.Append($"Origem: PDV");
                }
            }

            return filtro.ToString();
        }
        private async Task<byte[]> GerarRelatorioVendaPorVendedorFrenteCaixa(RelatorioVendaFiltrosViewModel filtrosViewModel, RelatorioVendaPorVendedorViewModel relatorioVendaPorVendedor)
        {
            var operacoes = await _operacaoRepository.ObterRelatoriosVendaVendedor(filtrosViewModel, _aspNetUserInfo.LojaId.Value);
            if (operacoes.Count == 0)
            {
                NotificarAviso(ResourceMensagem.NenhumRegistroEncontrado);
                return null;
            }

            ConverterDate.AplicarUTC(_aspNetUserInfo.TimezoneOffset.Value, filtrosViewModel);
            ConverterDate.AplicarUTC(_aspNetUserInfo.TimezoneOffset.Value, relatorioVendaPorVendedor);

            foreach (var devolucao in operacoes.Where(op => op.TipoOperacao.IdentificacaoTipoOperacao == IdentificacaoTipoOperacao.DEVOLUCAO))
            {
                devolucao.ValorTotal *= -1;

                foreach (var devolucaoItem in devolucao.OperacaoItens)
                    devolucaoItem.Quantidade *= -1;
            }

            var totalValorTotal = operacoes.Sum(o => o.ValorTotal);
            var totalQuantidadeVendas = operacoes.Count(o => o.TipoOperacao.IdentificacaoTipoOperacao == IdentificacaoTipoOperacao.VENDA);
            var totalItensVendidos = operacoes.Sum(oi => oi.OperacaoItens.Sum(oi => oi.Quantidade));
            var totalTicketMedio = totalQuantidadeVendas == 0 ? 0 : totalValorTotal / totalQuantidadeVendas;
            var totalProdutoPorAtendimento = totalQuantidadeVendas == 0 ? 0 : totalItensVendidos / totalQuantidadeVendas;
            var possuiIntegracao = await _integracaoRepository.ValidarModulo(_aspNetUserInfo.LojaId.Value, Data.Enums.Integracao.IdentificacaoIntegracao.FRENTE_CAIXA);

            var VendaPorVendedor = operacoes
                .SelectMany(op => op.OperacaoItens.Select(oi => new
                {
                    OperacaoId = op.Id,
                    VendedorId = oi.Vendedor?.Id,
                    NomeVendedor = oi.Vendedor?.Nome ?? "",
                    Quantidade = oi.Quantidade,
                    ValorOperacao = op.ValorTotal,
                    TipoOperacao = op.TipoOperacao.IdentificacaoTipoOperacao,
                    ValorTaxaServico = oi.ValorTaxaServico,
                    ValorItem = oi.ValorTotal
                }))
                .GroupBy(x => new { x.VendedorId, x.NomeVendedor })
                .Select(g =>
                {
                    var valorItem = g.Sum(x => x.ValorItem);
                    var itensVendidos = g.Sum(x => x.Quantidade);
                    var quantidadeVendas = g.Select(x => x.OperacaoId).Distinct().Count();

                    var porcentagem = totalValorTotal == 0 ? 0 : valorItem / totalValorTotal * 100;
                    var ticketMedio = quantidadeVendas == 0 ? 0 : valorItem / quantidadeVendas;
                    var produtoPorAtendimento = quantidadeVendas == 0 ? 0 : itensVendidos / quantidadeVendas;
                    var taxaServico = g.Sum(x => x.ValorTaxaServico);

                    return new VendaPorVendedorViewModel
                    {
                        Vendedor = g.Key.NomeVendedor,
                        ItensVendidos = itensVendidos,
                        QuantidadeVendas = quantidadeVendas,
                        Valor = valorItem,
                        Porcentagem = porcentagem,
                        TicketMedio = ticketMedio,
                        TaxaServico = taxaServico,
                        ProdutoPorAtendimento = produtoPorAtendimento
                    };
                })
                .ToList();

            relatorioVendaPorVendedor.VendaPorVendedor = VendaPorVendedor.OrderByDescending(c => c.Valor).ToList();
            relatorioVendaPorVendedor.TotalTaxaServico = VendaPorVendedor.Sum(s => s.TaxaServico);
            relatorioVendaPorVendedor.TotalQuantidadeVendas = totalQuantidadeVendas;
            relatorioVendaPorVendedor.TotalTicketMedio = totalTicketMedio;
            relatorioVendaPorVendedor.TotalProdutoPorAtendimento = totalProdutoPorAtendimento;
            relatorioVendaPorVendedor.TotalValor = totalValorTotal;
            relatorioVendaPorVendedor.PossuiFrenteCaixa = possuiIntegracao;
            relatorioVendaPorVendedor.TotalItensVendidos = totalItensVendidos;
            relatorioVendaPorVendedor.Filtro = await GerarTextoExibicaoFiltros(filtrosViewModel);

            if (relatorioVendaPorVendedor.TotalValor == 0)
            {
                NotificarAviso(ResourceMensagem.RelatorioVendas_ValorTotalZerado);
                return null;
            }

            return new ImpressaoRelatorioVendaPorVendedor(relatorioVendaPorVendedor).ToArray();
        }
    }
}
