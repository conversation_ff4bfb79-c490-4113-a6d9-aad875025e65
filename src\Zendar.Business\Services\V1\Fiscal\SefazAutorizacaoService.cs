﻿using Microsoft.AspNetCore.SignalR;
using Multiempresa.Data.Models;
using Multiempresa.Data.Repositories.NotaFiscalRejeicoesRepositories;
using Multiempresa.Data.Repositories.ParametrosNotaFiscalPorUfRepositories;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography.X509Certificates;
using System.Threading;
using System.Threading.Tasks;
using Zendar.Business.API.SefazWs;
using Zendar.Business.Background;
using Zendar.Business.Consts;
using Zendar.Business.Helpers.NotaFiscal;
using Zendar.Business.Helpers.NotaFiscal.CertificadoA3;
using Zendar.Business.Helpers.SefazWs;
using Zendar.Business.Interfaces;
using Zendar.Business.Interfaces.Services;
using Zendar.Business.Interfaces.Services.Fiscal;
using Zendar.Business.Services.LogGenericoServices;
using Zendar.Business.SignalR;
using Zendar.Business.ViewModels;
using Zendar.Business.ViewModels.Fiscal;
using Zendar.Data.Enums;
using Zendar.Data.Enums.Integracao;
using Zendar.Data.Interfaces.Aplicacao;
using Zendar.Data.Models.Aplicacao;
using Zendar.Data.Resources.Mensagens;
using Zendar.Data.ViewModels;
using Zendar.QueueService.Interfaces;
using Zendar.Shared.Common.Dtos.Request;
using Zendar.Shared.Services.Interfaces;
using ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.Envio;
using ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe;
using ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.Retorno;
using ZendarPackage.NotaFiscal.ClassesXml.ConsultaProtocoloXml;
using ZendarPackage.NotaFiscal.ClassesXml.ConsultaReciboXml;
using ZendarPackage.NotaFiscal.ClassesXml.EventoXml.Evento;
using ZendarPackage.NotaFiscal.ClassesXml.ProtocoloXml;
using ZendarPackage.NotaFiscal.Enums;
using ZendarPackage.NotaFiscal.Helpers;
using ZendarPackage.NotaFiscal.Helpers.ManipuladorXml;

//Validar Marco
namespace Zendar.Business.Services.Fiscal
{
    public class SefazAutorizacaoService : BaseService, ISefazAutorizacaoService
    {
        private readonly INotificador _notificador;
        private readonly IDocumentoFiscalRepository _documentoFiscalRepository;
        private readonly IAspNetUserInfo _aspNetUserInfo;
        private readonly IParametrosNotaFiscalPorUfRepository _parametrosNotaFiscalPorUfRepository;
        private readonly IDocumentoFiscalXmlService _documentoFiscalXmlService;
        private readonly ILogAuditoriaService _logAuditoriaService;
        private readonly IServiceBusEnqueueMessage _serviceBusEnqueueMessage;
        private readonly IHubContext<NotificationHubService> _hubContext;
        private readonly INotaFiscalService _notaFiscalService;
        private readonly INotaFiscalRejeicoesRepository _notaFiscalRejeicoesRepository;
        private readonly ISefazHelperService _sefazHelperService;
        private readonly ILojaRepository _lojaRepository;
        private readonly IConversaoDocumentoFiscalService _conversaoDocumentoFiscalService;
        private readonly ISefazValidacaoService _sefazValidacaoService;
        private readonly ISefazWs _sefazWs;
        private readonly IHistoricoEventoService _historicoEventoService;
        private readonly ILogErroService _logErroService;
        private readonly IHttpService _httpService;
        private string _metodoAlterarStatusTransmissao = "alterar-status-transmissao";
        private string _metodoSucessoTransmissaoA3 = "sucesso-transmissao-a3";

        public SefazAutorizacaoService(
            INotificador notificador,
            IDocumentoFiscalRepository documentoFiscalRepository,
            IAspNetUserInfo aspNetUserInfo,
            IParametrosNotaFiscalPorUfRepository parametrosNotaFiscalPorUfRepository,
            IDocumentoFiscalXmlService documentoFiscalXmlService,
            ILogAuditoriaService logAuditoriaService,
            IServiceBusEnqueueMessage serviceBusEnqueueMessage,
            IHubContext<NotificationHubService> hubContext,
            INotaFiscalService notaFiscalService,
            INotaFiscalRejeicoesRepository notaFiscalRejeicoesRepository,
            ISefazHelperService sefazHelperService,
            ILojaRepository lojaRepository,
            IConversaoDocumentoFiscalService conversaoDocumentoFiscalService,
            ISefazValidacaoService sefazValidacaoService,
            ISefazWs sefazWs,
            IHistoricoEventoService historicoEventoService,
            ILogErroService logErroService,
            IHttpService httpService)
            : base(notificador)
        {
            _notificador = notificador;
            _documentoFiscalRepository = documentoFiscalRepository;
            _aspNetUserInfo = aspNetUserInfo;
            _parametrosNotaFiscalPorUfRepository = parametrosNotaFiscalPorUfRepository;
            _documentoFiscalXmlService = documentoFiscalXmlService;
            _logAuditoriaService = logAuditoriaService;
            _serviceBusEnqueueMessage = serviceBusEnqueueMessage;
            _hubContext = hubContext;
            _notaFiscalService = notaFiscalService;
            _notaFiscalRejeicoesRepository = notaFiscalRejeicoesRepository;
            _sefazHelperService = sefazHelperService;
            _lojaRepository = lojaRepository;
            _conversaoDocumentoFiscalService = conversaoDocumentoFiscalService;
            _sefazValidacaoService = sefazValidacaoService;
            _sefazWs = sefazWs;
            _historicoEventoService = historicoEventoService;
            _logErroService = logErroService;
            _httpService = httpService;
        }

        private X509Certificate2 Certificado = null;

        private byte[] ArrayCertificado = null;

        #region [ Métodos Públicos ]

        public async Task EnviarAutorizacaoNotaFiscalEmMassa(
            List<Guid> listaDocumentoFiscalId)
        {
            var contador = 1;

            foreach (var documentoFiscalId in listaDocumentoFiscalId)
            {
                await RealizarNotificacao(documentoFiscalId,
                                          contador.ToString(),
                                          TipoNotificacaoTransacaoNotaFiscal.EMITINDO);

                await EnviarAutorizacaoNotaFiscal(documentoFiscalId, true);

                contador++;
            }
        }

        public async Task<TipoCertificado?> EnviarAutorizacaoNotaFiscal(
            Guid documentoFiscalId,
            bool background = false)
        {
            await EnviarMensagemNotaSignalR(documentoFiscalId, _metodoAlterarStatusTransmissao, ResourceMensagem.NotaFiscalService_EnviarNotaFiscalSefaz);

            var loja = await _lojaRepository.FirstOrDefault(l => l.Id == _aspNetUserInfo.LojaId.Value, l => new Loja
            {
                LojaFiscal = new LojaFiscal
                {
                    TipoCertificado = l.LojaFiscal.TipoCertificado,
                },
                CpfCnpj = l.CpfCnpj
            });

            if (loja == null)
            {
                NotificarAviso(ResourceMensagem.Geral_LojaNaoEncontrada);

                await RealizarNotificacao(documentoFiscalId,
                                          ResourceMensagem.Geral_LojaNaoEncontrada,
                                          TipoNotificacaoTransacaoNotaFiscal.AVISO);

                return null;
            }

            if (loja.LojaFiscal.TipoCertificado == TipoCertificado.A1)
            {
                Certificado = await _sefazHelperService.ObterCertificado(true);
                ArrayCertificado = await _sefazHelperService.ObterArrayCertificado(true);

                if (Certificado == null)
                {
                    await ObterAviso(documentoFiscalId);

                    return null;
                }
            }

            //Preenche o objeto NFeXml com as informações do documento fiscal
            var nfeXml = await PreencherNFeXml(documentoFiscalId);

            if (nfeXml == null)
            {
                await ObterAviso(documentoFiscalId);

                return null;
            }

            //Assina o xml
            var xmlAssinado = await AssinarXml(loja.LojaFiscal.TipoCertificado, nfeXml, "infNFe", documentoFiscalId, loja.CpfCnpj);

            if (xmlAssinado == null)
            {
                if (loja.LojaFiscal.TipoCertificado == TipoCertificado.A3)
                    return loja.LojaFiscal.TipoCertificado;

                await ObterAviso(documentoFiscalId);

                return null;
            }

            //Só continua se for certificado A1, se for A3 para no if acima porque precisa aguardar o retorno do desktop
            //Envia a autorização para o webservice da sefaz
            var xmlRetornoEnvio = await EnviarAutorizacaoWebservice(TipoCertificado.A1, documentoFiscalId, xmlAssinado, background);

            if (xmlRetornoEnvio == null)
            {
                await ObterAviso(documentoFiscalId);

                return null;
            }

            //Se for NFC-e, a sefaz já retorna a autorização da nota, tratamos o protocolo e retorna null porque a transmissão acaba aqui
            //Se for A1: Consulta a autorização na sefaz e retorna a resposta do webservice
            //Se for A3: Dispara a mensagem para o desktop consultar a autorização na sefaz e retorna null porque precisa aguardar o retorno
            var xmlRetornoConsultaAutorizacao = await TratarRetornoAutorizacao(TipoCertificado.A1, documentoFiscalId, xmlRetornoEnvio, background);

            if (xmlRetornoConsultaAutorizacao == null)
            {
                if ((ModeloFiscal)nfeXml.InformacoesNFeXml.Ide.Modelo == ModeloFiscal.NFCe)
                    return loja.LojaFiscal.TipoCertificado;

                await ObterAviso(documentoFiscalId);

                return null;
            }

            await TratarRetornoConsultaAutorizacao(documentoFiscalId, xmlRetornoConsultaAutorizacao);

            return loja.LojaFiscal.TipoCertificado;
        }

        public async Task<TipoCertificado?> ConsultarProtocolo(
            Guid documentoFiscalId)
        {
            var documentoFiscal = await _documentoFiscalRepository.FirstOrDefaultAsNoTracking(d => d.Id == documentoFiscalId, d => new DocumentoFiscal
            {
                ModeloFiscal = d.ModeloFiscal,
                ChaveAcesso = d.ChaveAcesso,
                Loja = new Loja
                {
                    LojaFiscal = new LojaFiscal
                    {
                        TipoAmbienteFiscal = d.Loja.LojaFiscal.TipoAmbienteFiscal,
                        TipoCertificado = d.Loja.LojaFiscal.TipoCertificado,
                        SenhaCertificadoDigital = d.Loja.LojaFiscal.SenhaCertificadoDigital
                    },
                    CpfCnpj = d.Loja.CpfCnpj,
                    Cidade = new Data.Models.Aplicacao.Cidade { Estado = new Data.Models.Aplicacao.Estado { Codigo = d.Loja.Cidade.Estado.Codigo } }
                }
            });

            if (documentoFiscal == null)
            {
                NotificarAviso(ResourceMensagem.NotaFiscalService_NaoEncontrado);

                await RealizarNotificacao(documentoFiscalId,
                                          ResourceMensagem.NotaFiscalService_NaoEncontrado,
                                          TipoNotificacaoTransacaoNotaFiscal.AVISO);

                return null;
            }

            var parametrosPorUf = await _sefazHelperService.ObterParametrosPorUf(documentoFiscal.Loja.Cidade.Estado.Codigo, documentoFiscal.ModeloFiscal);

            if (parametrosPorUf == null)
            {
                await ObterAviso(documentoFiscalId);

                return null;
            }

            var envioXml = new EnvioConsultaProtocoloXml
            {
                ChaveAcesso = documentoFiscal.ChaveAcesso,
                TipoAmbiente = (int)documentoFiscal.Loja.LojaFiscal.TipoAmbienteFiscal,
                Versao = parametrosPorUf.VersaoWebservice
            };

            var xml = ConverterXml.ConverterObjetoParaXmlDocument(envioXml, PacoteLiberacaoNf.NamespaceDeclaration);

            var (url, autorizador) = await _sefazHelperService.ObterUrl(_aspNetUserInfo.LojaId.Value, documentoFiscal.ModeloFiscal, ServicoNotaFiscal.CONSULTA_PROTOCOLO);

            if (string.IsNullOrEmpty(url))
            {
                await ObterAviso(documentoFiscalId);

                return null;
            }

            if (documentoFiscal.Loja.LojaFiscal.TipoCertificado == TipoCertificado.A3)
            {
                var mensagem = new
                {
                    Certificado = "A3",
                    Tipo = "Enviado",
                    Acao = Acao.ConsultarProtocolo,
                    JsonObject = JsonConvert.SerializeObject(new ConsultarProtocoloMessageServiceBus
                    {
                        Id = documentoFiscalId,
                        Xml = xml.OuterXml,
                        Url = url
                    })
                };

                EnviarMensagemDesktop(mensagem);

                await _historicoEventoService.CadastrarLog(new LogGenerico(documentoFiscalId, JsonConvert.SerializeObject(mensagem)));
            }
            else
            {
                if (ArrayCertificado == null)
                {
                    ArrayCertificado = await _sefazHelperService.ObterArrayCertificado(true);

                    if (ArrayCertificado == null)
                    {
                        await ObterAviso(documentoFiscalId);

                        return null;
                    }
                }

                var sefazRequest = new EnvioSefazWs
                {
                    Autorizador = autorizador.ToString(),
                    Url = url,
                    Xml = xml.OuterXml,
                    Certificado = ArrayCertificado,
                    SenhaCertificado = documentoFiscal.Loja.LojaFiscal.SenhaCertificadoDigital,
                    Servico = ServicoNotaFiscal.CONSULTA_PROTOCOLO
                };

                await _historicoEventoService.CadastrarLog(new LogGenerico(documentoFiscalId, JsonConvert.SerializeObject(new
                {
                    Certificado = "A1",
                    Acao = Acao.ConsultarProtocolo,
                    JsonObject = JsonConvert.SerializeObject(sefazRequest)
                })));

                var retornoXml = await _sefazWs.ConsultarProtocolo(sefazRequest);

                await TratarRetornoConsultaProtocolo(retornoXml, documentoFiscalId);
            }

            return documentoFiscal.Loja.LojaFiscal.TipoCertificado;
        }

        public async Task<TipoCertificado?> AlterarStatusRetorno(
            Guid documentoFiscalId)
        {
            var documentoFiscal = await _documentoFiscalRepository.ObterSomenteDocumentoFiscalParaAlteracao(documentoFiscalId);

            if (documentoFiscal == null)
            {
                NotificarAvisoRegistroNaoEncontrado("documento fiscal");
                return null;
            }

            if (documentoFiscal.Status != StatusFiscal.RETORNO_INDISPONIVEL)
            {
                NotificarAviso("O status do documento fiscal não é retorno indisponível");
                return null;
            }

            if (string.IsNullOrEmpty(documentoFiscal.Lote))
            {
                documentoFiscal.Status = StatusFiscal.EM_DIGITACAO;
                await _documentoFiscalRepository.SaveChanges();
                return await EnviarAutorizacaoNotaFiscal(documentoFiscalId);
            }
            else
            {
                documentoFiscal.Status = StatusFiscal.EM_PROCESSAMENTO;
            }

            await _documentoFiscalRepository.SaveChanges();

            return null;
        }

        #region [ Retornos desktop (A3) ]

        public async Task TratarRetornoAssinaturaXmlA3(
            AssinarMessageServiceBus mensagemXmlAssinado)
        {
            await _historicoEventoService.CadastrarLog(new LogGenerico(mensagemXmlAssinado.Id, JsonConvert.SerializeObject(new
            {
                Certificado = "A3",
                Tipo = "Recebido",
                Acao = Acao.AssinarNota,
                JsonObject = JsonConvert.SerializeObject(mensagemXmlAssinado)
            })));

            await EnviarAutorizacaoWebservice(TipoCertificado.A3, mensagemXmlAssinado.Id, mensagemXmlAssinado.Xml, false);
        }

        public async Task TratarRetornoAutorizacaoXmlA3(
            AutorizarMessageServiceBus mensagemRetornoAutorizacao)
        {
            await _historicoEventoService.CadastrarLog(new LogGenerico(mensagemRetornoAutorizacao.Id, JsonConvert.SerializeObject(new
            {
                Certificado = "A3",
                Tipo = "Recebido",
                Acao = Acao.AutorizarNota,
                JsonObject = JsonConvert.SerializeObject(mensagemRetornoAutorizacao)
            })));

            var xmlRetornoEnvio = (RetornoAutorizarXml)ConverterXml.ConverterStringXmlParaObjeto(mensagemRetornoAutorizacao.Xml, typeof(RetornoAutorizarXml));

            await TratarRetornoAutorizacao(TipoCertificado.A3, mensagemRetornoAutorizacao.Id, xmlRetornoEnvio, false);

            if (xmlRetornoEnvio != null &&
                xmlRetornoEnvio.CodigoStatus == CodigoMensagensRetornoSefaz.AutorizacaoLoteProcessado)
                await EnviarMensagemNotaSignalR(mensagemRetornoAutorizacao.Id, _metodoSucessoTransmissaoA3);
        }

        public async Task TratarRetornoConsultaAutorizacaoXmlA3(
            ConsultarReciboMessageServiceBus mensagemConsultaAutorizacao)
        {
            await _historicoEventoService.CadastrarLog(new LogGenerico(mensagemConsultaAutorizacao.Id, JsonConvert.SerializeObject(new
            {
                Certificado = "A3",
                Tipo = "Recebido",
                Acao = Acao.ConsultarRecibo,
                JsonObject = JsonConvert.SerializeObject(mensagemConsultaAutorizacao)
            })));

            var xmlConsultaRecebimento = (RetornoConsultaRecebimentoXml)ConverterXml.ConverterStringXmlParaObjeto(mensagemConsultaAutorizacao.Xml, typeof(RetornoConsultaRecebimentoXml));

            await TratarRetornoConsultaAutorizacao(mensagemConsultaAutorizacao.Id, xmlConsultaRecebimento);

            if (xmlConsultaRecebimento != null &&
                xmlConsultaRecebimento.CodigoStatus == CodigoMensagensRetornoSefaz.AutorizacaoLoteProcessado)
                await EnviarMensagemNotaSignalR(mensagemConsultaAutorizacao.Id, _metodoSucessoTransmissaoA3);
        }

        public async Task TratarRetornoConsultaProtocoloXmlA3(
            ConsultarProtocoloMessageServiceBus mensagemConsultaProtocolo)
        {
            await _historicoEventoService.CadastrarLog(new LogGenerico(mensagemConsultaProtocolo.Id, JsonConvert.SerializeObject(new
            {
                Certificado = "A3",
                Tipo = "Recebido",
                Acao = Acao.ConsultarProtocolo,
                JsonObject = JsonConvert.SerializeObject(mensagemConsultaProtocolo)
            })));

            var retornoXml = (RetornoConsultaProtocoloXml)ConverterXml.ConverterStringXmlParaObjeto(mensagemConsultaProtocolo.Xml, typeof(RetornoConsultaProtocoloXml));

            await TratarRetornoConsultaProtocolo(retornoXml, mensagemConsultaProtocolo.Id);
        }

        #endregion

        #endregion

        #region [ Métodos Privados ]

        private async Task<NFeXml> PreencherNFeXml(
            Guid documentoFiscalId)
        {
            //Valida as informações do documento fiscal
            if (!await _sefazValidacaoService.ValidarInformacoesEmitir(documentoFiscalId))
            {
                await ObterAviso(documentoFiscalId);

                return null;
            }

            //Preenche a numeração e chave de acesso
            if (!await _notaFiscalService.PreencherNumeracaoNotaFiscal(documentoFiscalId))
            {
                NotificarAviso(ResourceMensagem.NotaFiscalService_ErroGerarNumeracao);

                await RealizarNotificacao(documentoFiscalId,
                                          ResourceMensagem.NotaFiscalService_ErroGerarNumeracao,
                                          TipoNotificacaoTransacaoNotaFiscal.AVISO);

                return null;
            }

            //Preenche a chave de acesso
            if (!await _notaFiscalService.PreencherChaveAcessoNotaFiscal(documentoFiscalId))
            {
                NotificarAviso(ResourceMensagem.NotaFiscalService_ErroGerarChaveAcesso);

                await RealizarNotificacao(documentoFiscalId,
                                          ResourceMensagem.NotaFiscalService_ErroGerarChaveAcesso,
                                          TipoNotificacaoTransacaoNotaFiscal.AVISO);

                return null;
            }

            //Preenche um objeto com as informações do documento fiscal
            var informacoesAutorizacao = await _conversaoDocumentoFiscalService.ConverterDocumentoFiscalEmInformacoesAutorizacao(documentoFiscalId);

            if (informacoesAutorizacao == null)
            {
                await ObterAviso(documentoFiscalId);

                return null;
            }

            //Monta o objeto do xml
            return NFeXml.ConverterXml(informacoesAutorizacao);
        }

        private async Task<string> AssinarXml(
            TipoCertificado tipoCertificado,
            object objeto,
            string uri,
            Guid documentoFiscalId,
            string cnpjLoja,
            int count = 0)
        {
            //Se for A1: Assina e retorna o xml assinado 
            if (tipoCertificado == TipoCertificado.A1)
            {
                try
                {
                    await _historicoEventoService.CadastrarLog(new LogGenerico(documentoFiscalId, JsonConvert.SerializeObject(new
                    {
                        Certificado = "A1",
                        Acao = Acao.AssinarNota,
                        Tentativa = count + 1
                    })));

                    var (xmlAssinado, mensagem) = ZendarPackage.NotaFiscal.Helpers.ManipuladorXml.AssinarXml.Assinar(objeto, uri, Certificado);

                    if (xmlAssinado == null)
                    {
                        NotificarAviso(mensagem);

                        await RealizarNotificacao(documentoFiscalId,
                                                  mensagem,
                                                  TipoNotificacaoTransacaoNotaFiscal.AVISO);

                        return null;
                    }

                    return xmlAssinado.OuterXml;
                }
                catch (Exception ex)
                {
                    if (count < 3)
                    {
                        Certificado = await _sefazHelperService.ObterCertificado(true);

                        if (Certificado == null)
                        {
                            await ObterAviso(documentoFiscalId);

                            return null;
                        }

                        return await AssinarXml(tipoCertificado, objeto, uri, documentoFiscalId, cnpjLoja, ++count);
                    }

                    var mensagemErro = $"AUTORIZAR | {ResourceMensagem.NotaFiscalService_ErroAssinarXml} | {ex.Message}";

                    await _logErroService.Inserir(new LogErroInserirViewModel
                    {
                        Erro = mensagemErro,
                        Dados = Certificado.ToString()
                    });

                    var mensagem = $"{ResourceMensagem.NotaFiscalService_ErroAssinarXml} Tente realizar a emissão novamente.";

                    NotificarAviso(mensagem);

                    await RealizarNotificacao(documentoFiscalId,
                                              mensagem,
                                              TipoNotificacaoTransacaoNotaFiscal.AVISO);
                }

                await ObterAviso(documentoFiscalId);

                return null;
            }
            //Se for A3: Dispara a mensagem para o desktop assinar o xml e retorna null porque precisa aguardar o retorno
            else
            {
                await EnviarMensagemNotaSignalR(documentoFiscalId, _metodoAlterarStatusTransmissao, "Aguardando assinatura do XML...");

                var mensagem = new
                {
                    Certificado = "A3",
                    Tipo = "Enviado",
                    Acao = Acao.AssinarNota,
                    JsonObject = JsonConvert.SerializeObject(new AssinarMessageServiceBus
                    {
                        Id = documentoFiscalId,
                        Xml = ConverterXml.ConverterObjetoParaXmlDocument(objeto, PacoteLiberacaoNf.NamespaceDeclaration).OuterXml,
                        RefUri = uri
                    })
                };

                EnviarMensagemDesktop(mensagem);

                await _historicoEventoService.CadastrarLog(new LogGenerico(documentoFiscalId, JsonConvert.SerializeObject(mensagem)));

                await ObterAviso(documentoFiscalId);

                return null;
            }
        }

        private async Task<RetornoAutorizarXml> EnviarAutorizacaoWebservice(
            TipoCertificado tipoCertificado,
            Guid documentoFiscalId,
            string xmlAssinado,
            bool background)
        {
            await EnviarMensagemNotaSignalR(documentoFiscalId, _metodoAlterarStatusTransmissao, "Aguardando a transmissão da nota para a SEFAZ...");

            //Monta o xml para enviar para a sefaz
            var xmlEnvio = await MontarXmlEnvio(documentoFiscalId, xmlAssinado, background);

            if (xmlEnvio == null)
            {
                await ObterAviso(documentoFiscalId);

                return null;
            }

            //Obtém algumas informações do documento fiscal
            var documentoFiscal = await _documentoFiscalRepository.FirstOrDefaultAsNoTracking(d => d.Id == documentoFiscalId, d => new DocumentoFiscal
            {
                ModeloFiscal = d.ModeloFiscal,
                Loja = new Loja
                {
                    CpfCnpj = d.Loja.CpfCnpj,
                    LojaFiscal = new LojaFiscal
                    {
                        SenhaCertificadoDigital = d.Loja.LojaFiscal.SenhaCertificadoDigital
                    }
                },
                LojaId = d.LojaId
            });

            if (documentoFiscal == null)
            {
                NotificarAviso(ResourceMensagem.NotaFiscalService_NaoEncontrado);

                await RealizarNotificacao(documentoFiscalId,
                                          ResourceMensagem.NotaFiscalService_NaoEncontrado,
                                          TipoNotificacaoTransacaoNotaFiscal.AVISO);

                return null;
            }

            //Consulta a url de autorização
            var (url, autorizador) = await _sefazHelperService.ObterUrl(documentoFiscal.LojaId, documentoFiscal.ModeloFiscal, ServicoNotaFiscal.AUTORIZACAO);

            if (string.IsNullOrEmpty(url))
            {
                await ObterAviso(documentoFiscalId);

                return null;
            }

            //Se for A1: Envia o xml para a sefaz e retorna a resposta do webservice
            if (tipoCertificado == TipoCertificado.A1)
            {
                var sefazRequest = new EnvioSefazWs
                {
                    Autorizador = autorizador.ToString(),
                    Url = url,
                    Xml = xmlEnvio,
                    Certificado = ArrayCertificado,
                    SenhaCertificado = documentoFiscal.Loja.LojaFiscal.SenhaCertificadoDigital,
                    Servico = ServicoNotaFiscal.AUTORIZACAO
                };

                var retorno = await _sefazWs.Autorizar(sefazRequest);

                await _historicoEventoService.CadastrarLog(new LogGenerico(documentoFiscalId, JsonConvert.SerializeObject(new
                {
                    Certificado = "A1",
                    Acao = Acao.AutorizarNota,
                    JsonObject = JsonConvert.SerializeObject(sefazRequest)
                })));

                if (retorno == null)
                {
                    await TratarRejeicaoSemRetorno(documentoFiscalId);

                    return null;
                }

                return retorno;
            }
            //Se for A3: Dispara a mensagem para o desktop enviar o xml para a sefaz e retorna null porque precisa aguardar o retorno
            else
            {
                var mensagem = new
                {
                    Certificado = "A3",
                    Tipo = "Enviado",
                    Acao = Acao.AutorizarNota,
                    JsonObject = JsonConvert.SerializeObject(new AutorizarMessageServiceBus
                    {
                        Id = documentoFiscalId,
                        Xml = xmlEnvio,
                        Url = url
                    })
                };

                EnviarMensagemDesktop(mensagem);

                await _historicoEventoService.CadastrarLog(new LogGenerico(documentoFiscalId, JsonConvert.SerializeObject(mensagem)));

                // Alterar o status para retorno indisponível
                await AlterarDocumentoFiscalParaRetornoIndisponivel(documentoFiscalId);

                return null;
            }
        }

        private async Task<RetornoConsultaRecebimentoXml> ConsultarAutorizacao(
            TipoCertificado tipoCertificado,
            Guid documentoFiscalId,
            bool background)
        {
            await EnviarMensagemNotaSignalR(documentoFiscalId, _metodoAlterarStatusTransmissao, ResourceMensagem.NotaFiscalService_VerificandoSituacaoNotaFiscalSefaz);

            var xmlConsultaAutorizacao = await MontarXmlConsultaAutorizacao(documentoFiscalId, background);

            if (xmlConsultaAutorizacao == null)
            {
                await ObterAviso(documentoFiscalId);

                return null;
            }

            var documentoFiscal = await _documentoFiscalRepository.FirstOrDefaultAsNoTracking(d => d.Id == documentoFiscalId, d => new DocumentoFiscal
            {
                ModeloFiscal = d.ModeloFiscal,
                Loja = new Loja
                {
                    CpfCnpj = d.Loja.CpfCnpj,
                    LojaFiscal = new LojaFiscal
                    {
                        SenhaCertificadoDigital = d.Loja.LojaFiscal.SenhaCertificadoDigital
                    }
                },
                LojaId = d.LojaId
            });

            if (documentoFiscal == null)
            {
                NotificarAviso(ResourceMensagem.NotaFiscalService_NaoEncontrado);

                await RealizarNotificacao(documentoFiscalId,
                                          ResourceMensagem.NotaFiscalService_NaoEncontrado,
                                          TipoNotificacaoTransacaoNotaFiscal.AVISO);

                return null;
            }

            var (url, autorizador) = await _sefazHelperService.ObterUrl(documentoFiscal.LojaId, documentoFiscal.ModeloFiscal, ServicoNotaFiscal.RET_AUTORIZACAO);

            if (string.IsNullOrEmpty(url))
            {
                await ObterAviso(documentoFiscalId);

                return null;
            }

            if (tipoCertificado == TipoCertificado.A1)
            {
                var sefazRequest = new EnvioSefazWs
                {
                    Autorizador = autorizador.ToString(),
                    Url = url,
                    Xml = xmlConsultaAutorizacao,
                    Certificado = ArrayCertificado,
                    SenhaCertificado = documentoFiscal.Loja.LojaFiscal.SenhaCertificadoDigital,
                    Servico = ServicoNotaFiscal.RET_AUTORIZACAO
                };

                await _historicoEventoService.CadastrarLog(new LogGenerico(documentoFiscalId, JsonConvert.SerializeObject(new
                {
                    Certificado = "A1",
                    Acao = Acao.ConsultarRecibo,
                    JsonObject = JsonConvert.SerializeObject(sefazRequest)
                })));

                return await _sefazWs.ConsultarAutorizacao(sefazRequest);
            }
            else
            {
                var mensagem = new
                {
                    Certificado = "A3",
                    Tipo = "Enviado",
                    Acao = Acao.ConsultarRecibo,
                    JsonObject = JsonConvert.SerializeObject(new AutorizarMessageServiceBus
                    {
                        Id = documentoFiscalId,
                        Xml = xmlConsultaAutorizacao,
                        Url = url
                    })
                };

                EnviarMensagemDesktop(mensagem);

                await _historicoEventoService.CadastrarLog(new LogGenerico(documentoFiscalId, JsonConvert.SerializeObject(mensagem)));

                return null;
            }
        }

        private async Task<string> MontarXmlEnvio(
            Guid documentoFiscalId,
            string xmlNfeAssinado,
            bool background)
        {
            //Vincula o xml assinado no documento fiscal
            await _documentoFiscalXmlService.VincularXml(new XmlFiscalViewModel
            {
                Arquivo = xmlNfeAssinado,
                Tipo = TipoXml.AUTORIZAR
            }, documentoFiscalId);

            //Obtem as informações necessárias para validar o schema e enviar para autorização
            var (documentoFiscal, parametrosPorUf) = await ObterInformacoesParaEnviarAutorizacao(documentoFiscalId);

            if (documentoFiscal == null)
            {
                NotificarAviso(ResourceMensagem.NotaFiscalService_NaoEncontrado);

                await RealizarNotificacao(documentoFiscalId,
                                          ResourceMensagem.NotaFiscalService_NaoEncontrado,
                                          TipoNotificacaoTransacaoNotaFiscal.AVISO);

                return null;
            }

            //Valida o schema do xml assinado
            if (!await ValidarSchemaXml(documentoFiscalId,
                                        xmlNfeAssinado,
                                        ServicoNotaFiscal.PRE_AUTORIZACAO,
                                        parametrosPorUf.PacoteLiberacao.ToString(),
                                        parametrosPorUf.VersaoWebservice,
                                        background))
            {
                await ObterAviso(documentoFiscalId);

                return null;
            }

            //Cria o xml do envio
            var envioXml = new EnvioAutorizarXml
            {
                Versao = parametrosPorUf.VersaoWebservice,
                NumeroLote = "000000000000001",
                IndicadorSincrono = 1 
            };

            var nfeAssinada = ConverterXml.ConverterStringXmlParaObjeto(xmlNfeAssinado, typeof(NFeXml));
            var nodeNfeAssinado = ConverterXml.ConverterObjetoParaXmlDocument(nfeAssinada, PacoteLiberacaoNf.NamespaceDeclaration).DocumentElement;

            var xmlAutorizacaoEnvio = ConverterXml.ConverterObjetoParaXmlDocument(envioXml, PacoteLiberacaoNf.NamespaceDeclaration);
            var nodeToMove = xmlAutorizacaoEnvio.ImportNode(nodeNfeAssinado, true);
            xmlAutorizacaoEnvio.DocumentElement.AppendChild(nodeToMove);

            //Valida o schema do xml de envio
            if (!await ValidarSchemaXml(documentoFiscalId,
                                        xmlAutorizacaoEnvio.OuterXml,
                                        ServicoNotaFiscal.AUTORIZACAO,
                                        parametrosPorUf.PacoteLiberacao.ToString(),
                                        parametrosPorUf.VersaoWebservice,
                                        background))
            {
                await ObterAviso(documentoFiscalId);

                return null;
            }

            return xmlAutorizacaoEnvio.OuterXml;
        }

        private async Task<string> MontarXmlConsultaAutorizacao(
            Guid documentoFiscalId,
            bool background)
        {
            var documentoFiscal = await _documentoFiscalRepository.FirstOrDefaultAsNoTracking(d => d.Id == documentoFiscalId, d => new DocumentoFiscal
            {
                Lote = d.Lote,
                ModeloFiscal = d.ModeloFiscal,
                Loja = new Loja
                {
                    LojaFiscal = new LojaFiscal
                    {
                        TipoAmbienteFiscal = d.Loja.LojaFiscal.TipoAmbienteFiscal,
                    },
                    Cidade = new Data.Models.Aplicacao.Cidade { Estado = new Data.Models.Aplicacao.Estado { Codigo = d.Loja.Cidade.Estado.Codigo } }
                }
            });

            if (documentoFiscal == null)
            {
                NotificarAviso(ResourceMensagem.NotaFiscalService_NaoEncontrado);

                await RealizarNotificacao(documentoFiscalId,
                                          ResourceMensagem.NotaFiscalService_NaoEncontrado,
                                          TipoNotificacaoTransacaoNotaFiscal.AVISO);

                return null;
            }

            var parametrosPorUf = await _sefazHelperService.ObterParametrosPorUf(documentoFiscal.Loja.Cidade.Estado.Codigo, documentoFiscal.ModeloFiscal);

            if (parametrosPorUf == null)
            {
                await ObterAviso(documentoFiscalId);

                return null;
            }

            var envioConsultarRecebimentoXml = new EnvioConsultaRecebimentoXml
            {
                Versao = parametrosPorUf.VersaoWebservice,
                TipoAmbiente = (int)documentoFiscal.Loja.LojaFiscal.TipoAmbienteFiscal,
                NumeroRecibo = documentoFiscal.Lote
            };

            var xml = ConverterXml.ConverterObjetoParaXmlDocument(envioConsultarRecebimentoXml, PacoteLiberacaoNf.NamespaceDeclaration);

            if (!await ValidarSchemaXml(documentoFiscalId,
                                        xml.OuterXml,
                                        ServicoNotaFiscal.RET_AUTORIZACAO,
                                        parametrosPorUf.PacoteLiberacao.ToString(),
                                        parametrosPorUf.VersaoWebservice,
                                        background))
            {
                await ObterAviso(documentoFiscalId);

                return null;
            }

            return xml.OuterXml;
        }

        private async Task<(DocumentoFiscal, ParametrosNotaFiscalPorUf)> ObterInformacoesParaEnviarAutorizacao(
           Guid documentoFiscalId)
        {
            var documentoFiscal = await _documentoFiscalRepository.FirstOrDefaultAsNoTracking(d => d.Id == documentoFiscalId, d => new DocumentoFiscal
            {
                Id = d.Id,
                ModeloFiscal = d.ModeloFiscal,
                DocumentoFiscalParticipantes = d.DocumentoFiscalParticipantes.Select(p => new DocumentoFiscalParticipantes
                {
                    Identificacao = p.Identificacao,
                    CodEstado = p.CodEstado
                }).ToList(),
                LojaId = d.LojaId,
                Loja = new Loja
                {
                    LojaFiscal = new LojaFiscal
                    {
                        TipoCertificado = d.Loja.LojaFiscal.TipoCertificado,
                    },
                    CpfCnpj = d.Loja.CpfCnpj
                }
            });

            var codEstadoEmitente = documentoFiscal.DocumentoFiscalParticipantes.FirstOrDefault(d => d.Identificacao == IdentificacaoParticipantes.EMITENTE).CodEstado;

            var modeloMultiempresa = (Multiempresa.Data.Enum.ModeloFiscal)documentoFiscal.ModeloFiscal;

            var parametrosPorUf = await _parametrosNotaFiscalPorUfRepository.FirstOrDefaultAsNoTracking(p => p.CodigoEstado == Convert.ToInt32(codEstadoEmitente) &&
                                                                                                             p.ModeloFiscal == modeloMultiempresa,
            p => new ParametrosNotaFiscalPorUf
            {
                VersaoWebservice = p.VersaoWebservice,
                PacoteLiberacao = p.PacoteLiberacao,
                PacoteLiberacaoEvento = p.PacoteLiberacaoEvento
            });

            return (documentoFiscal, parametrosPorUf);
        }

        #region [ Alterar situação da nota fiscal ]

        private async Task AlterarDocumentoFiscalParaEmProcessamento(
            Guid documentoFiscalId,
            string lote)
        {
            var documentoFiscal = await _documentoFiscalRepository.FindByKey(documentoFiscalId);
            documentoFiscal.Status = StatusFiscal.EM_PROCESSAMENTO;
            documentoFiscal.Lote = lote;

            await _documentoFiscalRepository.SaveChanges();

            await _historicoEventoService.CadastrarLog(
                new LogGenerico(documentoFiscalId, JsonConvert.SerializeObject(new
                {
                    Status = documentoFiscal.Status,
                    Lote = documentoFiscal.Lote,
                    Protocolo = documentoFiscal.NumeroProtocolo,
                    DataProtocolo = documentoFiscal.DataProtocolo,
                    Mensagem = documentoFiscal.Mensagem,
                })));
        }

        private async Task AlterarDocumentoFiscalParaAutorizado(
            Guid documentoFiscalId,
            RetornoProtocoloXml protocoloXml)
        {
            //Substitui o xml vinculado no documento fiscal
            var xml = await _documentoFiscalXmlService.ObterXml(documentoFiscalId, TipoXml.AUTORIZAR);

            if (string.IsNullOrEmpty(xml))
            {
                NotificarAviso(ResourceMensagem.NotaFiscalService_ErroObterXml);

                await RealizarNotificacao(documentoFiscalId,
                                          ResourceMensagem.NotaFiscalService_ErroObterXml,
                                          TipoNotificacaoTransacaoNotaFiscal.AVISO);

                return;
            }

            var notaProcessadaXml = new NFeProcessadaXml
            {
                Versao = protocoloXml.Versao,
                NFe = (NFeXml)ConverterXml.ConverterStringXmlParaObjeto(xml, typeof(NFeXml)),
                ProtocoloXml = protocoloXml
            };

            //Altera o status do documento fiscal
            var documentoFiscal = await _documentoFiscalRepository.FindByKey(documentoFiscalId);
            documentoFiscal.Mensagem = protocoloXml.InformacoesProtocoloXml.Resposta;
            documentoFiscal.Status = StatusFiscal.AUTORIZADA;
            documentoFiscal.NumeroProtocolo = protocoloXml.InformacoesProtocoloXml.NumeroProtocolo;
            documentoFiscal.DataProtocolo = Convert.ToDateTime(protocoloXml.InformacoesProtocoloXml.DataHoraRecebimento.Replace("T", " "));
            documentoFiscal.LinkBaseConhecimento = null;
            documentoFiscal.TipoRejeicao = TipoRejeicaoNotaFiscal.SEFAZ;

            await _documentoFiscalRepository.SaveChanges();

            await _historicoEventoService.CadastrarLog(
                new LogGenerico(documentoFiscalId, JsonConvert.SerializeObject(new
                {
                    Status = documentoFiscal.Status,
                    Lote = documentoFiscal.Lote,
                    Protocolo = documentoFiscal.NumeroProtocolo,
                    DataProtocolo = documentoFiscal.DataProtocolo,
                    Mensagem = documentoFiscal.Mensagem,
                    XMl = protocoloXml
                })));

            await _documentoFiscalXmlService.VincularXml(
                new XmlFiscalViewModel
                {
                    Arquivo = ConverterXml.ConverterObjetoParaXmlDocument(notaProcessadaXml, PacoteLiberacaoNf.NamespaceDeclaration).OuterXml,
                    Tipo = TipoXml.AUTORIZAR
                }, documentoFiscalId);

            await _logAuditoriaService.Inserir(new LogAuditoriaInserirViewModel(LogAuditoriaTela.NOTA_FISCAL, LogAuditoriaOperacao.AUTORIZAR_NOTA_FISCAL, $"Número: {documentoFiscal.Numero}"));

            await RealizarNotificacao(documentoFiscal.Id,
                                      string.Empty,
                                      TipoNotificacaoTransacaoNotaFiscal.NOTA_FISCAL,
                                      StatusFiscal.AUTORIZADA);

            try
            {
                if (documentoFiscal.ModeloFiscal == ZendarPackage.NotaFiscal.Enums.ModeloFiscal.NFe)
                    await _notaFiscalService.EnviarEmailNotaFiscal(documentoFiscal.Id);
            }
            catch { }
        }

        private async Task AlterarDocumentoFiscalParaUsoDenegado(
            Guid documentoFiscalId,
            string mensagem)
        {
            var documentoFiscal = await _documentoFiscalRepository.FindByKey(documentoFiscalId);
            documentoFiscal.Status = StatusFiscal.USO_DENEGADO;
            documentoFiscal.Lote = mensagem;

            await _historicoEventoService.CadastrarLog(
                new LogGenerico(documentoFiscalId, JsonConvert.SerializeObject(new
                {
                    Status = documentoFiscal.Status,
                    Lote = documentoFiscal.Lote,
                    Protocolo = documentoFiscal.NumeroProtocolo,
                    DataProtocolo = documentoFiscal.DataProtocolo,
                    Mensagem = mensagem,
                })));

            await _documentoFiscalRepository.SaveChanges();

            await RealizarNotificacao(documentoFiscal.Id,
                                      mensagem,
                                      TipoNotificacaoTransacaoNotaFiscal.NOTA_FISCAL,
                                      StatusFiscal.USO_DENEGADO);
        }

        private async Task AlterarDocumentoFiscalParaCancelado(
            Guid documentoFiscalId,
            EventoProcessadoXml eventoProcessadoXml)
        {
            await _documentoFiscalXmlService.VincularXml(new XmlFiscalViewModel
            {
                Arquivo = ConverterXml.ConverterObjetoParaXmlDocument(eventoProcessadoXml, PacoteLiberacaoNf.NamespaceDeclaration).OuterXml,
                Tipo = TipoXml.CANCELAR
            }, documentoFiscalId);

            var documentoFiscal = await _documentoFiscalRepository.FindByKey(documentoFiscalId);
            documentoFiscal.Status = StatusFiscal.CANCELADA;
            documentoFiscal.Motivo = eventoProcessadoXml.EnvioEvento.InformacoesEventoXml.DetalhesEventoXml.Justificativa;
            documentoFiscal.SequenciaEvento = eventoProcessadoXml.EnvioEvento.InformacoesEventoXml.NumeroSequencial;

            await _documentoFiscalRepository.SaveChanges();

            await _historicoEventoService.CadastrarLog(
                new LogGenerico(documentoFiscalId, JsonConvert.SerializeObject(new
                {
                    Status = documentoFiscal.Status,
                    Lote = documentoFiscal.Lote,
                    Protocolo = documentoFiscal.NumeroProtocolo,
                    DataProtocolo = documentoFiscal.DataProtocolo,
                    Mensagem = documentoFiscal.Mensagem,
                    Motivo = documentoFiscal.Motivo,
                    XML = eventoProcessadoXml
                })));

            await _logAuditoriaService
                  .Inserir(new LogAuditoriaInserirViewModel(LogAuditoriaTela.NOTA_FISCAL,
                                                            LogAuditoriaOperacao.CANCELAR_NOTA_FISCAL,
                                                            $"Número: {documentoFiscal.Numero} Motivo: {documentoFiscal.Motivo}"));

            await RealizarNotificacao(documentoFiscal.Id,
                          documentoFiscal.Mensagem,
                          TipoNotificacaoTransacaoNotaFiscal.NOTA_FISCAL,
                          StatusFiscal.CANCELADA);
        }

        private async Task AlterarDocumentoFiscalParaRetornoIndisponivel(
            Guid documentoFiscalId)
        {
            var documentoFiscal = await _documentoFiscalRepository.FindByKey(documentoFiscalId);
            documentoFiscal.Status = StatusFiscal.RETORNO_INDISPONIVEL;
            documentoFiscal.Mensagem = ResourceMensagem.NotaFiscalService_SemRetornoSefaz;

            await _documentoFiscalRepository.SaveChanges();

            await _historicoEventoService.CadastrarLog(
                new LogGenerico(documentoFiscalId, JsonConvert.SerializeObject(new
                {
                    Status = documentoFiscal.Status,
                    Lote = documentoFiscal.Lote,
                    Protocolo = documentoFiscal.NumeroProtocolo,
                    DataProtocolo = documentoFiscal.DataProtocolo,
                    Mensagem = documentoFiscal.Mensagem,
                })));

            await RealizarNotificacao(documentoFiscal.Id,
                                      documentoFiscal.Mensagem,
                                      TipoNotificacaoTransacaoNotaFiscal.NOTA_FISCAL,
                                      StatusFiscal.RETORNO_INDISPONIVEL);
        }

        #endregion

        #region [ Mensagens ]

        private async Task EnviarMensagemNotaSignalR(
            Guid documentoFiscalId,
            string metodo,
            string mensagem = null)
        {
            if (_hubContext != null)
            {
                if (!string.IsNullOrEmpty(mensagem))
                    await _hubContext.Clients.Group($"{documentoFiscalId}_transmitindo-nota").SendAsync(metodo, mensagem);
                else
                    await _hubContext.Clients.Group($"{documentoFiscalId}_transmitindo-nota").SendAsync(metodo);
            }
        }

        private void EnviarMensagemDesktop(
            object mensagem)
        {
            // Fazer split na string para quando for localhost
            _serviceBusEnqueueMessage.Send($"{_aspNetUserInfo.HostUrl.Split(":")[0]}-{_aspNetUserInfo.LojaId}-assinar-xml", JsonConvert.SerializeObject(mensagem));
        }

        #endregion

        #region [ Validações ]

        private async Task<bool> ValidarSchemaXml(
            Guid documentoFiscalId,
            string xml,
            ServicoNotaFiscal servico,
            string pacoteLiberacao,
            string versaoWebservice,
            bool background)
        {
            if (background)
            {
                var (sucessoValidacao, mensagemValidacao) = ValidarXmlBackground.Validar(xml, servico, pacoteLiberacao, versaoWebservice);

                if (!sucessoValidacao)
                    await TratarRejeicaoXSD(documentoFiscalId, mensagemValidacao);

                return sucessoValidacao;
            }
            else
            {
                var (sucessoValidacao, mensagemValidacao) = ValidarXml.Validar(xml, servico, pacoteLiberacao, versaoWebservice);

                if (!sucessoValidacao)
                    await TratarRejeicaoXSD(documentoFiscalId, mensagemValidacao);

                return sucessoValidacao;

            }
        }

        #endregion

        #region [ Tratamentos ]

        private async Task<RetornoConsultaRecebimentoXml> TratarRetornoAutorizacao(
            TipoCertificado tipoCertificado,
            Guid documentoFiscalId,
            RetornoAutorizarXml retornoXml,
            bool background)
        {
            if (retornoXml != null)
            {
                //Quando transmite a NFCe a SEFAZ já retorna a autorização da nota e não precisa consultar a autorização, tratamos o protocolo e a transmissão acaba aqui
                if (retornoXml.CodigoStatus == CodigoMensagensRetornoSefaz.AutorizacaoLoteProcessado)
                {
                    await TratarProtocolo(documentoFiscalId, retornoXml.ProtocoloXml);

                    return null;
                }

                //Se a sefaz retornar que o lote foi recebido, altera o status para em processamento e consulta no webservice a situação da nota
                //Se for A1: Consulta a autorização na sefaz e retorna a resposta do webservice
                //Se for A3: Dispara a mensagem para o desktop consultar a autorização na sefaz e retorna null porque precisa aguardar o retorno
                if (retornoXml.CodigoStatus == CodigoMensagensRetornoSefaz.AutorizacaoLoteRecebidoComSucesso)
                {
                    await AlterarDocumentoFiscalParaEmProcessamento(documentoFiscalId, retornoXml.ReciboXml.NumeroRecibo);

                    Thread.Sleep(TimeSpan.FromSeconds(3));

                    return await ConsultarAutorizacao(tipoCertificado, documentoFiscalId, background);
                }
                else
                {
                    //Rejeições da sefaz
                    if (retornoXml.ProtocoloXml != null)
                        await TratarRejeicaoSefaz(documentoFiscalId,
                                                  retornoXml.ProtocoloXml.InformacoesProtocoloXml.CodigoStatus,
                                                  retornoXml.ProtocoloXml.InformacoesProtocoloXml.Resposta);
                    else
                        await TratarRejeicaoSefaz(documentoFiscalId,
                                                  retornoXml.CodigoStatus,
                                                  retornoXml.Motivo);
                }
            }
            else
                await TratarRejeicaoSemRetorno(documentoFiscalId);

            return null;
        }

        private async Task TratarRetornoConsultaAutorizacao(
            Guid documentoFiscalId,
            RetornoConsultaRecebimentoXml retornoXml)
        {
            await EnviarMensagemNotaSignalR(documentoFiscalId, _metodoAlterarStatusTransmissao, ResourceMensagem.NotaFiscalService_FinalizandoTransmissao);

            if (retornoXml != null)
            {
                if (retornoXml.CodigoStatus == CodigoMensagensRetornoSefaz.AutorizacaoLoteProcessado)
                    await TratarProtocolo(documentoFiscalId, retornoXml.ProtocoloXml.First());
                else
                    await TratarRejeicaoSefaz(documentoFiscalId, retornoXml.CodigoStatus, retornoXml.Motivo);
            }
            else
                await TratarRejeicaoSemRetorno(documentoFiscalId);
        }

        private async Task TratarProtocolo(
            Guid documentoFiscalId,
            RetornoProtocoloXml protocoloXml)
        {
            if (protocoloXml.InformacoesProtocoloXml.CodigoStatus == MsgRetornoSefaz.AutorizacaoNotaAutorizada ||
                protocoloXml.InformacoesProtocoloXml.CodigoStatus == MsgRetornoSefaz.AutorizacaoNotaAutorizadaForaDoPrazo)
                await AlterarDocumentoFiscalParaAutorizado(documentoFiscalId, protocoloXml);
            else if (protocoloXml.InformacoesProtocoloXml.CodigoStatus == MsgRetornoSefaz.AutorizacaoDuplicidadeNfe)
                await ConsultarProtocolo(documentoFiscalId);
            else if (protocoloXml.InformacoesProtocoloXml.CodigoStatus == MsgRetornoSefaz.AutorizacaoUsoDenegado)
                await AlterarDocumentoFiscalParaUsoDenegado(documentoFiscalId, protocoloXml.InformacoesProtocoloXml.Resposta);
            else
                await TratarRejeicaoSefaz(documentoFiscalId, protocoloXml.InformacoesProtocoloXml.CodigoStatus, protocoloXml.InformacoesProtocoloXml.Resposta);
        }

        private async Task TratarRetornoConsultaProtocolo(
            RetornoConsultaProtocoloXml retornoXml,
            Guid documentoFiscalId)
        {
            if (retornoXml == null)
                await TratarRejeicaoSemRetorno(documentoFiscalId);
            else
            {
                if (retornoXml.ProtocoloCancelamentoXml != null)
                    await AlterarDocumentoFiscalParaCancelado(documentoFiscalId, retornoXml.EventoProcessadoXml);
                else if (retornoXml.ProtocoloXml != null)
                    await TratarProtocolo(documentoFiscalId, retornoXml.ProtocoloXml);
                else
                    await TratarRejeicaoSefaz(documentoFiscalId, retornoXml.CodigoStatus, retornoXml.Motivo);
            }
        }

        #region [ Rejeições ]

        private async Task TratarRejeicaoXSD(
            Guid documentoFiscalId,
            string mensagemValidacao)
        {
            var documentoFiscal = await _documentoFiscalRepository.FindByKey(documentoFiscalId);

            documentoFiscal.Status = StatusFiscal.REJEITADA;
            documentoFiscal.TipoRejeicao = TipoRejeicaoNotaFiscal.XSD;
            documentoFiscal.Mensagem = mensagemValidacao;
            documentoFiscal.LinkBaseConhecimento = null;

            await _documentoFiscalRepository.SaveChanges();

            await TratarMensagemRejeicao(documentoFiscal);
        }

        private async Task TratarRejeicaoSefaz(
            Guid documentoFiscalId,
            int codigo,
            string resposta)
        {
            var documentoFiscal = await _documentoFiscalRepository.FindByKey(documentoFiscalId);

            documentoFiscal.Status = StatusFiscal.REJEITADA;
            documentoFiscal.TipoRejeicao = TipoRejeicaoNotaFiscal.SEFAZ;
            documentoFiscal.Mensagem = resposta;
            documentoFiscal.LinkBaseConhecimento = await _notaFiscalRejeicoesRepository.ObterLink(codigo);

            await _documentoFiscalRepository.SaveChanges();

            await TratarMensagemRejeicao(documentoFiscal);
        }

        /// <summary>
        /// Tratamento de rejeição para quando não há retorno da SEFAZ
        /// </summary>
        private async Task TratarRejeicaoSemRetorno(
            Guid documentoFiscalId)
        {
            var documentoFiscal = await _documentoFiscalRepository.FindByKey(documentoFiscalId);

            documentoFiscal.Status = StatusFiscal.RETORNO_INDISPONIVEL;
            documentoFiscal.TipoRejeicao = TipoRejeicaoNotaFiscal.SEFAZ;
            documentoFiscal.LinkBaseConhecimento = null;
            documentoFiscal.Mensagem = ResourceMensagem.NotaFiscalService_SemRetornoSefaz;

            await _documentoFiscalRepository.SaveChanges();

            await TratarMensagemRejeicao(documentoFiscal);
        }

        /// <summary>
        /// Método genérico para o tratamento de rejeições do fluxo
        /// de autorização da nota fiscal
        /// </summary>
        private async Task TratarMensagemRejeicao(
            DocumentoFiscal documentoFiscal)
        {
            // Objeto para a exibição das rejeições futuramente em tela.
            var detalhesRejeicao = new RejeicaoNotaFiscalViewModel
            {
                LinkBaseConhecimento = documentoFiscal.LinkBaseConhecimento,
                Mensagem = documentoFiscal.Mensagem,
                TipoRejeicao = documentoFiscal.TipoRejeicao
            };

            // Gravar log
            await _historicoEventoService.CadastrarLog(new LogGenerico(documentoFiscal.Id, JsonConvert.SerializeObject(new
            {
                Mensagem = documentoFiscal.Mensagem,
                Status = documentoFiscal.Status,
                TipoRejeicao = documentoFiscal.TipoRejeicao
            })));

            NotificarAviso(detalhesRejeicao.Mensagem);

            await RealizarNotificacao(documentoFiscal.Id,
                                      detalhesRejeicao.Mensagem,
                                      TipoNotificacaoTransacaoNotaFiscal.NOTA_FISCAL,
                                      StatusFiscal.REJEITADA);
        }

        #endregion

        #endregion

        #region [ Notificacoes ]

        private async Task ObterAviso(
            Guid documentoFiscalId)
        {
            var mensagem = _notificador?.ObterNotificacoesAviso()
                                       ?.Select(x => x.Mensagem)
                                       ?.FirstOrDefault();

            if (!string.IsNullOrEmpty(mensagem))
                await RealizarNotificacao(documentoFiscalId,
                                          mensagem,
                                          TipoNotificacaoTransacaoNotaFiscal.AVISO);
        }

        private async Task RealizarNotificacao(
            Guid documentoFiscalId,
            string mensagem,
            TipoNotificacaoTransacaoNotaFiscal tipoNotificacaoTransacaoNotaFiscal,
            StatusFiscal statusFiscal = StatusFiscal.EM_PROCESSAMENTO)
        {
            if (_aspNetUserInfo.Trigger) return;

            try
            {
                var zendarUrl = Environment.GetEnvironmentVariable(SystemConst.ZENDAR_URL);
                _httpService.AddHeader("HTTP_REFERER_MULTIEMPRESA", _aspNetUserInfo.HostUrl);
                _httpService.AddHeader("lojaid", _aspNetUserInfo.LojaId.ToString());

                string url = string.Empty;

                if (tipoNotificacaoTransacaoNotaFiscal == TipoNotificacaoTransacaoNotaFiscal.EMITINDO)
                    url = $"{zendarUrl}/integracao/notificar-nota-fiscal-emitindo";
                else if (tipoNotificacaoTransacaoNotaFiscal == TipoNotificacaoTransacaoNotaFiscal.AVISO)
                    url = $"{zendarUrl}/integracao/notificar-nota-fiscal-aviso";
                else
                    url = $"{zendarUrl}/integracao/notificar-nota-fiscal-status";

                var notificacaoRequest = new NotificacaoRequest();
                notificacaoRequest.UsuarioId = new Guid(_aspNetUserInfo.Id);
                notificacaoRequest.Mensagem = mensagem;
                notificacaoRequest.Id = documentoFiscalId;
                notificacaoRequest.Tipo = (int)Tipo.NOTA_FISCAL;
                notificacaoRequest.Status = statusFiscal;

                await _httpService.PostAsync<Guid?>(url, notificacaoRequest);
            }
            catch (Exception ex)
            {
                await _logErroService.Inserir(new LogErroInserirViewModel { Erro = ex.Message, Dados = "Erro ao enviar notificação." });
            }
        }

        #endregion

        #endregion
    }
}
