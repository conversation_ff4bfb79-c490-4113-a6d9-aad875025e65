﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace Zendar.Data.Migrations.v3_8
{
    public partial class AddPermissaoVendaTotalizadaVendedoresFrenteCaixa : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql(@"

            INSERT INTO [dbo].[Permissao] 
            VALUES 
                ('F3A9A5C2-73E8-4E59-8B3E-6A1C56A18217', 'relatorio_vendas_totalizadas_vendedores_grafico_frente_caixa', 'Vendas totalizadas por vendedores (Frente de caixa)',  null, 13, 'DB82B609-C763-4A6E-8AD3-21314954B0BD');

            INSERT INTO [dbo].[PlanoPermissao]
            VALUES 
                ('F3A9A5C2-73E8-4E59-8B3E-6A1C56A18217', 'PLANO_PRO');");

            migrationBuilder.Sql(@"EXEC [dbo].[VINCULAR_PERMISSOES_USER_ADM];
                       EXEC [dbo].[VINCULAR_PERMISSOES_PLANO_PRIME];");

        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {

        }
    }
}
