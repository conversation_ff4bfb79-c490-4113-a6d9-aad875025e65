using Microsoft.EntityFrameworkCore;
using Multiempresa.Shared.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Linq.Expressions;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Zendar.Data.Contexts;
using Zendar.Data.Enums;
using Zendar.Data.Enums.Integracao;
using Zendar.Data.Extensions;
using Zendar.Data.Helpers;
using Zendar.Data.Interfaces.Aplicacao;
using Zendar.Data.Models.Aplicacao;
using Zendar.Data.Models.FichaTecnica;
using Zendar.Data.Models.GerenciadorDeImpressao;
using Zendar.Data.Models.ProdutoPorEtapa;
using Zendar.Data.ViewModels;
using ZendarPackage.NotaFiscal.Enums;

namespace Zendar.Data.Repository.Aplicacao
{
    public class ProdutoRepository : RepositoryAplicacao<Produto>, IProdutoRepository
    {
        public ProdutoRepository(AplicacaoContexto context) : base(context) { }

        public async Task<Produto> SingleOrDefault(
            Expression<Func<Produto, bool>> predicate = null,
            Expression<Func<Produto, Produto>> selector = null)
        {
            var query = DbSet.AsQueryable();

            if (predicate is not null)
                query = query.Where(predicate);

            if (selector is not null)
            {
                query = query.AsNoTracking()
                            .Select(selector);
            }

            return await query.SingleOrDefaultAsync();
        }

        public GridPaginadaRetorno<ProdutoPaginadoViewModel> ListarPaginado
            (GridPaginadaConsulta gridPaginada,
            ProdutoFiltrosViewModel produtoFiltrosViewModel,
            IEnumerable<Guid> produtosIds,
            Guid lojaId)
        {
            var gridPaginadaRetorno = new GridPaginadaRetorno<ProdutoPaginadoViewModel>();

            var produtos = FiltrarCamposListagemProduto(produtoFiltrosViewModel, produtosIds);

            var query = produtos
                   .OrderBy($"{gridPaginada.CampoOrdenacao} {gridPaginada.DirecaoOrdenacao}")
                   .Select(ProdutoPaginadoViewModel.Selector(lojaId));

            gridPaginadaRetorno.CarregarPaginacao(query, gridPaginada);

            return gridPaginadaRetorno;
        }

        public async Task<Guid[]> ObterIdsProdutosFiltradosAlteracaoEmMassa(Guid[] marcasIds, Guid[] categoriasIds, StatusConsulta status)
        {
            var query = DbSet.Where(p => marcasIds.Any(m => m == p.MarcaId) || categoriasIds.Any(c => c == p.CategoriaProdutoId));

            query = status switch
            {
                StatusConsulta.Ativos => query.Where(p => p.Ativo),
                StatusConsulta.Inativos => query.Where(p => !p.Ativo),
                _ => query
            };

            return await query.Select(p => p.Id).ToArrayAsync();
        }

        public async Task<List<Produto>> ObterProdutosFiltradosEmMassa(bool selecionados, Guid[] produtosIds, Guid[] marcasIds, Guid[] categoriasIds, StatusConsulta status)
        {
            // SQL: Where ProdutoId IN (...) OR ProdutoId NOT IN (...)
            var query = DbSet
                .Where(p => (selecionados && produtosIds.Contains(p.Id))
                         || (!selecionados && !produtosIds.Contains(p.Id)));

            // SQL: Where Ativo = 1 ou 0
            query = status switch
            {
                StatusConsulta.Ativos => query.Where(p => p.Ativo),
                StatusConsulta.Inativos => query.Where(p => !p.Ativo),
                _ => query
            };

            // SQL: Where MarcaId IN (...) OR CategoriaProdutoId IN (...)
            query = query.Where(p =>
                (marcasIds.Any(m => m == p.MarcaId)
                || categoriasIds.Any(c => c == p.CategoriaProdutoId))
            );

            return await query.ToListAsync();
        }

        public async Task<GridPaginadaRetorno<IdNomeViewModel>> ListarPaginadoEmMassa(GridPaginadaConsulta gridPaginada, CamposEditaveisProdutoAlteracaoEmMassa nomeCampo, object valor)
        {
            var queryProduto = AplicarFiltrosAlteracaoEmMassa(nomeCampo, valor);

            var queryVM = queryProduto
                .OrderBy(gridPaginada.OrderBy)
                .Select(p => new IdNomeViewModel(p.Id, p.Nome));

            var total = await queryProduto.CountAsync();
            var registros = await queryVM
                .Skip(gridPaginada.Skip)
                .Take(gridPaginada.TamanhoPaginaLimitado)
                .ToListAsync();

            return new(total, registros);
        }

        private IQueryable<Produto> AplicarFiltrosAlteracaoEmMassa(CamposEditaveisProdutoAlteracaoEmMassa nomeCampo, object valor)
        {
            var query = DbSet.AsQueryable();

            var coluna = nomeCampo.ObterNomeColuna();

            Type tabela = nomeCampo switch
            {
                CamposEditaveisProdutoAlteracaoEmMassa.PRODUTO_GERENCIADOR_IMPRESSAO => typeof(ProdutoGerenciadorImpressao),
                CamposEditaveisProdutoAlteracaoEmMassa.PRODUTO_ESTOQUE_MINIMO => typeof(ProdutoCorTamanho),
                _ => typeof(Produto)
            };

            var propertyInfo = tabela.GetProperty(coluna);

            object parsedValue = ProdutoAlteracaoEmMassaExtensions.ConvertValue(valor, propertyInfo.PropertyType);

            query = nomeCampo switch
            {
                // ProdutoGerenciadorImpressao.GerenciadorImpressaoId == valor
                CamposEditaveisProdutoAlteracaoEmMassa.PRODUTO_GERENCIADOR_IMPRESSAO => query.Where(x => x.GerenciadoresDeImpressao.Any(pgi => pgi.GerenciadorImpressaoId.Equals(parsedValue))),

                // ProdutoCorTamanho.EstoqueMinimo == valor
                CamposEditaveisProdutoAlteracaoEmMassa.PRODUTO_ESTOQUE_MINIMO => query.Where(x => x.ProdutoCores.Where(pc => pc.ProdutoCorTamanhos.Any(pct => pct.EstoqueMinimo.Equals(parsedValue))).Any()),

                // Produto.{coluna} == valor
                _ => query.Where(x => EF.Property<object>(x, coluna).Equals(parsedValue))
            };

            return query;
        }

        public GridPaginadaRetorno<ProdutoDetalhadoViewModel> ListarFiltrosDetalhadoPaginado(
            GridPaginadaConsulta gridPaginada,
            ProdutoDetalhadoFiltrosViewModel produtoDetalhadoFiltrosViewModel,
            Guid lojaId)
        {
            var gridPaginadaRetorno = new GridPaginadaRetorno<ProdutoDetalhadoViewModel>();

            var produtos = ListarProdutosFiltrosDetalhados(produtoDetalhadoFiltrosViewModel);

            var query = produtos.OrderBy($"{gridPaginada.CampoOrdenacao} {gridPaginada.DirecaoOrdenacao}")
                                .Select(ProdutoDetalhadoViewModel.Selector(lojaId));


            gridPaginadaRetorno.CarregarPaginacao(query, gridPaginada);
            return gridPaginadaRetorno;
        }

        public async Task<ProdutoDetalhadoViewModel> ObterPorSku(string sku, Guid lojaId)
        {
            var skuPattern = @"^\d+-\d+-\d+$";
            bool skuValido = Regex.IsMatch(sku, skuPattern);

            if (!skuValido)
                return null;

            return await DbSet.AsNoTracking()
                        .Where(p => p.ProdutoCores.Any(pc => pc.ProdutoCorTamanhos.Any(pct => pct.SKU == sku)))
                        .Select(ProdutoDetalhadoViewModel.Selector(lojaId))
                        .FirstOrDefaultAsync();
        }

        public async Task<IEnumerable<ProdutoDetalhadoViewModel>> ObterPorCodigoIntegracaoOuReferencia(string pesquisa, Guid lojaId)
        {
            var pesquisarPorCodigoIntegracao = int.TryParse(pesquisa, out int codigoIntegracao);
            return await DbSet.AsNoTracking()
                        .Where(p => (pesquisarPorCodigoIntegracao && p.CodigoIntegracao == codigoIntegracao) ||
                                    p.Referencia == pesquisa)
                        .OrderBy(p => p.CodigoIntegracao)
                        .Select(ProdutoDetalhadoViewModel.Selector(lojaId))
                        .ToListAsync();
        }

        public async Task<IEnumerable<ProdutoDetalhadoViewModel>> ObterPorCodigoGtinEanOuExterno(string pesquisa, Guid lojaId)
        {
            bool pesquisarPorGtinEan = long.TryParse(pesquisa, out long _) && pesquisa.Length is 8 or 12 or 13 or 14;
            return await DbSet.AsNoTracking().Where(p =>
                                            p.ProdutoCores.Any(pc =>
                                                pc.ProdutoCorTamanhos.Any(pct =>
                                                    ((pesquisarPorGtinEan && pct.CodigoGTINEAN == pesquisa) ||
                                                      pct.CodigoExterno == pesquisa) &&
                                                      pct.Ativo
                                                )
                                            ))
                        .Select(ProdutoDetalhadoViewModel.Selector(lojaId))
                        .ToListAsync();
        }

        public async Task<Produto> ObterProdutoPorCodigoGtinEanParaVincularProdutoFornecedor(string codigoGtinEan)
        {
            var produtos = await DbSet.AsNoTracking()
                .Where(p => p.ProdutoCores
                    .SelectMany(pc => pc.ProdutoCorTamanhos)
                    .Any(pct => p.Ativo && pct.Ativo && pct.CodigoGTINEAN == codigoGtinEan))
                .Select(p => new Produto
                {
                    Id = p.Id,
                    TipoProduto = p.TipoProduto
                })
                .ToListAsync();

            return produtos.Count == 1 ? produtos.Single() : null;
        }

        public async Task<ProdutoDetalhadoViewModel> ObterPorCodigoBarrasInternoOuSequencia(string pesquisa, Guid lojaId)
        {
            (var codigoBarrasHexadecimalValido, var hexadecimal) = CodigoBarrasReduzidoHelper.ValidarCodigoBarras(pesquisa);
            bool sequenciaCodigoValida = (pesquisa.StartsWith("2") || pesquisa.StartsWith("5")) && long.TryParse(pesquisa, out _) && pesquisa.Length is 13;

            return await DbSet.AsNoTracking().Where(p =>
                                            p.ProdutoCores.Any(pc =>
                                                pc.ProdutoCorTamanhos.Any(pct =>
                                                    ((codigoBarrasHexadecimalValido && pct.SequenciaCodigoBarras == hexadecimal) ||
                                                    (sequenciaCodigoValida && pct.CodigoBarrasInterno == pesquisa)) &&
                                                    pct.Ativo
                                                )
                                            ))
                        .Select(ProdutoDetalhadoViewModel.Selector(lojaId))
                        .FirstOrDefaultAsync();
        }

        public async Task<Produto> Obter(Guid id)
        {
            var produto = await DbSet
                .AsNoTracking()
                .Where(p => p.Id.Equals(id))
                .Select(p => new Produto()
                {
                    Id = p.Id,
                    DataHoraCadastro = p.DataHoraCadastro,
                    DataHoraUltimaAlteracao = p.DataHoraUltimaAlteracao,
                    SolicitarInformacaoComplementarNoPdv = p.SolicitarInformacaoComplementarNoPdv,
                    Ativo = p.Ativo,
                    Foto = p.Foto,
                    Nome = p.Nome,
                    NomeAbreviado = p.NomeAbreviado,
                    TipoProduto = p.TipoProduto,
                    Referencia = p.Referencia,
                    CategoriaProdutoId = p.CategoriaProdutoId,
                    CategoriaProduto = new CategoriaProduto()
                    {
                        Nome = p.CategoriaProduto.Nome,
                        CategoriaProdutoPai = new CategoriaProduto()
                        {
                            Nome = p.CategoriaProduto.CategoriaProdutoPai.Nome,
                            CategoriaProdutoPai = new CategoriaProduto()
                            {
                                Nome = p.CategoriaProduto.CategoriaProdutoPai.CategoriaProdutoPai.Nome,
                                CategoriaProdutoPai = new CategoriaProduto()
                                {
                                    Nome = p.CategoriaProduto.CategoriaProdutoPai.CategoriaProdutoPai.CategoriaProdutoPai.Nome
                                }
                            }
                        }
                    },
                    MarcaId = p.MarcaId,
                    Marca = new Marca()
                    {
                        Id = p.Marca.Id,
                        Nome = p.Marca.Nome
                    },
                    UnidadeMedidaId = p.UnidadeMedidaId,
                    UnidadeMedida = new UnidadeMedida()
                    {
                        Id = p.UnidadeMedida.Id,
                        Sigla = p.UnidadeMedida.Sigla,
                        Descricao = p.UnidadeMedida.Descricao
                    },
                    ProdutoPrecoLojas = p.ProdutoPrecoLojas.Select(pcl => new ProdutoPrecoLoja
                    {
                        Id = pcl.Id,
                        LojaId = pcl.LojaId,
                        Loja = new Loja
                        {
                            RazaoSocial = pcl.Loja.RazaoSocial,
                        },
                        ProdutoId = pcl.ProdutoId,
                        PrecoVenda = pcl.PrecoVenda,
                        PrecoCusto = pcl.PrecoCusto,
                        PrecoCompra = pcl.PrecoCompra,
                        Markup = pcl.Markup,
                    }).ToList(),
                    SkuIdentificador = p.SkuIdentificador,

                    RegraFiscalId = p.RegraFiscalId,
                    RegraFiscal = new RegraFiscal()
                    {
                        Id = p.RegraFiscal.Id,
                        Nome = p.RegraFiscal.Nome
                    },
                    IcmsStRetidoBaseCalculo = p.IcmsStRetidoBaseCalculo,
                    IcmsStRetidoValor = p.IcmsStRetidoValor,
                    FcpStRetidoBaseCalculo = p.FcpStRetidoBaseCalculo,
                    FcpStRetidoValor = p.FcpStRetidoValor,

                    IcmsAliquota = p.IcmsAliquota,
                    PisAliquota = p.PisAliquota,
                    CofinsAliquota = p.CofinsAliquota,
                    FcpAliquota = p.FcpAliquota,
                    IcmsReducaoBaseCalculo = p.IcmsReducaoBaseCalculo,
                    CodigoBeneficioFiscal = p.CodigoBeneficioFiscal,

                    UnidadeTributavelId = p.UnidadeTributavelId,
                    UnidadeTributavel = new UnidadeMedida()
                    {
                        Id = p.UnidadeTributavel.Id,
                        UnidadeTributavel = p.UnidadeTributavel.UnidadeTributavel,
                        Descricao = p.UnidadeTributavel.Descricao
                    },
                    QtdeConversao = p.QtdeConversao,
                    FatorConversao = p.FatorConversao,

                    CNPJFabricante = p.CNPJFabricante,
                    IndicadorEscalaRelevante = p.IndicadorEscalaRelevante,

                    CodigoAnp = p.CodigoAnp,
                    CODIF = p.CODIF,
                    PercentualGLP = p.PercentualGLP,
                    PercentualGasNacional = p.PercentualGasNacional,
                    PercentualGasImportado = p.PercentualGasImportado,
                    ValorPartidaGLP = p.ValorPartidaGLP,
                    AliquotaAdREmICMSRetido = p.AliquotaAdREmICMSRetido,
                    QuantidadeBCMonoRetido = p.QuantidadeBCMonoRetido,

                    CodigoNcm = p.CodigoNcm,
                    CodigoCest = p.CodigoCest,

                    CstOrigem = p.CstOrigem,
                    TipoProdutoFiscal = p.TipoProdutoFiscal,

                    ControlaEstoque = p.ControlaEstoque,
                    PermiteAlteraValorNaVenda = p.PermiteAlteraValorNaVenda,
                    UtilizarBalanca = p.UtilizarBalanca,
                    ExportarBalanca = p.ExportarBalanca,

                    TagProdutos = p.TagProdutos
                        .Select(tag => new TagProduto()
                        {
                            TagId = tag.TagId
                        })
                        .ToList(),
                    ProdutoCores = new List<ProdutoCor>(),
                    ProdutoRegraFiscalExcecoes = p.ProdutoRegraFiscalExcecoes
                        .Select(prf => new ProdutoRegraFiscalExcecao()
                        {
                            AliquotaIcms = prf.AliquotaIcms,
                            CodigoBeneficioFiscal = prf.CodigoBeneficioFiscal,
                            EstadoDestinoId = prf.EstadoDestinoId,
                            EstadoOrigemId = prf.EstadoOrigemId,
                            PorcentagemFCP = prf.PorcentagemFCP,
                            ReducaoBaseCalculo = prf.ReducaoBaseCalculo,
                        })
                        .ToList(),
                    CampoPersonalizadoProduto = p.CampoPersonalizadoProduto
                        .Select(cpp => new CampoPersonalizadoProduto()
                        {
                            CampoPersonalizadoId = cpp.CampoPersonalizadoId,
                            Valor = cpp.Valor
                        })
                        .ToList()
                })
                .FirstOrDefaultAsync();

            return produto;
        }

        public async Task<Produto> ObterVariacoesProduto(Guid id, Guid lojaId)
        {
            return await DbSet
                .AsNoTracking()
                .Where(p => p.Id.Equals(id))
                .Select(p => new Produto()
                {
                    Marca = new Marca(),
                    UnidadeMedida = new UnidadeMedida(),
                    UnidadeTributavel = new UnidadeMedida(),
                    RegraFiscal = new RegraFiscal(),
                    TipoProduto = p.TipoProduto,
                    ProdutoCores = p.ProdutoCores
                        .Where(pc => pc.Produto.ProdutoCores.Count() == 1 || !pc.Cor.PadraoSistema)
                        .Select(pc => new ProdutoCor()
                        {
                            CorId = pc.CorId,
                            Cor = new Cor()
                            {
                                Id = pc.Cor.Id,
                                Descricao = pc.Cor.Descricao,
                                PadraoSistema = pc.Cor.PadraoSistema
                            },
                            ProdutoCorTamanhos = pc.ProdutoCorTamanhos
                                .Where(pct => pct.ProdutoCor.Produto.TipoProduto != Multiempresa.Shared.Enums.TipoProduto.PRODUTO_VARIACAO ||
                                            ((pct.ProdutoCor.ProdutoCorTamanhos.Count() == 1 || !pct.Tamanho.PadraoSistema)
                                            && !(pct.ProdutoCor.Cor.PadraoSistema && pct.Tamanho.PadraoSistema)))
                                .Select(pct => new ProdutoCorTamanho()
                                {
                                    Id = pct.Id,
                                    TamanhoId = pct.TamanhoId,
                                    Tamanho = new Tamanho()
                                    {
                                        Id = pct.Tamanho.Id,
                                        Descricao = pct.Tamanho.Descricao,
                                        PadraoSistema = pct.Tamanho.PadraoSistema,
                                        SequenciaOrdenacao = pct.Tamanho.SequenciaOrdenacao
                                    },

                                    CodigoGTINEAN = pct.CodigoGTINEAN,
                                    Altura = pct.Altura,
                                    Largura = pct.Largura,
                                    Profundidade = pct.Profundidade,
                                    PesoLiquido = pct.PesoLiquido,
                                    PesoBruto = pct.PesoBruto,
                                    PesoEmbalagem = pct.PesoEmbalagem,
                                    EstoqueMinimo = pct.EstoqueMinimo,

                                    SKU = pct.SKU,
                                    CodigoBarrasInterno = pct.CodigoBarrasInterno,
                                    CodigoExterno = pct.CodigoExterno,
                                    ProdutoCorTamanhoEstoques = pct.ProdutoCorTamanhoEstoques.Where(x => x.LocalEstoque.LojaId == lojaId)
                                                                                             .Select(pcte => new ProdutoCorTamanhoEstoque
                                                                                             {
                                                                                                 EstoqueAtual = pcte.EstoqueAtual,
                                                                                                 LocalEstoque = new LocalEstoque
                                                                                                 {
                                                                                                     LojaId = pcte.LocalEstoque.LojaId
                                                                                                 }
                                                                                             }).ToList()
                                })
                                .ToList(),
                        })
                        .ToList()
                })
                .FirstOrDefaultAsync();
        }

        public async Task<Produto> ObterParaAlteracao(Guid id, Guid? lojaId = null)
        {
            var produto = await DbSet
                .Include(p => p.ProdutoPrecoLojas.Where(pcl => !lojaId.HasValue || pcl.LojaId.Equals(lojaId)))
                .Include(p => p.ProdutoCores)
                    .ThenInclude(p => p.ProdutoCorTamanhos)
                        .ThenInclude(p => p.ProdutoCorTamanhoEstoques)
                .Include(p => p.TagProdutos)
                .Include(p => p.ProdutoRegraFiscalExcecoes)
                .Include(p => p.CampoPersonalizadoProduto.Where(c => c.CampoPersonalizado.Ativo))
                .FirstOrDefaultAsync(p => p.Id.Equals(id));

            return produto;
        }

        public async Task<Produto> ObterParaGerenciarVariacoes(Guid id)
        {
            return await DbSet.Where(p => p.Id == id)
                            .Include(p => p.ProdutoCores)
                                .ThenInclude(pc => pc.Cor)
                            .Include(p => p.ProdutoCores)
                                .ThenInclude(pc => pc.ProdutoCorTamanhos)
                                    .ThenInclude(pct => pct.Tamanho)
                            .FirstOrDefaultAsync();
        }

        public async Task<List<Produto>> ObterParaGerenciarVariacoesPaginada(int pagina, int tamanhoPagina)
        {
            return await DbSet
                            .Where(p => p.TipoProduto == TipoProduto.PRODUTO_VARIACAO)
                            .Include(p => p.ProdutoCores)
                                .ThenInclude(pc => pc.Cor)
                            .Include(p => p.ProdutoCores)
                                .ThenInclude(pc => pc.ProdutoCorTamanhos)
                                    .ThenInclude(pct => pct.Tamanho)
                            .Skip(pagina * tamanhoPagina)
                            .Take(tamanhoPagina)
                            .ToListAsync();
        }

        public async Task<Produto> ObterParaInativar(Guid id)
        {
            var produto = await DbSet
                .Include(p => p.ProdutoCores)
                    .ThenInclude(p => p.ProdutoCorTamanhos)
                .FirstOrDefaultAsync(p => p.Id.Equals(id));

            return produto;
        }

        private IQueryable<Produto> FiltrarCamposListagemProduto(
            ProdutoFiltrosViewModel produtoFiltrosViewModel,
            IEnumerable<Guid> produtosIds)
        {
            IQueryable<Produto> produtos;

            if (produtosIds == null)
            {
                produtos = FiltrarIdentificadoresProduto(produtoFiltrosViewModel.NomeReferencia);
            }
            else
            {
                produtos = DbSet.AsQueryable();

                if (produtosIds.Any() || !string.IsNullOrWhiteSpace(produtoFiltrosViewModel.NomeReferencia))
                    produtos = produtos.Where(p => produtosIds.Contains(p.Id));
            }

            produtos = produtoFiltrosViewModel.StatusConsulta switch
            {
                StatusConsulta.Ativos => produtos.Where(p => p.Ativo),
                StatusConsulta.Inativos => produtos.Where(p => !p.Ativo),
                StatusConsulta.Todos => produtos,
                _ => throw new InvalidCastException("Status não identificado")
            };

            // ---- FILTRO DE ESTOQUE / COR / TAMANHO ----
            bool temFiltroEstoque = Enum.IsDefined(produtoFiltrosViewModel.TipoEstoque) && produtoFiltrosViewModel.TipoEstoque != TipoFiltroProdutoEstoque.TODOS;
            bool temFiltroCor = produtoFiltrosViewModel.Cores != null && produtoFiltrosViewModel.Cores.Any();
            bool temFiltroTamanho = produtoFiltrosViewModel.Tamanhos != null && produtoFiltrosViewModel.Tamanhos.Any();

            if (temFiltroEstoque)
            {
                produtos = produtos
                    .Where(x => x.ProdutoCores.Any(pc =>
                        (!temFiltroCor || produtoFiltrosViewModel.Cores.Contains(pc.CorId)) &&
                        pc.ProdutoCorTamanhos.Any(pct =>
                            pct.Ativo &&
                            (!temFiltroTamanho || produtoFiltrosViewModel.Tamanhos.Contains(pct.TamanhoId)) &&
                            pct.ProdutoCorTamanhoEstoques.Any(pcte =>
                                (produtoFiltrosViewModel.TipoEstoque == TipoFiltroProdutoEstoque.POSITIVO && pcte.EstoqueAtual > 0) ||
                                (produtoFiltrosViewModel.TipoEstoque == TipoFiltroProdutoEstoque.NEGATIVO && pcte.EstoqueAtual < 0) ||
                                (produtoFiltrosViewModel.TipoEstoque == TipoFiltroProdutoEstoque.ZERADO && pcte.EstoqueAtual == 0) ||
                                (produtoFiltrosViewModel.TipoEstoque == TipoFiltroProdutoEstoque.ABAIXO_MINIMO && pcte.EstoqueAtual < pct.EstoqueMinimo)
                            )
                        )
                    ));
            }
            else if (temFiltroCor || temFiltroTamanho)
            {
                produtos = produtos.Where(x =>
                    x.ProdutoCores.Any(pc =>
                        (!temFiltroCor || produtoFiltrosViewModel.Cores.Contains(pc.CorId)) &&
                        pc.ProdutoCorTamanhos.Any(pct =>
                            !temFiltroTamanho || produtoFiltrosViewModel.Tamanhos.Contains(pct.TamanhoId)
                        )
                    )
                );
            }

            // ---- FILTROS ADICIONAIS ----
            if (produtoFiltrosViewModel.CategoriasProduto != null && produtoFiltrosViewModel.CategoriasProduto.Any())
            {
                produtos = produtos.Where(x => produtoFiltrosViewModel.CategoriasProduto.Contains(x.CategoriaProdutoId));
            }

            if (produtoFiltrosViewModel.Marcas != null && produtoFiltrosViewModel.Marcas.Any())
            {
                produtos = produtos.Where(x => produtoFiltrosViewModel.Marcas.Contains(x.MarcaId));
            }

            if (produtoFiltrosViewModel.Tags != null && produtoFiltrosViewModel.Tags.Any())
            {
                produtos = produtos.Where(x => x.TagProdutos.Any(y => produtoFiltrosViewModel.Tags.Contains(y.TagId)));
            }

            if (produtoFiltrosViewModel.CamposPersonalizados != null && produtoFiltrosViewModel.CamposPersonalizados.Length > 0)
            {
                foreach (var campo in produtoFiltrosViewModel.CamposPersonalizados)
                {
                    produtos = produtos.Where(x =>
                        x.CampoPersonalizadoProduto.Any(y =>
                            y.CampoPersonalizadoId == campo.CampoPersonalizadoId &&
                            y.Valor.Contains(campo.Valor)
                        )
                    );
                }
            }

            return produtos;
        }

        public async Task<string> ObterUrlFoto(Guid produtoId)
        {
            return await DbSet.Where(x => x.Id == produtoId)
                              .Select(x => x.Foto)
                              .FirstOrDefaultAsync();
        }

        public async Task<string> ObterNome(Guid id)
        {
            return await DbSet.Where(p => p.Id == id)
                              .Select(p => p.Nome)
                              .FirstOrDefaultAsync();
        }

        public async Task<decimal> ObterPrecoPorLoja(Guid produtoId, Guid lojaId)
        {
            return await Db.ProdutoPrecoLoja.Where(x => x.ProdutoId == produtoId && x.LojaId == lojaId)
                                            .Select(x => x.PrecoVenda)
                                            .FirstOrDefaultAsync();
        }

        public async Task<(decimal precoVenda, decimal precoCusto)> ObterPrecoVendaCustoPorLoja(Guid produtoId, Guid lojaId)
        {
            var preco = await Db.ProdutoPrecoLoja.Where(x => x.ProdutoId == produtoId && x.LojaId == lojaId)
                                            .Select(x => new ProdutoPrecoLoja
                                            {
                                                PrecoVenda = x.PrecoVenda,
                                                PrecoCusto = x.PrecoCusto
                                            })
                                            .FirstOrDefaultAsync();

            return (preco.PrecoVenda, preco.PrecoCusto);
        }

        public async Task<List<Produto>> ObterProdutosParaRecriarVariacoes(List<Guid> listaProdutos = null)
        {
            return await DbSet.Where(x => listaProdutos == null || listaProdutos.Contains(x.Id)).Select(x => new Produto
            {
                Id = x.Id,
                ProdutoCores = x.ProdutoCores.Select(y => new ProdutoCor
                {
                    Id = y.Id,
                    ProdutoCorTamanhos = y.ProdutoCorTamanhos.Select(z => new ProdutoCorTamanho
                    {
                        TamanhoId = z.TamanhoId,
                    }).ToList()
                }).ToList()
            })
            .ToListAsync();
        }

        public async Task<List<Produto>> ListarSelect(
            string produtoNomeReferencia,
            TipoProduto? tipoProduto = null)
        {
            var where =
                FiltrarProduto(produtoNomeReferencia, tipoProduto);

            return await where.Select(x => new Produto
            {
                Id = x.Id,
                Nome = x.Nome
            })
            .ToListAsync();
        }

        public async Task<List<Guid>> ListarPorCategoria(Guid categoriaId)
        {
            return await DbSet
                .Where(x => x.CategoriaProdutoId == categoriaId && x.Ativo)
                .Select(x => x.Id)
                .ToListAsync();
        }

        private IQueryable<Produto> FiltrarProduto(
            string nomeReferencia,
            TipoProduto? tipoProduto = null)
        {
            var produtos = DbSet.Where(p => p.Ativo && (tipoProduto == null || p.TipoProduto == tipoProduto));

            if (!string.IsNullOrEmpty(nomeReferencia))
            {
                produtos = produtos.Where(p => EF.Functions.Collate(p.Nome, Collates.RemoverCaracteresEspeciaisAcentos).Contains(nomeReferencia) ||
                                         EF.Functions.Collate(p.Referencia, Collates.RemoverCaracteresEspeciaisAcentos).Equals(nomeReferencia));
            }

            return produtos;
        }

        public async Task<Produto> ObterProdutoParaDuplicar(Guid id)
        {
            return await DbSet
               .Where(c => c.Id == id)
               .Select(p => new Produto()
               {
                   Nome = p.Nome,
                   NomeAbreviado = p.NomeAbreviado,
                   TipoProduto = p.TipoProduto,
                   Ativo = p.Ativo,
                   Foto = p.Foto,
                   UnidadeMedidaId = p.UnidadeMedidaId,
                   CategoriaProdutoId = p.CategoriaProdutoId,
                   MarcaId = p.MarcaId,

                   #region Configurações (FOOD)
                   UsarComoComplemento = p.UsarComoComplemento,
                   ProdutoCombo = p.ProdutoCombo,
                   CobrarTaxaServico = p.CobrarTaxaServico,
                   BaixarSaldoMateriaPrima = p.BaixarSaldoMateriaPrima,
                   UtilizarPrecoDosItensEtapa = p.UtilizarPrecoDosItensEtapa,
                   ComposicaoProduto = p.ComposicaoProduto,
                   DiasParaValidade = p.DiasParaValidade,
                   PrecoCombo = p.PrecoCombo,
                   ImagemCardapio = p.ImagemCardapio,
                   ImagemDestaque = p.ImagemDestaque,
                   GerenciadoresDeImpressao = p.GerenciadoresDeImpressao.Select(pgi => new ProdutoGerenciadorImpressao
                   {
                       GerenciadorImpressao = new GerenciadorImpressao
                       {
                           Id = pgi.GerenciadorImpressao.Id,
                           Nome = pgi.GerenciadorImpressao.Nome,
                       }
                   }).ToList(),
                   #endregion

                   #region FichaTecnica
                   ProdutosFichaTecnicaPrincipal = p.ProdutosFichaTecnicaPrincipal
                       .Select(ft => new ProdutoFichaTecnica
                       {
                           DataHoraCadastro = ft.DataHoraCadastro,
                           DataHoraUltimaAlteracao = ft.DataHoraUltimaAlteracao,
                           MateriaPrimaFinal = ft.MateriaPrimaFinal,
                           ProdutoItem = ft.ProdutoItem,
                           ProdutoItemId = ft.ProdutoItemId,
                           ProdutoTamanhosFichaTecnica = ft.ProdutoTamanhosFichaTecnica
                       })
                       .ToList(),
                   #endregion

                   #region Etapas
                   Etapas = p.Etapas
                       .Select(e => new ProdutoEtapa
                       {
                           CategoriaProdutoId = e.CategoriaProdutoId,
                           TextoParaExibicao = e.TextoParaExibicao,
                           QuantidadeMinima = e.QuantidadeMinima,
                           QuantidadeMaxima = e.QuantidadeMaxima,
                           PermitirItensExtras = e.PermitirItensExtras,
                           QuantidadeEscolhasPorItem = e.QuantidadeEscolhasPorItem,
                           TelasExibicao = e.TelasExibicao,
                           SequenciaOrdenacao = e.SequenciaOrdenacao,
                           TipoEtapa = e.TipoEtapa,
                           DataHoraCadastro = e.DataHoraCadastro,
                           DataHoraUltimaAlteracao = e.DataHoraUltimaAlteracao,
                           Itens = e.Itens.Select(i => new ProdutoEtapaItem
                           {
                               ProdutoEtapaId = i.ProdutoEtapaId,
                               ProdutoId = i.ProdutoId,
                           }).ToList(),
                       }).ToList(),
                   #endregion

                   #region Variacoes
                   ProdutoCores = p.ProdutoCores
                        .Select(pc => new ProdutoCor()
                        {
                            CorId = pc.CorId,
                            Cor = new Cor
                            {
                                PadraoSistema = pc.Cor.PadraoSistema
                            },
                            ProdutoCorTamanhos = pc.ProdutoCorTamanhos
                                .Select(pct => new ProdutoCorTamanho()
                                {
                                    Id = pct.Id,
                                    Ativo = pct.Ativo,
                                    TamanhoId = pct.TamanhoId,
                                    Tamanho = new Tamanho
                                    {
                                        PadraoSistema = pct.Tamanho.PadraoSistema
                                    },
                                    Altura = pct.Altura,
                                    Largura = pct.Largura,
                                    Profundidade = pct.Profundidade,
                                    PesoLiquido = pct.PesoLiquido,
                                    PesoBruto = pct.PesoBruto,
                                    PesoEmbalagem = pct.PesoEmbalagem,
                                    EstoqueMinimo = pct.EstoqueMinimo
                                })
                                .ToList(),
                        })
                        .ToList(),
                   #endregion

                   #region Precos
                   ProdutoPrecoLojas = p.ProdutoPrecoLojas.Select(pcl => new ProdutoPrecoLoja
                   {
                       LojaId = pcl.LojaId,
                       PrecoVenda = pcl.PrecoVenda,
                       PrecoCusto = pcl.PrecoCusto,
                       PrecoCompra = pcl.PrecoCompra,
                       Markup = pcl.Markup,
                   }).ToList(),

                   TabelaPrecoProdutos = p.TabelaPrecoProdutos.Select(tp => new TabelaPrecoProduto
                   {
                       TabelaPrecoId = tp.TabelaPrecoId,
                       PrecoVenda = tp.PrecoVenda
                   }).ToList(),
                   #endregion

                   #region Informacoes fiscais
                   RegraFiscalId = p.RegraFiscalId,
                   IcmsStRetidoBaseCalculo = p.IcmsStRetidoBaseCalculo,
                   IcmsStRetidoValor = p.IcmsStRetidoValor,
                   FcpStRetidoBaseCalculo = p.FcpStRetidoBaseCalculo,
                   FcpStRetidoValor = p.FcpStRetidoValor,

                   IcmsAliquota = p.IcmsAliquota,
                   PisAliquota = p.PisAliquota,
                   CofinsAliquota = p.CofinsAliquota,
                   FcpAliquota = p.FcpAliquota,
                   IcmsReducaoBaseCalculo = p.IcmsReducaoBaseCalculo,
                   CodigoBeneficioFiscal = p.CodigoBeneficioFiscal,

                   UnidadeTributavelId = p.UnidadeTributavelId,
                   QtdeConversao = p.QtdeConversao,
                   FatorConversao = p.FatorConversao,

                   CNPJFabricante = p.CNPJFabricante,
                   IndicadorEscalaRelevante = p.IndicadorEscalaRelevante,

                   CodigoAnp = p.CodigoAnp,
                   CODIF = p.CODIF,
                   PercentualGLP = p.PercentualGLP,
                   PercentualGasNacional = p.PercentualGasNacional,
                   PercentualGasImportado = p.PercentualGasImportado,
                   ValorPartidaGLP = p.ValorPartidaGLP,
                   AliquotaAdREmICMSRetido = p.AliquotaAdREmICMSRetido,
                   QuantidadeBCMonoRetido = p.QuantidadeBCMonoRetido,

                   CodigoNcm = p.CodigoNcm,
                   CodigoCest = p.CodigoCest,

                   CstOrigem = p.CstOrigem,
                   TipoProdutoFiscal = p.TipoProdutoFiscal,

                   ProdutoRegraFiscalExcecoes = p.ProdutoRegraFiscalExcecoes.Select(e => new ProdutoRegraFiscalExcecao
                   {
                       EstadoDestinoId = e.EstadoDestinoId,
                       EstadoOrigemId = e.EstadoOrigemId,
                       CodigoBeneficioFiscal = e.CodigoBeneficioFiscal,
                       AliquotaIcms = e.AliquotaIcms,
                       PorcentagemFCP = e.PorcentagemFCP,
                       ReducaoBaseCalculo = e.ReducaoBaseCalculo
                   }).ToList(),
                   #endregion

                   #region Informacoes adicionais
                   ControlaEstoque = p.ControlaEstoque,
                   PermiteAlteraValorNaVenda = p.PermiteAlteraValorNaVenda,
                   UtilizarBalanca = p.UtilizarBalanca,
                   ExportarBalanca = p.ExportarBalanca,
                   SolicitarInformacaoComplementarNoPdv = p.SolicitarInformacaoComplementarNoPdv,
                   VenderEcommerce = p.VenderEcommerce,
                   #endregion

                   #region E-commerce
                   ProdutoEcommerces = p.ProdutoEcommerces.Select(pe => new ProdutoEcommerce
                   {
                       Titulo = pe.Titulo,
                       Descricao = pe.Descricao,
                       Anunciado = pe.Anunciado,
                       IdentificacaoIntegracao = pe.IdentificacaoIntegracao,
                       ItensInclusos = pe.ItensInclusos,
                       DisponibilidadeEntrega = pe.DisponibilidadeEntrega,
                       TempoGarantia = pe.TempoGarantia,
                       Campo1 = pe.Campo1,
                       Campo2 = pe.Campo2,
                       Referencia = pe.Referencia
                   }).ToList(),
                   #endregion

                   #region Tags
                   TagProdutos = p.TagProdutos.Select(t => new TagProduto
                   {
                       TagId = t.TagId
                   }).ToList(),
                   #endregion

                   #region Campos personalizados
                   CampoPersonalizadoProduto = p.CampoPersonalizadoProduto.Select(cp => new CampoPersonalizadoProduto
                   {
                       CampoPersonalizadoId = cp.CampoPersonalizadoId,
                       Valor = cp.Valor
                   }).ToList()
                   #endregion
               })
               .FirstOrDefaultAsync();
        }

        public async Task<List<Produto>> ObterListaProdutoGrid(
           Guid lojaId,
           GridPaginadaConsulta gridPaginada,
           ProdutoFiltrosViewModel produtoFiltrosViewModel,
           IdentificacaoIntegracao identificacaoIntegracao)
        {
            var listaProduto = new List<Produto>();

            var produtos = FiltrarCamposListagemProduto(produtoFiltrosViewModel, null);

            listaProduto = await produtos.Where(p => p.ProdutoEcommerces != null &&
                                                     p.ProdutoEcommerces.FirstOrDefault(c => c.IdentificacaoIntegracao == identificacaoIntegracao).Anunciado)
                                         .OrderBy($"{gridPaginada.CampoOrdenacao} {gridPaginada.DirecaoOrdenacao}")
                                         .Select(p => new Produto()
                                         {
                                             Id = p.Id,
                                             Nome = p.Nome,
                                             Ativo = p.Ativo,
                                             TipoProduto = p.TipoProduto,
                                             Referencia = p.Referencia,
                                             SkuIdentificador = p.SkuIdentificador,
                                             ProdutoCores = p.ProdutoCores.Select(pc => new ProdutoCor()
                                             {
                                                 Id = pc.Id,
                                                 CorId = pc.CorId,
                                                 Ativo = pc.Ativo,
                                                 Cor = new Cor()
                                                 {
                                                     Id = pc.Cor.Id,
                                                     Descricao = pc.Cor.Descricao,
                                                     PadraoSistema = pc.Cor.PadraoSistema
                                                 },
                                                 ProdutoCorTamanhos = pc.ProdutoCorTamanhos
                                                     .Where(pct => (pct.ProdutoCor.Produto.TipoProduto != TipoProduto.PRODUTO_VARIACAO ||
                                                                   (pct.ProdutoCor.Produto.TipoProduto == TipoProduto.PRODUTO_VARIACAO &&
                                                                   (pct.ProdutoCor.Produto.ProdutoCores.Count() == 1 || !pct.ProdutoCor.Cor.PadraoSistema) &&
                                                                   (pct.ProdutoCor.ProdutoCorTamanhos.Count() == 1 || !pct.Tamanho.PadraoSistema))))
                                                     .Select(pct => new ProdutoCorTamanho()
                                                     {
                                                         Id = pct.Id,
                                                         TamanhoId = pct.TamanhoId,
                                                         Ativo = pc.Ativo,
                                                         Tamanho = new Tamanho()
                                                         {
                                                             Id = pct.Tamanho.Id,
                                                             Descricao = pct.Tamanho.Descricao,
                                                             PadraoSistema = pct.Tamanho.PadraoSistema,
                                                             SequenciaOrdenacao = pct.Tamanho.SequenciaOrdenacao
                                                         },
                                                         ProdutoCorTamanhoEstoques = pct.ProdutoCorTamanhoEstoques.Where(x => x.LocalEstoque.LojaId == lojaId)
                                                                                        .Select(pcte => new ProdutoCorTamanhoEstoque
                                                                                        {
                                                                                            EstoqueAtual = pcte.EstoqueAtual,
                                                                                            LocalEstoque = new LocalEstoque
                                                                                            {
                                                                                                LojaId = pcte.LocalEstoque.LojaId
                                                                                            }
                                                                                        }).ToList()
                                                     })
                                                     .ToList(),
                                             }).ToList(),
                                             ProdutoPrecoLojas = p.ProdutoPrecoLojas.Select(pcl => new ProdutoPrecoLoja
                                             {
                                                 LojaId = pcl.LojaId,
                                                 PrecoVenda = pcl.PrecoVenda,
                                                 PrecoCusto = pcl.PrecoCusto,
                                                 PrecoCompra = pcl.PrecoCompra,
                                                 Markup = pcl.Markup,
                                             }).ToList()
                                         }).ToListAsync();

            return listaProduto;
        }

        public async Task<string> ObterImagemPrincipal(Guid id)
        {
            return await DbSet
                         .Where(p => p.Id == id)
                         .SelectMany(p => p.ProdutoCores.SelectMany(pc => pc.ProdutoCorImagens))
                                                        .Where(i => i.Principal)
                                                        .Select(i => i.Imagem)
                                                        .FirstOrDefaultAsync();
        }

        public IQueryable<Produto> Where(Expression<Func<Produto, bool>> predicate)
            => DbSet.Where(predicate);

        public Task<int> Count(Expression<Func<Produto, bool>> predicate)
            => DbSet.CountAsync(predicate);

        public async Task AtualizarProdutoNcmCestConformeNFe(Guid produtoId, string ncm, string cest)
        {
            var produto = await DbSet.FindAsync(produtoId);
            if (produto is null)
            {
                return;
            }

            if (!string.IsNullOrWhiteSpace(ncm))
            {
                produto.CodigoNcm = ncm;
            }
            produto.CodigoCest = cest;

            await SaveChanges();
        }

        public async Task<int> MaxAsync(Expression<Func<Produto, int>> selector)
            => await DbSet.MaxAsync(selector);

        #region Integracao

        public async Task<Produto> ObterProdutoIntegracaoCompleto(
            Guid lojaId,
            Guid id,
            IdentificacaoIntegracao identificacaoIntegracao,
            Guid localEstoqueId)
        {
            return await DbSet
                .Where(c => c.Id == id &&
                            c.ProdutoEcommerces != null &&
                            c.ProdutoEcommerces.FirstOrDefault(c => c.IdentificacaoIntegracao == identificacaoIntegracao).Anunciado)
                .Select(p => new Produto()
                {
                    Id = p.Id,
                    Nome = p.Nome,
                    NomeAbreviado = p.NomeAbreviado,
                    TipoProduto = p.TipoProduto,
                    Ativo = p.Ativo,
                    Foto = p.Foto,
                    Referencia = p.Referencia,
                    SkuIdentificador = p.SkuIdentificador,
                    CodigoNcm = p.CodigoNcm,
                    VenderEcommerce = p.VenderEcommerce,
                    Marca = new Marca()
                    {
                        Id = p.Marca.Id,
                        Nome = p.Marca.Nome
                    },
                    CategoriaProduto = new CategoriaProduto()
                    {
                        Id = p.CategoriaProduto == null ? Guid.Empty : p.CategoriaProduto.Id,
                        Nome = p.CategoriaProduto.Nome,
                        CategoriaProdutoPai = new CategoriaProduto()
                        {
                            Id = p.CategoriaProduto.CategoriaProdutoPai == null ? Guid.Empty : p.CategoriaProduto.CategoriaProdutoPai.Id,
                            Nome = p.CategoriaProduto.CategoriaProdutoPai.Nome,
                            CategoriaProdutoPai = new CategoriaProduto()
                            {
                                Id = p.CategoriaProduto.CategoriaProdutoPai.CategoriaProdutoPai == null ? Guid.Empty : p.CategoriaProduto.CategoriaProdutoPai.CategoriaProdutoPai.Id,
                                Nome = p.CategoriaProduto.CategoriaProdutoPai.CategoriaProdutoPai.Nome,
                                CategoriaProdutoPai = new CategoriaProduto()
                                {
                                    Id = p.CategoriaProduto.CategoriaProdutoPai.CategoriaProdutoPai.CategoriaProdutoPai == null ? Guid.Empty : p.CategoriaProduto.CategoriaProdutoPai.CategoriaProdutoPai.CategoriaProdutoPai.Id,
                                    Nome = p.CategoriaProduto.CategoriaProdutoPai.CategoriaProdutoPai.CategoriaProdutoPai.Nome
                                }
                            }
                        }
                    },
                    ProdutoCores = p.ProdutoCores
                         .Where(pc => p.TipoProduto != TipoProduto.PRODUTO_VARIACAO ||
                                      (!pc.Cor.PadraoSistema || p.ProdutoCores.Count == 1))
                         .Select(pc => new ProdutoCor()
                         {
                             CorId = pc.CorId,
                             Ativo = pc.Ativo,
                             Cor = new Cor()
                             {
                                 Id = pc.Cor.Id,
                                 Descricao = pc.Cor.Descricao,
                                 PadraoSistema = pc.Cor.PadraoSistema
                             },
                             ProdutoCorImagens = pc.ProdutoCorImagens
                                 .Select(i => new ProdutoCorImagem()
                                 {
                                     Id = i.Id,
                                     Imagem = i.Imagem,
                                     SequenciaOrdenacao = i.SequenciaOrdenacao,
                                     Principal = i.Principal
                                 })
                                 .ToList(),
                             ProdutoCorTamanhos = pc.ProdutoCorTamanhos
                                                 .Where(pct => pc.ProdutoCorTamanhos.Count == 1 || !pct.Tamanho.PadraoSistema)
                                                 .Select(pct => new ProdutoCorTamanho()
                                                 {
                                                     Id = pct.Id,
                                                     Ativo = pct.Ativo,
                                                     TamanhoId = pct.TamanhoId,
                                                     Tamanho = new Tamanho()
                                                     {
                                                         Id = pct.Tamanho.Id,
                                                         Descricao = pct.Tamanho.Descricao,
                                                         PadraoSistema = pct.Tamanho.PadraoSistema,
                                                         SequenciaOrdenacao = pct.Tamanho.SequenciaOrdenacao
                                                     },
                                                     CodigoGTINEAN = pct.CodigoGTINEAN,
                                                     Altura = pct.Altura,
                                                     Largura = pct.Largura,
                                                     Profundidade = pct.Profundidade,
                                                     PesoLiquido = pct.PesoLiquido,
                                                     PesoBruto = pct.PesoBruto,
                                                     PesoEmbalagem = pct.PesoEmbalagem,
                                                     EstoqueMinimo = pct.EstoqueMinimo,
                                                     SKU = pct.SKU,
                                                     CodigoBarrasInterno = pct.CodigoBarrasInterno,
                                                     CodigoExterno = pct.CodigoExterno,
                                                     ProdutoCorTamanhoEstoques = pct.ProdutoCorTamanhoEstoques.Where(x => x.LocalEstoqueId == localEstoqueId)
                                                                                                              .Select(pcte => new ProdutoCorTamanhoEstoque
                                                                                                              {
                                                                                                                  EstoqueAtual = pcte.EstoqueAtual,
                                                                                                                  LocalEstoque = new LocalEstoque
                                                                                                                  {
                                                                                                                      LojaId = pcte.LocalEstoque.LojaId
                                                                                                                  }
                                                                                                              }).ToList()
                                                 }).OrderBy(c => c.Tamanho.SequenciaOrdenacao)
                                                 .ToList(),
                         }).OrderBy(c => c.Cor.Descricao)
                         .ToList(),
                    ProdutoPrecoLojas = p.ProdutoPrecoLojas.Select(pcl => new ProdutoPrecoLoja
                    {
                        Id = pcl.Id,
                        LojaId = pcl.LojaId,
                        Loja = new Loja
                        {
                            RazaoSocial = pcl.Loja.RazaoSocial,
                        },
                        ProdutoId = pcl.ProdutoId,
                        PrecoVenda = pcl.PrecoVenda,
                        PrecoCusto = pcl.PrecoCusto,
                        PrecoCompra = pcl.PrecoCompra,
                        Markup = pcl.Markup,
                    }).ToList(),
                    TabelaPrecoProdutos = p.TabelaPrecoProdutos.Select(tp => new TabelaPrecoProduto
                    {
                        TabelaPrecoId = tp.TabelaPrecoId,
                        PrecoVenda = tp.PrecoVenda
                    }).ToList(),
                    ProdutoEcommerces = p.ProdutoEcommerces.Select(pe => new ProdutoEcommerce
                    {
                        Titulo = pe.Titulo,
                        Descricao = pe.Descricao,
                        Anunciado = pe.Anunciado,
                        IdentificacaoIntegracao = pe.IdentificacaoIntegracao,
                        ItensInclusos = pe.ItensInclusos,
                        DisponibilidadeEntrega = pe.DisponibilidadeEntrega,
                        TempoGarantia = pe.TempoGarantia,
                        Campo1 = pe.Campo1,
                        Campo2 = pe.Campo2,
                        Referencia = pe.Referencia
                    }).ToList()
                })
                .FirstOrDefaultAsync();
        }

        public async Task<List<Produto>> ObterListaProdutoIntegracaoCompleto(
            Guid lojaId,
            bool? ativo,
            IdentificacaoIntegracao identificacaoIntegracao,
            Guid localEstoqueId)
        {
            return await DbSet
                .Where(x => (ativo == null || x.Ativo.Equals(ativo)) &&
                                  x.ProdutoEcommerces != null &&
                                  x.ProdutoEcommerces.FirstOrDefault(c => c.IdentificacaoIntegracao == identificacaoIntegracao).Anunciado)
                .Select(p => new Produto()
                {
                    Id = p.Id,
                    Nome = p.Nome,
                    NomeAbreviado = p.NomeAbreviado,
                    TipoProduto = p.TipoProduto,
                    Ativo = p.Ativo,
                    Foto = p.Foto,
                    Referencia = p.Referencia,
                    SkuIdentificador = p.SkuIdentificador,
                    CodigoNcm = p.CodigoNcm,
                    VenderEcommerce = p.VenderEcommerce,
                    Marca = new Marca()
                    {
                        Id = p.Marca.Id,
                        Nome = p.Marca.Nome
                    },
                    CategoriaProduto = new CategoriaProduto()
                    {
                        Id = p.CategoriaProduto == null ? Guid.Empty : p.CategoriaProduto.Id,
                        Nome = p.CategoriaProduto.Nome,
                        CategoriaProdutoPai = new CategoriaProduto()
                        {
                            Id = p.CategoriaProduto.CategoriaProdutoPai == null ? Guid.Empty : p.CategoriaProduto.CategoriaProdutoPai.Id,
                            Nome = p.CategoriaProduto.CategoriaProdutoPai.Nome,
                            CategoriaProdutoPai = new CategoriaProduto()
                            {
                                Id = p.CategoriaProduto.CategoriaProdutoPai.CategoriaProdutoPai == null ? Guid.Empty : p.CategoriaProduto.CategoriaProdutoPai.CategoriaProdutoPai.Id,
                                Nome = p.CategoriaProduto.CategoriaProdutoPai.CategoriaProdutoPai.Nome,
                                CategoriaProdutoPai = new CategoriaProduto()
                                {
                                    Id = p.CategoriaProduto.CategoriaProdutoPai.CategoriaProdutoPai.CategoriaProdutoPai == null ? Guid.Empty : p.CategoriaProduto.CategoriaProdutoPai.CategoriaProdutoPai.CategoriaProdutoPai.Id,
                                    Nome = p.CategoriaProduto.CategoriaProdutoPai.CategoriaProdutoPai.CategoriaProdutoPai.Nome
                                }
                            }
                        }
                    },
                    ProdutoCores = p.ProdutoCores
                         .Select(pc => new ProdutoCor()
                         {
                             CorId = pc.CorId,
                             Ativo = pc.Ativo,
                             Cor = new Cor()
                             {
                                 Id = pc.Cor.Id,
                                 Descricao = pc.Cor.Descricao,
                                 PadraoSistema = pc.Cor.PadraoSistema
                             },
                             ProdutoCorImagens = pc.ProdutoCorImagens
                                 .Select(i => new ProdutoCorImagem()
                                 {
                                     Id = i.Id,
                                     Imagem = i.Imagem,
                                     SequenciaOrdenacao = i.SequenciaOrdenacao,
                                     Principal = i.Principal
                                 })
                                 .ToList(),
                             ProdutoCorTamanhos = pc.ProdutoCorTamanhos
                                                  .Where(pct => (pct.ProdutoCor.Produto.TipoProduto != TipoProduto.PRODUTO_VARIACAO ||
                                                                (pct.ProdutoCor.Produto.TipoProduto == TipoProduto.PRODUTO_VARIACAO &&
                                                                (pct.ProdutoCor.Produto.ProdutoCores.Count() == 1 || !pct.ProdutoCor.Cor.PadraoSistema) &&
                                                                (pct.ProdutoCor.ProdutoCorTamanhos.Count() == 1 || !pct.Tamanho.PadraoSistema))))
                                                 .Select(pct => new ProdutoCorTamanho()
                                                 {
                                                     Id = pct.Id,
                                                     Ativo = pc.Ativo,
                                                     TamanhoId = pct.TamanhoId,
                                                     Tamanho = new Tamanho()
                                                     {
                                                         Id = pct.Tamanho.Id,
                                                         Descricao = pct.Tamanho.Descricao,
                                                         PadraoSistema = pct.Tamanho.PadraoSistema,
                                                         SequenciaOrdenacao = pct.Tamanho.SequenciaOrdenacao
                                                     },
                                                     CodigoGTINEAN = pct.CodigoGTINEAN,
                                                     Altura = pct.Altura,
                                                     Largura = pct.Largura,
                                                     Profundidade = pct.Profundidade,
                                                     PesoLiquido = pct.PesoLiquido,
                                                     PesoBruto = pct.PesoBruto,
                                                     PesoEmbalagem = pct.PesoEmbalagem,
                                                     EstoqueMinimo = pct.EstoqueMinimo,
                                                     SKU = pct.SKU,
                                                     CodigoBarrasInterno = pct.CodigoBarrasInterno,
                                                     CodigoExterno = pct.CodigoExterno,
                                                     ProdutoCorTamanhoEstoques = pct.ProdutoCorTamanhoEstoques.Where(x => x.LocalEstoqueId == localEstoqueId)
                                                                                                              .Select(pcte => new ProdutoCorTamanhoEstoque
                                                                                                              {
                                                                                                                  EstoqueAtual = pcte.EstoqueAtual,
                                                                                                                  LocalEstoque = new LocalEstoque
                                                                                                                  {
                                                                                                                      LojaId = pcte.LocalEstoque.LojaId
                                                                                                                  }
                                                                                                              }).ToList()
                                                 }).OrderBy(c => c.Tamanho.SequenciaOrdenacao)
                                                 .ToList(),
                         }).OrderBy(c => c.Cor.Descricao)
                         .ToList(),
                    ProdutoPrecoLojas = p.ProdutoPrecoLojas.Select(pcl => new ProdutoPrecoLoja
                    {
                        Id = pcl.Id,
                        LojaId = pcl.LojaId,
                        Loja = new Loja
                        {
                            RazaoSocial = pcl.Loja.RazaoSocial,
                        },
                        ProdutoId = pcl.ProdutoId,
                        PrecoVenda = pcl.PrecoVenda,
                        PrecoCusto = pcl.PrecoCusto,
                        PrecoCompra = pcl.PrecoCompra,
                        Markup = pcl.Markup,
                    }).ToList(),
                    TabelaPrecoProdutos = p.TabelaPrecoProdutos.Select(tp => new TabelaPrecoProduto
                    {
                        TabelaPrecoId = tp.TabelaPrecoId,
                        PrecoVenda = tp.PrecoVenda
                    }).ToList(),
                    ProdutoEcommerces = p.ProdutoEcommerces.Select(pe => new ProdutoEcommerce
                    {
                        Titulo = pe.Titulo,
                        Descricao = pe.Descricao,
                        Anunciado = pe.Anunciado,
                        IdentificacaoIntegracao = pe.IdentificacaoIntegracao,
                        ItensInclusos = pe.ItensInclusos,
                        DisponibilidadeEntrega = pe.DisponibilidadeEntrega,
                        TempoGarantia = pe.TempoGarantia,
                        Campo1 = pe.Campo1,
                        Campo2 = pe.Campo2,
                        Referencia = pe.Referencia
                    }).ToList()
                })
                .ToListAsync();
        }

        public GridPaginadaRetorno<Produto> ObterListaProdutoIntegracaoPaginadoCompleto(
            Guid lojaId,
            GridPaginadaConsulta gridPaginadaConsulta,
            IdentificacaoIntegracao identificacaoIntegracao,
            Guid localEstoqueId)
        {
            var gridPaginadaRetorno = new GridPaginadaRetorno<Produto>();

            var query = DbSet
                .Where(x => x.Ativo == true &&
                            x.ProdutoEcommerces != null &&
                            x.ProdutoEcommerces.FirstOrDefault(c => c.IdentificacaoIntegracao == identificacaoIntegracao).Anunciado)
                .Select(p => new Produto()
                {
                    Id = p.Id,
                    Nome = p.Nome,
                    NomeAbreviado = p.NomeAbreviado,
                    TipoProduto = p.TipoProduto,
                    Ativo = p.Ativo,
                    Foto = p.Foto,
                    Referencia = p.Referencia,
                    SkuIdentificador = p.SkuIdentificador,
                    CodigoNcm = p.CodigoNcm,
                    VenderEcommerce = p.VenderEcommerce,
                    Marca = new Marca()
                    {
                        Id = p.Marca.Id,
                        Nome = p.Marca.Nome
                    },
                    CategoriaProduto = new CategoriaProduto()
                    {
                        Id = p.CategoriaProduto == null ? Guid.Empty : p.CategoriaProduto.Id,
                        Nome = p.CategoriaProduto.Nome,
                        CategoriaProdutoPai = new CategoriaProduto()
                        {
                            Id = p.CategoriaProduto.CategoriaProdutoPai == null ? Guid.Empty : p.CategoriaProduto.CategoriaProdutoPai.Id,
                            Nome = p.CategoriaProduto.CategoriaProdutoPai.Nome,
                            CategoriaProdutoPai = new CategoriaProduto()
                            {
                                Id = p.CategoriaProduto.CategoriaProdutoPai.CategoriaProdutoPai == null ? Guid.Empty : p.CategoriaProduto.CategoriaProdutoPai.CategoriaProdutoPai.Id,
                                Nome = p.CategoriaProduto.CategoriaProdutoPai.CategoriaProdutoPai.Nome,
                                CategoriaProdutoPai = new CategoriaProduto()
                                {
                                    Id = p.CategoriaProduto.CategoriaProdutoPai.CategoriaProdutoPai.CategoriaProdutoPai == null ? Guid.Empty : p.CategoriaProduto.CategoriaProdutoPai.CategoriaProdutoPai.CategoriaProdutoPai.Id,
                                    Nome = p.CategoriaProduto.CategoriaProdutoPai.CategoriaProdutoPai.CategoriaProdutoPai.Nome
                                }
                            }
                        }
                    },
                    ProdutoCores = p.ProdutoCores
                            .Where(pc => (p.ProdutoCores.Count == 1 || !pc.Cor.PadraoSistema))
                         .Select(pc => new ProdutoCor()
                         {
                             CorId = pc.CorId,
                             Ativo = pc.Ativo,
                             Cor = new Cor()
                             {
                                 Id = pc.Cor.Id,
                                 Descricao = pc.Cor.Descricao,
                                 PadraoSistema = pc.Cor.PadraoSistema
                             },
                             ProdutoCorImagens = pc.ProdutoCorImagens
                                 .Select(i => new ProdutoCorImagem()
                                 {
                                     Id = i.Id,
                                     Imagem = i.Imagem,
                                     SequenciaOrdenacao = i.SequenciaOrdenacao,
                                     Principal = i.Principal
                                 })
                                 .ToList(),
                             ProdutoCorTamanhos = pc.ProdutoCorTamanhos
                                                  .Where(pct => pc.ProdutoCorTamanhos.Count == 1 || !pct.Tamanho.PadraoSistema)
                                                 .Select(pct => new ProdutoCorTamanho()
                                                 {
                                                     Id = pct.Id,
                                                     Ativo = pc.Ativo,
                                                     TamanhoId = pct.TamanhoId,
                                                     Tamanho = new Tamanho()
                                                     {
                                                         Id = pct.Tamanho.Id,
                                                         Descricao = pct.Tamanho.Descricao,
                                                         PadraoSistema = pct.Tamanho.PadraoSistema,
                                                         SequenciaOrdenacao = pct.Tamanho.SequenciaOrdenacao
                                                     },
                                                     CodigoGTINEAN = pct.CodigoGTINEAN,
                                                     Altura = pct.Altura,
                                                     Largura = pct.Largura,
                                                     Profundidade = pct.Profundidade,
                                                     PesoLiquido = pct.PesoLiquido,
                                                     PesoBruto = pct.PesoBruto,
                                                     PesoEmbalagem = pct.PesoEmbalagem,
                                                     EstoqueMinimo = pct.EstoqueMinimo,
                                                     SKU = pct.SKU,
                                                     CodigoBarrasInterno = pct.CodigoBarrasInterno,
                                                     CodigoExterno = pct.CodigoExterno,
                                                     ProdutoCorTamanhoEstoques = pct.ProdutoCorTamanhoEstoques.Where(x => x.LocalEstoqueId == localEstoqueId)
                                                                                                              .Select(pcte => new ProdutoCorTamanhoEstoque
                                                                                                              {
                                                                                                                  EstoqueAtual = pcte.EstoqueAtual,
                                                                                                                  LocalEstoque = new LocalEstoque
                                                                                                                  {
                                                                                                                      LojaId = pcte.LocalEstoque.LojaId
                                                                                                                  }
                                                                                                              }).ToList()
                                                 })
                                                 .ToList(),
                         })
                         .ToList(),
                    ProdutoPrecoLojas = p.ProdutoPrecoLojas.Select(pcl => new ProdutoPrecoLoja
                    {
                        Id = pcl.Id,
                        LojaId = pcl.LojaId,
                        Loja = new Loja
                        {
                            RazaoSocial = pcl.Loja.RazaoSocial,
                        },
                        ProdutoId = pcl.ProdutoId,
                        PrecoVenda = pcl.PrecoVenda,
                        PrecoCusto = pcl.PrecoCusto,
                        PrecoCompra = pcl.PrecoCompra,
                        Markup = pcl.Markup,
                    }).ToList(),
                    TabelaPrecoProdutos = p.TabelaPrecoProdutos.Select(tp => new TabelaPrecoProduto
                    {
                        TabelaPrecoId = tp.TabelaPrecoId,
                        PrecoVenda = tp.PrecoVenda
                    }).ToList(),
                    ProdutoEcommerces = p.ProdutoEcommerces.Select(pe => new ProdutoEcommerce
                    {
                        Titulo = pe.Titulo,
                        Descricao = pe.Descricao,
                        Anunciado = pe.Anunciado,
                        IdentificacaoIntegracao = pe.IdentificacaoIntegracao,
                        ItensInclusos = pe.ItensInclusos,
                        DisponibilidadeEntrega = pe.DisponibilidadeEntrega,
                        TempoGarantia = pe.TempoGarantia,
                        Campo1 = pe.Campo1,
                        Campo2 = pe.Campo2,
                        Referencia = pe.Referencia
                    }).ToList()

                });

            gridPaginadaRetorno.CarregarPaginacao(query, gridPaginadaConsulta);

            return gridPaginadaRetorno;
        }

        public async Task<Dictionary<int, Guid>> ObterListaIdPorSkuIdentificador(
            List<string> listaSkuIdentificador)
        {
            return await DbSet
                            .Where(x => listaSkuIdentificador.Contains(x.SkuIdentificador.ToString()))
                            .Select(x => new Produto
                            {
                                Id = x.Id,
                                SkuIdentificador = x.SkuIdentificador
                            })
                            .ToDictionaryAsync(x => x.SkuIdentificador, x => x.Id);
        }

        #endregion

        #region Pdv Offline/Frente Caixa

        #region Produto

        public async Task<Produto> ObterCompletoPorIdELojaId(
            Guid id,
            Guid lojaId)
        {
            return await DbSet
                .Where(c => c.Id == id)
                .Select(p => new Produto()
                {
                    Id = p.Id,
                    DataHoraCadastro = p.DataHoraCadastro,
                    DataHoraUltimaAlteracao = p.DataHoraUltimaAlteracao,
                    Ativo = p.Ativo,
                    TipoProduto = p.TipoProduto,
                    Nome = p.Nome,
                    NomeAbreviado = p.NomeAbreviado,
                    Referencia = p.Referencia,
                    SkuIdentificador = p.SkuIdentificador,
                    Foto = p.Foto,
                    CategoriaProdutoId = p.CategoriaProdutoId,
                    CategoriaProduto = new CategoriaProduto()
                    {
                        Id = p.CategoriaProduto == null ? Guid.Empty : p.CategoriaProduto.Id,
                        Nome = p.CategoriaProduto.Nome,
                        CategoriaProdutoPai = new CategoriaProduto()
                        {
                            Id = p.CategoriaProduto.CategoriaProdutoPai == null ? Guid.Empty : p.CategoriaProduto.CategoriaProdutoPai.Id,
                            Nome = p.CategoriaProduto.CategoriaProdutoPai.Nome,
                            CategoriaProdutoPai = new CategoriaProduto()
                            {
                                Id = p.CategoriaProduto.CategoriaProdutoPai.CategoriaProdutoPai == null ? Guid.Empty : p.CategoriaProduto.CategoriaProdutoPai.CategoriaProdutoPai.Id,
                                Nome = p.CategoriaProduto.CategoriaProdutoPai.CategoriaProdutoPai.Nome,
                                CategoriaProdutoPai = new CategoriaProduto()
                                {
                                    Id = p.CategoriaProduto.CategoriaProdutoPai.CategoriaProdutoPai.CategoriaProdutoPai == null ? Guid.Empty : p.CategoriaProduto.CategoriaProdutoPai.CategoriaProdutoPai.CategoriaProdutoPai.Id,
                                    Nome = p.CategoriaProduto.CategoriaProdutoPai.CategoriaProdutoPai.CategoriaProdutoPai.Nome
                                }
                            }
                        }
                    },
                    MarcaId = p.MarcaId,
                    Marca = new Marca()
                    {
                        Id = p.Marca.Id,
                        Nome = p.Marca.Nome
                    },
                    UnidadeMedidaId = p.UnidadeMedidaId,
                    RegraFiscalId = p.RegraFiscalId,
                    IcmsStRetidoBaseCalculo = p.IcmsStRetidoBaseCalculo,
                    IcmsStRetidoValor = p.IcmsStRetidoValor,
                    FcpStRetidoBaseCalculo = p.FcpStRetidoBaseCalculo,
                    FcpStRetidoValor = p.FcpStRetidoValor,
                    IcmsAliquota = p.IcmsAliquota,
                    PisAliquota = p.PisAliquota,
                    CofinsAliquota = p.CofinsAliquota,
                    FcpAliquota = p.FcpAliquota,
                    IcmsReducaoBaseCalculo = p.IcmsReducaoBaseCalculo,
                    CodigoBeneficioFiscal = p.CodigoBeneficioFiscal,
                    UnidadeTributavelId = p.UnidadeTributavelId,
                    QtdeConversao = p.QtdeConversao,
                    FatorConversao = p.FatorConversao,
                    CNPJFabricante = p.CNPJFabricante,
                    IndicadorEscalaRelevante = p.IndicadorEscalaRelevante,
                    CodigoAnp = p.CodigoAnp,
                    CODIF = p.CODIF,
                    PercentualGLP = p.PercentualGLP,
                    PercentualGasNacional = p.PercentualGasNacional,
                    PercentualGasImportado = p.PercentualGasImportado,
                    ValorPartidaGLP = p.ValorPartidaGLP,
                    AliquotaAdREmICMSRetido = p.AliquotaAdREmICMSRetido,
                    QuantidadeBCMonoRetido = p.QuantidadeBCMonoRetido,
                    CodigoNcm = p.CodigoNcm,
                    CodigoCest = p.CodigoCest,
                    CstOrigem = p.CstOrigem,
                    TipoProdutoFiscal = p.TipoProdutoFiscal,
                    ControlaEstoque = p.ControlaEstoque,
                    PermiteAlteraValorNaVenda = p.PermiteAlteraValorNaVenda,
                    UtilizarBalanca = p.UtilizarBalanca,
                    ExportarBalanca = p.ExportarBalanca,
                    SolicitarInformacaoComplementarNoPdv = p.SolicitarInformacaoComplementarNoPdv,
                    VenderEcommerce = p.VenderEcommerce,
                    ProdutoCores = p.ProdutoCores
                                   .Select(pc => new ProdutoCor()
                                   {
                                       CorId = pc.CorId,
                                       Ativo = pc.Ativo,
                                       Cor = new Cor
                                       {
                                           Id = pc.Cor.Id,
                                           Descricao = pc.Cor.Descricao,
                                           PadraoSistema = pc.Cor.PadraoSistema
                                       },
                                       ProdutoCorImagens = pc.ProdutoCorImagens
                                            .Select(i => new ProdutoCorImagem()
                                            {
                                                Id = i.Id,
                                                Imagem = i.Imagem,
                                                SequenciaOrdenacao = i.SequenciaOrdenacao,
                                                Principal = i.Principal
                                            })
                                            .ToList(),
                                       ProdutoCorTamanhos = pc.ProdutoCorTamanhos
                                           .Where(pct => (pct.ProdutoCor.Produto.TipoProduto != TipoProduto.PRODUTO_VARIACAO ||
                                                         (pct.ProdutoCor.Produto.TipoProduto == TipoProduto.PRODUTO_VARIACAO &&
                                                         (pct.ProdutoCor.Produto.ProdutoCores.Count() == 1 || !pct.ProdutoCor.Cor.PadraoSistema) &&
                                                         (pct.ProdutoCor.ProdutoCorTamanhos.Count() == 1 || !pct.Tamanho.PadraoSistema))))
                                           .Select(pct => new ProdutoCorTamanho()
                                           {
                                               Id = pct.Id,
                                               Ativo = pc.Ativo,
                                               TamanhoId = pct.TamanhoId,
                                               Tamanho = new Tamanho()
                                               {
                                                   Id = pct.Tamanho.Id,
                                                   Descricao = pct.Tamanho.Descricao,
                                                   PadraoSistema = pct.Tamanho.PadraoSistema,
                                                   SequenciaOrdenacao = pct.Tamanho.SequenciaOrdenacao
                                               },
                                               CodigoGTINEAN = pct.CodigoGTINEAN,
                                               Altura = pct.Altura,
                                               Largura = pct.Largura,
                                               Profundidade = pct.Profundidade,
                                               PesoLiquido = pct.PesoLiquido,
                                               PesoBruto = pct.PesoBruto,
                                               PesoEmbalagem = pct.PesoEmbalagem,
                                               EstoqueMinimo = pct.EstoqueMinimo,
                                               SKU = pct.SKU,
                                               CodigoBarrasInterno = pct.CodigoBarrasInterno,
                                               CodigoExterno = pct.CodigoExterno,
                                               ProdutoCorTamanhoEstoques = pct.ProdutoCorTamanhoEstoques.Where(x => x.LocalEstoque.LojaId == lojaId)
                                                                                                        .Select(pcte => new ProdutoCorTamanhoEstoque
                                                                                                        {
                                                                                                            EstoqueAtual = pcte.EstoqueAtual,
                                                                                                            LocalEstoque = new LocalEstoque
                                                                                                            {
                                                                                                                LojaId = pcte.LocalEstoque.LojaId
                                                                                                            }
                                                                                                        }).ToList(),
                                           })
                                           .ToList(),
                                   })
                                   .ToList(),
                    ProdutoPrecoLojas = p.ProdutoPrecoLojas
                                   .Select(pcl => new ProdutoPrecoLoja
                                   {
                                       Id = pcl.Id,
                                       LojaId = pcl.LojaId,
                                       Loja = new Loja
                                       {
                                           RazaoSocial = pcl.Loja.RazaoSocial,
                                       },
                                       ProdutoId = pcl.ProdutoId,
                                       PrecoVenda = pcl.PrecoVenda,
                                       PrecoCusto = pcl.PrecoCusto,
                                       PrecoCompra = pcl.PrecoCompra,
                                       Markup = pcl.Markup
                                   })
                                   .ToList(),
                    TabelaPrecoProdutos = p.TabelaPrecoProdutos
                                   .Select(tp => new TabelaPrecoProduto
                                   {
                                       TabelaPrecoId = tp.TabelaPrecoId,
                                       PrecoVenda = tp.PrecoVenda
                                   })
                                   .ToList(),
                    ProdutoEcommerces = p.ProdutoEcommerces
                                   .Select(pe => new ProdutoEcommerce
                                   {
                                       Titulo = pe.Titulo,
                                       Descricao = pe.Descricao,
                                       Anunciado = pe.Anunciado,
                                       IdentificacaoIntegracao = pe.IdentificacaoIntegracao,
                                       ItensInclusos = pe.ItensInclusos,
                                       DisponibilidadeEntrega = pe.DisponibilidadeEntrega,
                                       TempoGarantia = pe.TempoGarantia,
                                       Campo1 = pe.Campo1,
                                       Campo2 = pe.Campo2,
                                       Referencia = pe.Referencia
                                   })
                                   .ToList(),
                    ProdutoRegraFiscalExcecoes = p.ProdutoRegraFiscalExcecoes
                                   .Select(e => new ProdutoRegraFiscalExcecao
                                   {
                                       EstadoDestinoId = e.EstadoDestinoId,
                                       EstadoOrigemId = e.EstadoOrigemId,
                                       CodigoBeneficioFiscal = e.CodigoBeneficioFiscal,
                                       AliquotaIcms = e.AliquotaIcms,
                                       PorcentagemFCP = e.PorcentagemFCP,
                                       ReducaoBaseCalculo = e.ReducaoBaseCalculo
                                   })
                                   .ToList(),
                    TagProdutos = p.TagProdutos
                                   .Select(t => new TagProduto
                                   {
                                       TagId = t.TagId
                                   })
                                   .ToList(),
                    CampoPersonalizadoProduto = p.CampoPersonalizadoProduto
                                   .Select(cp => new CampoPersonalizadoProduto
                                   {
                                       CampoPersonalizadoId = cp.CampoPersonalizadoId,
                                       Valor = cp.Valor
                                   })
                                   .ToList(),
                    GerenciadoresDeImpressao = p.GerenciadoresDeImpressao
                                   .Select(gi => new ProdutoGerenciadorImpressao
                                   {
                                       GerenciadorImpressao = gi.GerenciadorImpressao
                                   })
                                   .ToList(),
                    UsarComoComplemento = p.UsarComoComplemento,
                    ProdutoCombo = p.ProdutoCombo,
                    CobrarTaxaServico = p.CobrarTaxaServico,
                    BaixarSaldoMateriaPrima = p.BaixarSaldoMateriaPrima,
                    ComposicaoProduto = p.ComposicaoProduto,
                    DiasParaValidade = p.DiasParaValidade,
                    ImagemCardapio = p.ImagemCardapio,
                    ImagemDestaque = p.ImagemDestaque,
                    PrecoCombo = p.PrecoCombo,
                    UtilizarPrecoDosItensEtapa = p.UtilizarPrecoDosItensEtapa
                })
                .FirstOrDefaultAsync();
        }

        public GridPaginadaRetorno<Produto> ObterListaPaginadaCompleta(
           Guid lojaId,
           GridPaginadaConsulta gridPaginadaConsulta,
           DateTime? dataAtualizacao)
        {
            var gridPaginadaRetorno = new GridPaginadaRetorno<Produto>();

            var query = DbSet.Where(x => (dataAtualizacao == null) || x.DataHoraUltimaAlteracao >= dataAtualizacao)
                             .Select(p => new Produto()
                             {
                                 Id = p.Id,
                                 DataHoraCadastro = p.DataHoraCadastro,
                                 DataHoraUltimaAlteracao = p.DataHoraUltimaAlteracao,
                                 Ativo = p.Ativo,
                                 TipoProduto = p.TipoProduto,
                                 Nome = p.Nome,
                                 NomeAbreviado = p.NomeAbreviado,
                                 Referencia = p.Referencia,
                                 SkuIdentificador = p.SkuIdentificador,
                                 CodigoIntegracao = p.CodigoIntegracao,
                                 Foto = p.Foto,
                                 CategoriaProdutoId = p.CategoriaProdutoId,
                                 CategoriaProduto = new CategoriaProduto()
                                 {
                                     Id = p.CategoriaProduto == null ? Guid.Empty : p.CategoriaProduto.Id,
                                     Nome = p.CategoriaProduto.Nome,
                                     CategoriaProdutoPai = new CategoriaProduto()
                                     {
                                         Id = p.CategoriaProduto.CategoriaProdutoPai == null ? Guid.Empty : p.CategoriaProduto.CategoriaProdutoPai.Id,
                                         Nome = p.CategoriaProduto.CategoriaProdutoPai.Nome,
                                         CategoriaProdutoPai = new CategoriaProduto()
                                         {
                                             Id = p.CategoriaProduto.CategoriaProdutoPai.CategoriaProdutoPai == null ? Guid.Empty : p.CategoriaProduto.CategoriaProdutoPai.CategoriaProdutoPai.Id,
                                             Nome = p.CategoriaProduto.CategoriaProdutoPai.CategoriaProdutoPai.Nome,
                                             CategoriaProdutoPai = new CategoriaProduto()
                                             {
                                                 Id = p.CategoriaProduto.CategoriaProdutoPai.CategoriaProdutoPai.CategoriaProdutoPai == null ? Guid.Empty : p.CategoriaProduto.CategoriaProdutoPai.CategoriaProdutoPai.CategoriaProdutoPai.Id,
                                                 Nome = p.CategoriaProduto.CategoriaProdutoPai.CategoriaProdutoPai.CategoriaProdutoPai.Nome
                                             }
                                         }
                                     }
                                 },
                                 MarcaId = p.MarcaId,
                                 Marca = new Marca()
                                 {
                                     Id = p.Marca.Id,
                                     Nome = p.Marca.Nome
                                 },
                                 UnidadeMedidaId = p.UnidadeMedidaId,
                                 RegraFiscalId = p.RegraFiscalId,
                                 IcmsStRetidoBaseCalculo = p.IcmsStRetidoBaseCalculo,
                                 IcmsStRetidoValor = p.IcmsStRetidoValor,
                                 FcpStRetidoBaseCalculo = p.FcpStRetidoBaseCalculo,
                                 FcpStRetidoValor = p.FcpStRetidoValor,
                                 IcmsAliquota = p.IcmsAliquota,
                                 PisAliquota = p.PisAliquota,
                                 CofinsAliquota = p.CofinsAliquota,
                                 FcpAliquota = p.FcpAliquota,
                                 IcmsReducaoBaseCalculo = p.IcmsReducaoBaseCalculo,
                                 CodigoBeneficioFiscal = p.CodigoBeneficioFiscal,
                                 UnidadeTributavelId = p.UnidadeTributavelId,
                                 QtdeConversao = p.QtdeConversao,
                                 FatorConversao = p.FatorConversao,
                                 CNPJFabricante = p.CNPJFabricante,
                                 IndicadorEscalaRelevante = p.IndicadorEscalaRelevante,
                                 CodigoAnp = p.CodigoAnp,
                                 CODIF = p.CODIF,
                                 PercentualGLP = p.PercentualGLP,
                                 PercentualGasNacional = p.PercentualGasNacional,
                                 PercentualGasImportado = p.PercentualGasImportado,
                                 ValorPartidaGLP = p.ValorPartidaGLP,
                                 AliquotaAdREmICMSRetido = p.AliquotaAdREmICMSRetido,
                                 QuantidadeBCMonoRetido = p.QuantidadeBCMonoRetido,
                                 CodigoNcm = p.CodigoNcm,
                                 CodigoCest = p.CodigoCest,
                                 CstOrigem = p.CstOrigem,
                                 TipoProdutoFiscal = p.TipoProdutoFiscal,
                                 ControlaEstoque = p.ControlaEstoque,
                                 PermiteAlteraValorNaVenda = p.PermiteAlteraValorNaVenda,
                                 UtilizarBalanca = p.UtilizarBalanca,
                                 ExportarBalanca = p.ExportarBalanca,
                                 SolicitarInformacaoComplementarNoPdv = p.SolicitarInformacaoComplementarNoPdv,
                                 VenderEcommerce = p.VenderEcommerce,
                                 ProdutoCores = p.ProdutoCores
                                                    .Select(pc => new ProdutoCor()
                                                    {
                                                        CorId = pc.CorId,
                                                        Ativo = pc.Ativo,
                                                        Cor = new Cor
                                                        {
                                                            Id = pc.Cor.Id,
                                                            Descricao = pc.Cor.Descricao,
                                                            PadraoSistema = pc.Cor.PadraoSistema
                                                        },
                                                        ProdutoCorImagens = pc.ProdutoCorImagens
                                                             .Select(i => new ProdutoCorImagem()
                                                             {
                                                                 Id = i.Id,
                                                                 Imagem = i.Imagem,
                                                                 SequenciaOrdenacao = i.SequenciaOrdenacao,
                                                                 Principal = i.Principal
                                                             })
                                                             .ToList(),
                                                        ProdutoCorTamanhos = pc.ProdutoCorTamanhos
                                                            .Where(pct => (pct.ProdutoCor.Produto.TipoProduto != TipoProduto.PRODUTO_VARIACAO ||
                                                                          (pct.ProdutoCor.Produto.TipoProduto == TipoProduto.PRODUTO_VARIACAO &&
                                                                          (pct.ProdutoCor.Produto.ProdutoCores.Count() == 1 || !pct.ProdutoCor.Cor.PadraoSistema) &&
                                                                          (pct.ProdutoCor.ProdutoCorTamanhos.Count() == 1 || !pct.Tamanho.PadraoSistema))))
                                                            .Select(pct => new ProdutoCorTamanho()
                                                            {
                                                                Id = pct.Id,
                                                                Ativo = pc.Ativo,
                                                                TamanhoId = pct.TamanhoId,
                                                                Tamanho = new Tamanho()
                                                                {
                                                                    Id = pct.Tamanho.Id,
                                                                    Descricao = pct.Tamanho.Descricao,
                                                                    PadraoSistema = pct.Tamanho.PadraoSistema,
                                                                    SequenciaOrdenacao = pct.Tamanho.SequenciaOrdenacao
                                                                },
                                                                CodigoGTINEAN = pct.CodigoGTINEAN,
                                                                Altura = pct.Altura,
                                                                Largura = pct.Largura,
                                                                Profundidade = pct.Profundidade,
                                                                PesoLiquido = pct.PesoLiquido,
                                                                PesoBruto = pct.PesoBruto,
                                                                PesoEmbalagem = pct.PesoEmbalagem,
                                                                EstoqueMinimo = pct.EstoqueMinimo,
                                                                SKU = pct.SKU,
                                                                CodigoBarrasInterno = pct.CodigoBarrasInterno,
                                                                CodigoExterno = pct.CodigoExterno,
                                                                ProdutoCorTamanhoEstoques = pct.ProdutoCorTamanhoEstoques.Where(x => x.LocalEstoque.LojaId == lojaId)
                                                                                                                         .Select(pcte => new ProdutoCorTamanhoEstoque
                                                                                                                         {
                                                                                                                             EstoqueAtual = pcte.EstoqueAtual,
                                                                                                                             LocalEstoque = new LocalEstoque
                                                                                                                             {
                                                                                                                                 LojaId = pcte.LocalEstoque.LojaId
                                                                                                                             }
                                                                                                                         }).ToList(),
                                                            })
                                                            .ToList(),
                                                    })
                                                    .ToList(),
                                 ProdutoPrecoLojas = p.ProdutoPrecoLojas
                                                    .Select(pcl => new ProdutoPrecoLoja
                                                    {
                                                        Id = pcl.Id,
                                                        LojaId = pcl.LojaId,
                                                        Loja = new Loja
                                                        {
                                                            RazaoSocial = pcl.Loja.RazaoSocial,
                                                        },
                                                        ProdutoId = pcl.ProdutoId,
                                                        PrecoVenda = pcl.PrecoVenda,
                                                        PrecoCusto = pcl.PrecoCusto,
                                                        PrecoCompra = pcl.PrecoCompra,
                                                        Markup = pcl.Markup
                                                    })
                                                    .ToList(),
                                 TabelaPrecoProdutos = p.TabelaPrecoProdutos
                                                    .Select(tp => new TabelaPrecoProduto
                                                    {
                                                        TabelaPrecoId = tp.TabelaPrecoId,
                                                        PrecoVenda = tp.PrecoVenda
                                                    })
                                                    .ToList(),
                                 ProdutoEcommerces = p.ProdutoEcommerces
                                                    .Select(pe => new ProdutoEcommerce
                                                    {
                                                        Titulo = pe.Titulo,
                                                        Descricao = pe.Descricao,
                                                        Anunciado = pe.Anunciado,
                                                        IdentificacaoIntegracao = pe.IdentificacaoIntegracao,
                                                        ItensInclusos = pe.ItensInclusos,
                                                        DisponibilidadeEntrega = pe.DisponibilidadeEntrega,
                                                        TempoGarantia = pe.TempoGarantia,
                                                        Campo1 = pe.Campo1,
                                                        Campo2 = pe.Campo2,
                                                        Referencia = pe.Referencia
                                                    })
                                                    .ToList(),
                                 ProdutoRegraFiscalExcecoes = p.ProdutoRegraFiscalExcecoes
                                                    .Select(e => new ProdutoRegraFiscalExcecao
                                                    {
                                                        EstadoDestinoId = e.EstadoDestinoId,
                                                        EstadoOrigemId = e.EstadoOrigemId,
                                                        CodigoBeneficioFiscal = e.CodigoBeneficioFiscal,
                                                        AliquotaIcms = e.AliquotaIcms,
                                                        PorcentagemFCP = e.PorcentagemFCP,
                                                        ReducaoBaseCalculo = e.ReducaoBaseCalculo
                                                    })
                                                    .ToList(),
                                 TagProdutos = p.TagProdutos
                                                    .Select(t => new TagProduto
                                                    {
                                                        TagId = t.TagId
                                                    })
                                                    .ToList(),
                                 CampoPersonalizadoProduto = p.CampoPersonalizadoProduto
                                                    .Select(cp => new CampoPersonalizadoProduto
                                                    {
                                                        CampoPersonalizadoId = cp.CampoPersonalizadoId,
                                                        Valor = cp.Valor
                                                    })
                                                    .ToList(),
                                 GerenciadoresDeImpressao = p.GerenciadoresDeImpressao
                                                    .Where(p => p.GerenciadorImpressao.LojaId == lojaId)
                                                    .Select(gi => new ProdutoGerenciadorImpressao
                                                    {
                                                        GerenciadorImpressao = gi.GerenciadorImpressao
                                                    })
                                                    .ToList(),
                                 UsarComoComplemento = p.UsarComoComplemento,
                                 ProdutoCombo = p.ProdutoCombo,
                                 CobrarTaxaServico = p.CobrarTaxaServico,
                                 BaixarSaldoMateriaPrima = p.BaixarSaldoMateriaPrima,
                                 ComposicaoProduto = p.ComposicaoProduto,
                                 DiasParaValidade = p.DiasParaValidade,
                                 ImagemCardapio = p.ImagemCardapio,
                                 ImagemDestaque = p.ImagemDestaque,
                                 PrecoCombo = p.PrecoCombo,
                                 UtilizarPrecoDosItensEtapa = p.UtilizarPrecoDosItensEtapa
                             });

            gridPaginadaRetorno.CarregarPaginacao(query, gridPaginadaConsulta);

            return gridPaginadaRetorno;
        }

        #endregion

        #region Cst

        public async Task<List<OrigemMercadoria>> ObterListaCstOrigem()
        {
            return await DbSet
                .Select(p => p.CstOrigem)
                .Distinct()
                .ToListAsync();
        }

        #endregion

        #region Icms

        public async Task<Produto> ObterAliquotaIcmsCompletoPorProdutoId(
            Guid id)
        {
            return await DbSet.Where(c => c.Id == id)
                              .Select(p => new Produto()
                              {
                                  IcmsAliquota = p.IcmsAliquota,
                                  IcmsReducaoBaseCalculo = p.IcmsReducaoBaseCalculo
                              })
                              .FirstOrDefaultAsync();
        }

        public async Task<List<Produto>> ObterListaAliquotaIcmsCompleto(
            DateTime? dataAtualizacao)
        {
            return await DbSet.Where(x => (dataAtualizacao == null) || x.DataHoraUltimaAlteracao >= dataAtualizacao)
                              .Select(p => new Produto()
                              {
                                  IcmsAliquota = p.IcmsAliquota,
                                  IcmsReducaoBaseCalculo = p.IcmsReducaoBaseCalculo
                              })
                              .Distinct()
                              .ToListAsync();
        }

        #endregion

        #region Fator

        public async Task<Produto> ObterFatorConversaoCompletoPorProdutoId(
            Guid id)
        {
            return await DbSet.Where(c => c.Id == id)
                              .Select(p => new Produto()
                              {
                                  UnidadeTributavelId = p.UnidadeTributavelId,
                                  FatorConversao = p.FatorConversao,
                                  QtdeConversao = p.QtdeConversao
                              })
                              .FirstOrDefaultAsync();
        }

        public async Task<List<Produto>> ObterListaFatorConversaoCompleto(
            DateTime? dataAtualizacao)
        {
            return await DbSet.Where(x => (dataAtualizacao == null) || x.DataHoraUltimaAlteracao >= dataAtualizacao)
                              .Select(p => new Produto()
                              {
                                  UnidadeTributavelId = p.UnidadeTributavelId,
                                  FatorConversao = p.FatorConversao,
                                  QtdeConversao = p.QtdeConversao
                              })
                              .Distinct()
                              .ToListAsync();
        }

        #endregion

        #endregion

        private IQueryable<Produto> FiltrarIdentificadoresProduto(
            string nomeReferenciaCodigoBarras)
        {
            var produtos = DbSet.AsQueryable();

            if (!string.IsNullOrEmpty(nomeReferenciaCodigoBarras))
            {
                (var codigoBarrasHexadecimal, var decimalValue) = CodigoBarrasReduzidoHelper.ValidarCodigoBarras(nomeReferenciaCodigoBarras);

                if (codigoBarrasHexadecimal)
                    produtos = produtos.Where(p => p.ProdutoCores.Any(pc => pc.ProdutoCorTamanhos.Any(pct => pct.SequenciaCodigoBarras == decimalValue)));
                else
                {
                    bool pesquisarGtinEan = false;

                    if (long.TryParse(nomeReferenciaCodigoBarras, out long codigoGtinEan))
                        pesquisarGtinEan = nomeReferenciaCodigoBarras.Length is 8 or 12 or 13 or 14;

                    bool pesquisarCodigoBarrasInterno = false;

                    if (long.TryParse(nomeReferenciaCodigoBarras, out long codigoBarrasInterno))
                        pesquisarCodigoBarrasInterno = nomeReferenciaCodigoBarras.Length is 13;

                    produtos = produtos.Where(p => EF.Functions.Collate(p.Nome, Collates.RemoverCaracteresEspeciaisAcentos).Contains(nomeReferenciaCodigoBarras) ||
                                                   EF.Functions.Collate(p.Referencia, Collates.RemoverCaracteresEspeciaisAcentos).Equals(nomeReferenciaCodigoBarras) ||
                                                   p.ProdutoCores.Any(c => c.ProdutoCorTamanhos.Any(t => t.SKU == nomeReferenciaCodigoBarras)) ||
                                                   p.ProdutoCores.Any(c => c.ProdutoCorTamanhos.Any(t => EF.Functions.Collate(t.CodigoExterno, Collates.RemoverCaracteresEspeciaisAcentos).Equals(nomeReferenciaCodigoBarras))) ||
                                                   (pesquisarGtinEan && p.ProdutoCores.Any(c => c.ProdutoCorTamanhos.Any(t => t.CodigoGTINEAN == nomeReferenciaCodigoBarras))) ||
                                                   (pesquisarCodigoBarrasInterno && p.ProdutoCores.Any(c => c.ProdutoCorTamanhos.Any(t => t.CodigoBarrasInterno == nomeReferenciaCodigoBarras))));
                }
            }

            return produtos;
        }

        public IQueryable<Produto> ListarProdutosFiltrosDetalhados(ProdutoDetalhadoFiltrosViewModel filtros)
        {
            var query = DbSet.AsQueryable()
                             .AsNoTracking();

            query = FiltrarPorTipoEstoque(query, filtros.TipoEstoque);
            query = query.Where(filtros.FiltarPorStatus);
            query = query.Where(filtros.FiltrarPorDescricao);
            query = query.Where(filtros.FiltrarPorCores);
            query = query.Where(filtros.FiltrarPorTamanhos);
            query = query.Where(filtros.FiltrarPorCategorias);
            query = query.Where(filtros.FiltrarPorMarcas);

            return query;
        }

        private IQueryable<Produto> FiltrarPorTipoEstoque(IQueryable<Produto> query, TipoFiltroProdutoEstoque tipoEstoque)
        {
            if (tipoEstoque == TipoFiltroProdutoEstoque.POSITIVO)
                return query.Where(x => x.ProdutoCores
                                .SelectMany(pc => pc.ProdutoCorTamanhos
                                    .SelectMany(pct => pct.ProdutoCorTamanhoEstoques
                                        .Select(pcte => pcte.EstoqueAtual))).Sum() > 0);

            if (tipoEstoque == TipoFiltroProdutoEstoque.NEGATIVO)
                return query.Where(x => x.ProdutoCores
                                .SelectMany(pc => pc.ProdutoCorTamanhos
                                    .SelectMany(pct => pct.ProdutoCorTamanhoEstoques
                                        .Select(pcte => pcte.EstoqueAtual))).Sum() < 0);

            if (tipoEstoque == TipoFiltroProdutoEstoque.ZERADO)
                return query.Where(x => x.ProdutoCores
                                .SelectMany(pc => pc.ProdutoCorTamanhos
                                    .SelectMany(pct => pct.ProdutoCorTamanhoEstoques
                                        .Select(pcte => pcte.EstoqueAtual))).Sum() == 0);

            if (tipoEstoque == TipoFiltroProdutoEstoque.ABAIXO_MINIMO)
                return query.Where(x => x.ProdutoCores
                                .SelectMany(pc => pc.ProdutoCorTamanhos
                                    .SelectMany(pct => pct.ProdutoCorTamanhoEstoques
                                        .Select(pcte => pcte.EstoqueAtual < pct.EstoqueMinimo))).Any());

            return query;
        }

        public async Task AtualizarProdutoDataHoraUltimaAlteracao(Guid produtoCorId)
        {
            var produto = await DbSet.FirstOrDefaultAsync(p => p.ProdutoCores.Any(pc => pc.Id == produtoCorId));
            if (produto is null)
                return;

            produto.DataHoraUltimaAlteracao = DateTime.UtcNow;

            await SaveChanges();
        }

        public async Task<List<Produto>> BuscarProdutosSubstituirValorEmMassa(string propName, object valorAtual, bool selecionados, Guid[] produtosIds)
        {
            var propertyType = typeof(Produto).GetProperty(propName).PropertyType;

            object parsedValue = ProdutoAlteracaoEmMassaExtensions.ConvertValue(valorAtual, propertyType);

            // Usar reflection para criar a lambda expression dinamicamente
            // (p => p.Campo == valorAtual)
            var parameter = Expression.Parameter(typeof(Produto), "p");
            var property = Expression.Property(parameter, propName);
            var constant = Expression.Constant(parsedValue, propertyType);
            var equality = Expression.Equal(property, constant);
            var lambda = Expression.Lambda<Func<Produto, bool>>(equality, parameter);

            var query = DbSet.Where(lambda);
            query = query.Where(p => selecionados ? produtosIds.Contains(p.Id) : !produtosIds.Contains(p.Id));

            return await query.ToListAsync();
        }
    }
}
