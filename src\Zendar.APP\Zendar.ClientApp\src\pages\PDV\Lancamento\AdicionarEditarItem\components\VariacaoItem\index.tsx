import { Flex, Td, Tr, Text, Box } from '@chakra-ui/react';
import { IoIosCheckmarkCircleOutline } from 'react-icons/io';

import { DecimalMask, moneyMask } from 'helpers/format/fieldsMasks';

type ProdutoCorTamanho = {
  produtoCorTamanhoId: string;
  produto: string;
  cor: string;
  tamanho: string;
  imagem: string;
};

type Identificadores = {
  codigoGTINEAN: string;
  sku: string;
  sequenciaCodigoBarras: string;
  sequenciaCodigoBarrasHexaDecimal: string;
  codigoBarrasInterno: string;
  codigoExterno: string;
};

export type VariacoesResponseProps = {
  precoVendaPadrao: number;
  precoVendaExclusivo: number;
  produtoCorTamanho: ProdutoCorTamanho;
  identificadores: Identificadores;
  estoque: number;
  ativo: boolean;
  padraoSistema: boolean;
  isChecked: boolean;
};

type VariacaoItemProps = {
  variacao: VariacoesResponseProps;
  casasDecimais: {
    casasDecimaisQuantidade: number;
  };
  toggleSelectVariacao: (variacao: VariacoesResponseProps) => void;
  variacaoSelecionada: VariacoesResponseProps | null;
  handleDoubleClickVariacao: (variacao: VariacoesResponseProps) => void;
  isFocused?: boolean;
  index?: number;
  setFocusedIndex?: (index: number) => void;
  setTabelaEstaFocada?: (focused: boolean) => void;
  containerTabelaRef?: React.RefObject<HTMLDivElement>;
};

export const VariacaoItem = ({
  variacao,
  casasDecimais,
  toggleSelectVariacao,
  variacaoSelecionada,
  handleDoubleClickVariacao,
  isFocused = false,
  index,
  setFocusedIndex,
  setTabelaEstaFocada,
  containerTabelaRef,
}: VariacaoItemProps) => {
  const isSelected =
    variacaoSelecionada?.produtoCorTamanho.produtoCorTamanhoId ===
    variacao?.produtoCorTamanho.produtoCorTamanhoId;

  const labelCor = variacao.produtoCorTamanho?.cor
    ? `${variacao.produtoCorTamanho?.cor} ${
        variacao.produtoCorTamanho?.tamanho ? '|' : ''
      }`
    : '';

  return (
    <>
      <Tr
        sx={{
          borderRadius: '6px',
          '& > td': {
            height: '64px',
            transition: 'all 0.2s',
            bg: isSelected ? 'purple.50' : isFocused ? 'gray.50' : '',
          },
        }}
        onClick={(e) => {
          e.stopPropagation();
          if (index !== undefined && setFocusedIndex) {
            setFocusedIndex(index);
          }
          if (setTabelaEstaFocada) {
            setTabelaEstaFocada(true);
          }
          if (containerTabelaRef?.current) {
            containerTabelaRef.current.focus();
          }
          toggleSelectVariacao(variacao);
        }}
        onDoubleClick={(e) => {
          e.stopPropagation();
          e.preventDefault();
          handleDoubleClickVariacao(variacao);
        }}
        cursor="pointer"
        bg={isSelected ? 'purple.50' : isFocused ? 'gray.50' : ''}
      >
        <Td fontWeight="bold">
          <Flex alignItems="center" fontWeight="semibold" color="black">
            <Text
              display="flex"
              alignItems="center"
              gap="12px"
              whiteSpace="nowrap"
              justifyContent="flex-start"
              pl={isSelected ? '0px' : '32px'}
            >
              {isSelected && (
                <IoIosCheckmarkCircleOutline color="#482ABC" fontSize="20px" />
              )}
              <Text color="teal.600" mr="5px">
                {labelCor}
              </Text>
              <Text color="pink.700">
                {variacao.produtoCorTamanho?.tamanho}
              </Text>
              {variacao.ativo === false && (
                <Flex
                  ml="5px"
                  borderRadius="16px"
                  color="white"
                  bg="red.500"
                  w="60px"
                  h="16px"
                  justifyContent="center"
                  alignItems="center"
                >
                  Inativo
                </Flex>
              )}
            </Text>
          </Flex>
        </Td>
        <Td isNumeric>
          <Flex justifyContent="flex-end">
            {moneyMask(
              variacao?.precoVendaExclusivo > 0
                ? variacao?.precoVendaExclusivo
                : variacao?.precoVendaPadrao,
              true
            )}
          </Flex>
        </Td>

        <Td isNumeric>
          {DecimalMask(
            variacao.estoque,
            casasDecimais.casasDecimaisQuantidade,
            casasDecimais.casasDecimaisQuantidade
          )}
        </Td>
      </Tr>
      <Box h="5px" />
    </>
  );
};
