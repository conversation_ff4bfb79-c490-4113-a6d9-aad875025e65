{
  "ConnectionStrings": {
    //DEV
    "DefaultConnection": "Data Source=tcp:sti3-dev.database.windows.net,1433;Initial Catalog=7BE9277E58264976A3BC8356CA81D4DD;User Id=admin-dev;Password=***************;MultipleActiveResultSets=true;",
    "MultiEmpresaConnection": "Data Source=tcp:sti3-dev.database.windows.net,1433;Initial Catalog=multiempresa;User Id=admin-dev;Password=***************;MultipleActiveResultSets=true;"

    //HOM
    //"DefaultConnection": "Data Source=tcp:zendar-hom.database.windows.net,1433;Initial Catalog=varejo;User Id=zendar-dev-admin;Password=***************;MultipleActiveResultSets=true;",
    //"MultiEmpresaConnection": "Data Source=tcp:zendar-dev.database.windows.net,1433;Initial Catalog=multiempresa;User Id=zendar-dev-admin;Password=***************;MultipleActiveResultSets=true;"

    //LOCALHOST
    //"DefaultConnection": "Data Source=tcp:localhost,1433;Initial Catalog=suporte;User Id=sa;Password=**********;MultipleActiveResultSets=true;",
    //"MultiEmpresaConnection": "Data Source=tcp:localhost,1433;Initial Catalog=stargate;User Id=sa;Password=**********;MultipleActiveResultSets=true;" 
  },
  "StorageSettings": {
    //DEV
    "App": "DefaultEndpointsProtocol=https;AccountName=zendarappdev;AccountKey=****************************************************************************************;BlobEndpoint=https://zendarappdev.blob.core.windows.net/;QueueEndpoint=https://zendarappdev.queue.core.windows.net/;TableEndpoint=https://zendarappdev.table.core.windows.net/;FileEndpoint=https://zendarappdev.file.core.windows.net/;",
    "ArquivosFiscais": "DefaultEndpointsProtocol=https;AccountName=zendararquivosfiscaisdev;AccountKey=****************************************************************************************;BlobEndpoint=https://zendararquivosfiscaisdev.blob.core.windows.net/;QueueEndpoint=https://zendararquivosfiscaisdev.queue.core.windows.net/;TableEndpoint=https://zendararquivosfiscaisdev.table.core.windows.net/;FileEndpoint=https://zendararquivosfiscaisdev.file.core.windows.net/;",
    "Certificados": "DefaultEndpointsProtocol=https;AccountName=zendarcertificadosdev;AccountKey=****************************************************************************************;BlobEndpoint=https://zendarcertificadosdev.blob.core.windows.net/;QueueEndpoint=https://zendarcertificadosdev.queue.core.windows.net/;TableEndpoint=https://zendarcertificadosdev.table.core.windows.net/;FileEndpoint=https://zendarcertificadosdev.file.core.windows.net/;",
    "Imagens": "DefaultEndpointsProtocol=https;AccountName=zendarimagensdev;AccountKey=****************************************************************************************;BlobEndpoint=https://zendarimagensdev.blob.core.windows.net/;QueueEndpoint=https://zendarimagensdev.queue.core.windows.net/;TableEndpoint=https://zendarimagensdev.table.core.windows.net/;FileEndpoint=https://zendarimagensdev.file.core.windows.net/;",
    "Danfes": "DefaultEndpointsProtocol=https;AccountName=zendardanfesdev;AccountKey=****************************************************************************************;BlobEndpoint=https://zendardanfesdev.blob.core.windows.net/;QueueEndpoint=https://zendardanfesdev.queue.core.windows.net/;TableEndpoint=https://zendardanfesdev.table.core.windows.net/;FileEndpoint=https://zendardanfesdev.file.core.windows.net/;",
    "Importacao": "DefaultEndpointsProtocol=https;AccountName=zendarimportacaodev;AccountKey=****************************************************************************************;BlobEndpoint=https://zendarimportacaodev.blob.core.windows.net/;QueueEndpoint=https://zendarimportacaodev.queue.core.windows.net/;TableEndpoint=https://zendarimportacaodev.table.core.windows.net/;FileEndpoint=https://zendarimportacaodev.file.core.windows.net/;",
    "Backup": "DefaultEndpointsProtocol=https;AccountName=zendarbackupdev;AccountKey=****************************************************************************************;BlobEndpoint=https://zendarbackupdev.blob.core.windows.net/;QueueEndpoint=https://zendarbackupdev.queue.core.windows.net/;TableEndpoint=https://zendarbackupdev.table.core.windows.net/;FileEndpoint=https://zendarbackupdev.file.core.windows.net/;",
    "Relatorios": "DefaultEndpointsProtocol=https;AccountName=zendarrelatoriosdev;AccountKey=****************************************************************************************;BlobEndpoint=https://zendarrelatoriosdev.blob.core.windows.net/;QueueEndpoint=https://zendarrelatoriosdev.queue.core.windows.net/;TableEndpoint=https://zendarrelatoriosdev.table.core.windows.net/;FileEndpoint=https://zendarrelatoriosdev.file.core.windows.net/;",
    "ArquivosTemporarios": "DefaultEndpointsProtocol=https;AccountName=zendararqtempdev;AccountKey=****************************************************************************************;BlobEndpoint=https://zendararqtempdev.blob.core.windows.net/;QueueEndpoint=https://zendararqtempdev.queue.core.windows.net/;TableEndpoint=https://zendararqtempdev.table.core.windows.net/;FileEndpoint=https://zendararqtempdev.file.core.windows.net/;"

    //HOM
    //"App": "DefaultEndpointsProtocol=https;AccountName=zendarapphom;AccountKey=****************************************************************************************;BlobEndpoint=https://zendarapphom.blob.core.windows.net/;QueueEndpoint=https://zendarapphom.queue.core.windows.net/;TableEndpoint=https://zendarapphom.table.core.windows.net/;FileEndpoint=https://zendarapphom.file.core.windows.net/;",
    //"ArquivosFiscais": "DefaultEndpointsProtocol=https;AccountName=zendararquivosfiscaishom;AccountKey=****************************************************************************************;BlobEndpoint=https://zendararquivosfiscaishom.blob.core.windows.net/;QueueEndpoint=https://zendararquivosfiscaishom.queue.core.windows.net/;TableEndpoint=https://zendararquivosfiscaishom.table.core.windows.net/;FileEndpoint=https://zendararquivosfiscaishom.file.core.windows.net/;",
    //"Certificados": "DefaultEndpointsProtocol=https;AccountName=zendarcertificadoshom;AccountKey=****************************************************************************************;BlobEndpoint=https://zendarcertificadoshom.blob.core.windows.net/;QueueEndpoint=https://zendarcertificadoshom.queue.core.windows.net/;TableEndpoint=https://zendarcertificadoshom.table.core.windows.net/;FileEndpoint=https://zendarcertificadoshom.file.core.windows.net/;",
    //"Imagens": "DefaultEndpointsProtocol=https;AccountName=zendarimagenshom;AccountKey=****************************************************************************************;BlobEndpoint=https://zendarimagenshom.blob.core.windows.net/;QueueEndpoint=https://zendarimagenshom.queue.core.windows.net/;TableEndpoint=https://zendarimagenshom.table.core.windows.net/;FileEndpoint=https://zendarimagenshom.file.core.windows.net/;",
    //"Danfes": "DefaultEndpointsProtocol=https;AccountName=zendardanfeshom;AccountKey=****************************************************************************************;BlobEndpoint=https://zendardanfeshom.blob.core.windows.net/;QueueEndpoint=https://zendardanfeshom.queue.core.windows.net/;TableEndpoint=https://zendardanfeshom.table.core.windows.net/;FileEndpoint=https://zendardanfeshom.file.core.windows.net/;",
    //"Importacao": "DefaultEndpointsProtocol=https;AccountName=zendarimportacaohom;AccountKey=****************************************************************************************;BlobEndpoint=https://zendarimportacaohom.blob.core.windows.net/;QueueEndpoint=https://zendarimportacaohom.queue.core.windows.net/;TableEndpoint=https://zendarimportacaohom.table.core.windows.net/;FileEndpoint=https://zendarimportacaohom.file.core.windows.net/;",
    //"Backup": "DefaultEndpointsProtocol=https;AccountName=zendarbackuphom;AccountKey=****************************************************************************************;BlobEndpoint=https://zendarbackuphom.blob.core.windows.net/;QueueEndpoint=https://zendarbackuphom.queue.core.windows.net/;TableEndpoint=https://zendarbackuphom.table.core.windows.net/;FileEndpoint=https://zendarbackuphom.file.core.windows.net/;",
    //"Relatorios": "DefaultEndpointsProtocol=https;AccountName=zendarrelatorioshom;AccountKey=****************************************************************************************;BlobEndpoint=https://zendarrelatorioshom.blob.core.windows.net/;QueueEndpoint=https://zendarrelatorioshom.queue.core.windows.net/;TableEndpoint=https://zendarrelatorioshom.table.core.windows.net/;FileEndpoint=https://zendarrelatorioshom.file.core.windows.net/;"
    //"ArquivosTemporarios": "DefaultEndpointsProtocol=https;AccountName=zendararqtemporarioshom;AccountKey=****************************************************************************************;BlobEndpoint=https://zendararqtemporarioshom.blob.core.windows.net/;QueueEndpoint=https://zendararqtemporarioshom.queue.core.windows.net/;TableEndpoint=https://zendararqtemporarioshom.table.core.windows.net/;FileEndpoint=https://zendararqtemporarioshom.file.core.windows.net/;"
  },
  "JwtSettings": {
    "Chave": "5P2Py9hgQPPVwCmfDmgkrSHmUkTT8LgKeFCnmLSSBgALbURm7JYBbgg53qpKXLKe678nUxwJLtBHysUVdASxA5PC3cq2f4r58yDuNRH4K8tZUfv9jBCWuYA87ZqY5MLNdrAe2e7kXHGkGBkhe6MLh5EZjvKMJd6SVHxMrNs5v6hhP8mXYbT4a8fuS2SLvVkxkpwrHGgTt3Uen23PEmUaPZb57ca7e7YZAhPHMLDynFKvPgjN5mraNkRD97hKdRFY",
    "ExpiracaoMinutos": 20,
    "Emissor": "Zendar-Dev",
    "ValidoEm": "https://zendar-dev-api.azurewebsites.net"
  },
  "TemporaryAccess": {
    "Chave": "H@McQfTjWnZr4u7x!A%D*F-JaNdRgUkXp2s5v8y/B?E(H+KbPeShVmYq3t6w9z$C",
    "ExpiracaoToken": 5
  },
  "LoginAplicacaoSettings": {
    "Login": "fomer-delivery",
    "Hash": "AQAAAAEAACcQAAAAEE/k8Lvchwf7XrEg0g6yN7hXiUZhzQvOA74my+vVyUztUP3reK30CHwJ5NHbuqaRow=="
  },
  "Logging": {
    "LogLevel": {
      "Default": "Error",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Error",
      "Microsoft.EntityFrameworkCore.Database.Command": "Debug" // Debug ou None - Log query do banco
      //"Hangfire": "Trace"
    }
  },
  "EmailSettings": {
    "EmailsLogErro": "",
    "Layout": {
      "Powerstock": {
        "NomeSistema": "Powerstock",
        "CorTexto": "#194888",
        "UrlFundo": "https://zendarappdev.blob.core.windows.net/onboarding/email/fundo/powerstock.jpg",
        "UrlLogo": "https://zendarappdev.blob.core.windows.net/onboarding/email/logos/powerstock.png",
        "UrlBanner": "https://zendarappdev.blob.core.windows.net/onboarding/email/banner/powerstock.png",
        "CorDivisores": "#90CDF4",
        "IconePrimeiroAcesso": "https://zendarappdev.blob.core.windows.net/onboarding/email/icones/primeiro-acesso-powerstock.png",
        "IconeTreinamentoUso": "https://zendarappdev.blob.core.windows.net/onboarding/email/icones/treinamento-powerstock.png",
        "IconeCentralAjuda": "https://zendarappdev.blob.core.windows.net/onboarding/email/icones/central-ajuda-powerstock.png",
        "IconeDicasUso": "https://zendarappdev.blob.core.windows.net/onboarding/email/icones/dicas-uso-powerstock.png"
      },
      "Zendar": {
        "NomeSistema": "Zendar",
        "CorTexto": "#5502B2",
        "UrlFundo": "https://zendarappdev.blob.core.windows.net/onboarding/email/fundo/zendar.jpg",
        "UrlLogo": "https://zendarappdev.blob.core.windows.net/onboarding/email/logos/zendar.png",
        "UrlBanner": "https://zendarappdev.blob.core.windows.net/onboarding/email/banner/zendar.png",
        "CorDivisores": "#D1BFF6",
        "IconePrimeiroAcesso": "https://zendarappdev.blob.core.windows.net/onboarding/email/icones/primeiro-acesso-zendar.png",
        "IconeTreinamentoUso": "https://zendarappdev.blob.core.windows.net/onboarding/email/icones/treinamento-zendar.png",
        "IconeCentralAjuda": "https://zendarappdev.blob.core.windows.net/onboarding/email/icones/central-ajuda-zendar.png",
        "IconeDicasUso": "https://zendarappdev.blob.core.windows.net/onboarding/email/icones/dicas-uso-zendar.png"
      },
      "Fomer": {
        "NomeSistema": "Fomer",
        "CorTexto": "#FF005A",
        "UrlFundo": "https://zendarappdev.blob.core.windows.net/onboarding/email/fundo/fomer.jpg",
        "UrlLogo": "https://zendarappdev.blob.core.windows.net/onboarding/email/logos/fomer.png",
        "UrlBanner": "https://zendarappdev.blob.core.windows.net/onboarding/email/banner/fomer.png",
        "CorDivisores": "#FF84AC",
        "IconePrimeiroAcesso": "https://zendarappdev.blob.core.windows.net/onboarding/email/icones/primeiro-acesso-fomer.png",
        "IconeTreinamentoUso": "https://zendarappdev.blob.core.windows.net/onboarding/email/icones/treinamento-fomer.png",
        "IconeCentralAjuda": "https://zendarappdev.blob.core.windows.net/onboarding/email/icones/central-ajuda-fomer.png",
        "IconeDicasUso": "https://zendarappdev.blob.core.windows.net/onboarding/email/icones/dicas-uso-fomer.png",
        "FaleComEspecialista": "https://zendarappdev.blob.core.windows.net/onboarding/email/icones/especialista-fomer.png"
      }
    }
  },
  "AllowedHosts": "*",
  "ServiceBusSettings": {
    "DefaultConnection": "Endpoint=sb://sb-zendar-dev.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=5XClu79yk4mibM88xXcFNbXw5kTh298mw+ASbIyTRsg="
  },
  "MOVIDESK_TOKEN_API": "309afd53-b7d3-437c-ba7c-cdb169dda796",
  "SmartPOSSettings": {
    "EmailsParceiroStone": "<EMAIL>"
  },
  "ApiKey": "5e68c70f-b296-47f5-b7ca-bfd9cea9a159",
  "ZendarSyncApi": {
    "TrayUrl": "https://zendar-sync-tray-dev-api.azurewebsites.net/api",
    "PdvAutonomoUrl": "https://zendar-sync-pdv-dev-api.azurewebsites.net/api",
    "FrenteCaixaUrl": "https://zendar-sync-frente-caixa-dev-api.azurewebsites.net/api"
  },
  "ApplicationInsights": {
    "InstrumentationKey": ""
  },
  "ZoopSettingsAPI": {
    "MarketplaceId": "64f1d2019f9045899cc745d21f36089e",
    "SplitRecipientId": "fed3a41252bb408c954ebb29e7ed7cca",
    "Authorization": "Basic enBrX3Byb2RfQkc1SDFVOGdBeURSc3ZKQlQ4M1BDM2xJOg==",
    "UrlApi": "https://api.zoop.ws/v1/marketplaces"
  },
  "RedisSettings": {
    "Endpoint": "redis-14634.c266.us-east-1-3.ec2.redns.redis-cloud.com",
    "Port": "14634",
    "Password": "3gvprhtyRKHjjMGc5wY05cJCPSSz1FOH",
    "Configuration": "redis-14634.c266.us-east-1-3.ec2.redns.redis-cloud.com:14634,password=3gvprhtyRKHjjMGc5wY05cJCPSSz1FOH,ssl=True,abortConnect=False", //public endpoint
    "InstanceName": "Zendar-Dev"
  }
}